<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hishop.himall</groupId>
        <artifactId>himall-base</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>seashop-base-common</artifactId>
    <packaging>jar</packaging>


    <dependencies>
        <dependency>
            <groupId>com.sankuai.thh.express</groupId>
            <artifactId>sc-express-api</artifactId>
            <version>1.0.69</version>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-util</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hishop.starter</groupId>
            <artifactId>hishop-sms-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hishop.starter</groupId>
            <artifactId>hishop-logistics-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.hishop.starter</groupId>-->
<!--            <artifactId>hishop-xxl-job-client-boot-starter</artifactId>-->
<!--            <version>2.0.1-SNAPSHOT</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.hishop.himall</groupId>-->
<!--            <artifactId>seashop-product-api</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.hishop.himall</groupId>-->
<!--            <artifactId>seashop-promotion-api</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.hishop.himall</groupId>-->
<!--            <artifactId>seashop-pay-api</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-order-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-trade-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-afs</artifactId>
        </dependency>
        <dependency>
            <groupId>org.finance.keykaiser</groupId>
            <artifactId>keykaiser</artifactId>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>html2pdf</artifactId>
        </dependency>
    </dependencies>

</project>
