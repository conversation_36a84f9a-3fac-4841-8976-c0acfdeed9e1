package com.sankuai.shangou.seashop.base.core.task;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hishop.xxljob.client.boot.annotation.XxlRegister;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.core.service.RegionService;
import com.sankuai.shangou.seashop.base.core.service.SiteSettingService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegion;
import com.sankuai.shangou.seashop.base.s3plus.S3plusStorageService;
import com.sankuai.shangou.seashop.base.thrift.core.SiteSettingQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.dto.GdRegionDto;
import com.sankuai.shangou.seashop.base.thrift.core.dto.GdRegionItem;
import com.sankuai.shangou.seashop.base.thrift.core.dto.RegionIdDto;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 基础服务 地区相关定时任务模块
 *
 * <AUTHOR>
 * @since 2025/7/17
 */
@Slf4j
@Component
@AllArgsConstructor
@SuppressWarnings("all")
public class RegionTask {

    private final SiteSettingService siteSettingService;
    private final S3plusStorageService s3plusStorageService;
    private final RegionService regionService;

    @XxlJob("syncRegionData")
    @XxlRegister(cron = "0 0 4 * * ?", author = "snow", jobDesc = "同步区域数据")
    public ResultDto<Boolean> syncRegionData() {
        return ThriftResponseHelper.responseInvoke("initData", NumberUtils.INTEGER_ZERO, req -> {
            List<GdRegionItem> treeList = this.getAllRegionWithTree();
            List<GdRegionItem> targetList = new ArrayList<>();
            // 把tree类型地区平铺
            RegionIdDto idDto = new RegionIdDto(0L);
            treeList.forEach(region -> {
                idDto.updateValue();
                region.setId(idDto.getValue());
                region.setParentId(NumberUtils.LONG_ZERO);
                fillRegion(targetList, region, NumberUtils.INTEGER_ONE, idDto);
            });
            List<BaseRegion> regions = targetList.stream().map(region -> {
                BaseRegion baseRegion = new BaseRegion();
                baseRegion.setId(region.getId());
                baseRegion.setRegionLevel(region.getRegionLevel());
                baseRegion.setCode(region.getAdcode());
                baseRegion.setParentId(region.getParentId());
                baseRegion.setName(region.getName());
                baseRegion.setStatus(NumberUtils.INTEGER_ZERO);
                baseRegion.setShortName(region.getName());
                return baseRegion;
            }).collect(Collectors.toList());
            return this.regionService.saveOrUpdateBatch(regions);
        });
    }

    /**
     * 获取所有地区 tree类型
     */
    private List<GdRegionItem> getAllRegionWithTree() {
        String jDRegionAppKey = this.siteSettingService.querySettingsValueByKey("jDRegionAppKey");
        if (StringUtils.isNotBlank(jDRegionAppKey)) {
            throw new BusinessException("请先配置高德appKey");
        }
        String dataPath = "https://restapi.amap.com/v3/config/district?key=" + jDRegionAppKey + "&subdistrict=4&keywords=100000";
        try {
            InputStream stream = s3plusStorageService.readExcelFromOnlineUrl(dataPath);
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            GdRegionDto region = objectMapper.readValue(stream, new TypeReference<GdRegionDto>() {
            });
            return region.getDistricts().get(0).getDistricts();

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 平铺地区
     *
     * @param list   最终返回的地区数据
     * @param parent 父级地区
     * @param level  地区级别
     */
    private void fillRegion(List<GdRegionItem> list, GdRegionItem parent, int level, RegionIdDto idDto) {
        parent.setRegionLevel(level);
        list.add(parent);
        level++;
        if (parent.getDistricts() == null || parent.getDistricts().isEmpty()) {
            return;
        }
        for (GdRegionItem subRegion : parent.getDistricts()) {
            idDto.updateValue();
            subRegion.setId(idDto.getValue());
            subRegion.setParentAdcode(parent.getAdcode());
            long partentId = parent.getId();
            subRegion.setParentId(partentId);
            fillRegion(list, subRegion, level, idDto);
        }
    }

}
