package com.sankuai.shangou.seashop.base.core.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegion;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseRegionReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseUpdateRegionReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.RegionIdsReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseIdsReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.AllPathRegionResp;

public interface RegionService extends IService<BaseRegion> {

    /**
     * 添加地区
     *
     * @param region
     * @return
     */
    Long create(BaseRegion region);

    Long createNoId(BaseRegion region);

    /**
     * 根据父id 获取子地区
     *
     * @param parentId 父级id  为0的话 则查询顶级区域
     * @return
     */
    List<BaseRegion> getRegionByParentId(long parentId);


    /**
     * 根据短名称以及level获取地区对象
     *
     * @param shortName 地区简称
     * @param level     级别
     * @return 地区对象
     */
    BaseRegion getRegionByShortNameAndLevel(String shortName, int level);


    /**
     * 获取系统内所有地址信息
     *
     * @return
     */
    List<BaseRegion> getAllRegions();

    /**
     * 获取系统内高于这个级别的所有地址信息
     *
     * @param level
     * @return
     */
    List<BaseRegion> getAllRegions(int level);

    Boolean update(BaseUpdateRegionReq dbRegion);

    Boolean update(BaseRegion dbRegion);

    /**
     * 根据地区id 获取所有上级地区和自己
     *
     * @param id
     * @return
     */
    List<BaseRegion> getParentRegions(Long id);

    /**
     * 根据地区code获取所有上级地区和自己
     *
     * @param code 地区编码
     * @return 往上找的地区列表
     */
    List<BaseRegion> getParentRegionsByCode(String code);

    /**
     * 根据地区id 获取所有下级地区和自己
     *
     * @param id
     * @return
     */
    List<BaseRegion> getSubRegions(Long id);

    /**
     * 根据地区id 获取一整条树的地区
     *
     * @param id
     * @return
     */
    List<BaseRegion> getTrackRegionsById(Long id);

    /**
     * 根据地区id集合获取地区的全路径
     *
     * @param regionIdsReq
     * @return
     */
    Map<String, AllPathRegionResp> getAllPathRegions(RegionIdsReq regionIdsReq);

    Long createRegion(BaseRegionReq regionReq);

    Boolean deleteRegion(BaseIdsReq req);

    Boolean deleteByCustom();
}
