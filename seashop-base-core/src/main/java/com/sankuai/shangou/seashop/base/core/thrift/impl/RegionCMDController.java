package com.sankuai.shangou.seashop.base.core.thrift.impl;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.sankuai.shangou.seashop.base.core.service.SiteSettingService;
import com.sankuai.shangou.seashop.base.thrift.core.SiteSettingQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.dto.*;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.RegionService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegion;
import com.sankuai.shangou.seashop.base.lock.DistributedLockService;
import com.sankuai.shangou.seashop.base.lock.model.LockKey;
import com.sankuai.shangou.seashop.base.s3plus.S3plusStorageService;
import com.sankuai.shangou.seashop.base.thrift.core.RegionCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseRegionReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseUpdateRegionReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseIdsReq;

import cn.hutool.core.util.StrUtil;

@RestController
@RequestMapping("/region")
public class RegionCMDController implements RegionCMDFeign {
    @Resource
    private DistributedLockService distributedLockService;
    @Resource
    private S3plusStorageService s3plusStorageService;
    @Value("${hishop.storage.domain}")
    private String hostName;
    @Value("${hishop.storage.base-path}")
    private String bucketName;

    @Resource
    private RegionService regionService;

    @Resource
    private SiteSettingService siteSettingService;

    @GetMapping(value = "/initData")
    @Override
    public ResultDto<Boolean> initData() {
        return ThriftResponseHelper.responseInvoke("initData", NumberUtils.INTEGER_ZERO, req -> {
            List<GdRegionItem> treeList = getAllRegionWithTree();
            List<GdRegionItem> targetList = new ArrayList<>();
            // 把tree类型地区平铺
            RegionIdDto idDto = new RegionIdDto(0L);
            treeList.forEach(region -> {
                idDto.updateValue();
                region.setId(idDto.getValue());
                region.setParentId(NumberUtils.LONG_ZERO);
                fillRegion(targetList, region, NumberUtils.INTEGER_ONE, idDto);
            });
            List<BaseRegion> regions = targetList.stream().map(region -> {
                BaseRegion baseRegion = new BaseRegion();
                baseRegion.setId(region.getId());
                baseRegion.setRegionLevel(region.getRegionLevel());
                baseRegion.setCode(region.getAdcode());
                baseRegion.setParentId(region.getParentId());
                baseRegion.setName(region.getName());
                baseRegion.setStatus(NumberUtils.INTEGER_ZERO);
                baseRegion.setShortName(region.getName());
                return baseRegion;
            }).collect(Collectors.toList());
            return this.regionService.saveOrUpdateBatch(regions);
        });
    }

    @GetMapping(value = "/initHishopData")
    @Override
    public ResultDto<Boolean> initHishopData() throws TException {
        List<HishopRegionDto> treeList = getHishopAllRegionWithTree();
        return ThriftResponseHelper.responseInvoke("initData", "i", req -> {
            for (HishopRegionDto region : treeList) {
                BaseRegion regionDao = new BaseRegion();
                regionDao.setId(region.getId());
                regionDao.setRegionLevel(region.getRegion_level());
                regionDao.setCode(region.getCode());
                regionDao.setParentId(region.getParent_id());
                regionDao.setName(region.getName());
                regionDao.setStatus(0);
                regionDao.setShortName(region.getShort_name());
                regionDao.setLeft(region.getLeft());
                regionDao.setRight(region.getRight());
                try {
                    regionService.createNoId(regionDao);
                } catch (Exception ex) {
                    continue;
                }

            }
            return true;
        });
    }


    @GetMapping(value = "/SynMTRegionCode")
    @Override
    public ResultDto<Boolean> SynMTRegionCode() throws TException {
        int i = 0;
        return ThriftResponseHelper.responseInvoke("SynMTRegionCode", 0, req -> {
            // todo: 这里需要设计任务阻塞式调用， 确保全局一个线程 ，防止过多的请求浪费计算资源
            List<MTRegionDto> mtRegions = getMTRegionWithTree();
            List<MTRegionDto> targetList = new ArrayList<>();

            List<BaseRegion> sourceRegions = regionService.getAllRegions();
            for (MTRegionDto region : mtRegions) {
                fillMTRegion(targetList, region);
            }

            for (MTRegionDto region : targetList) {
                String shortName = MTTitleConvertShortName(region.getTitle());
                if (StrUtil.isEmpty(shortName)) {
                    continue;
                }
                if (!sourceRegions.stream().filter(t -> t != null && t.getShortName() != null && t.getRegionLevel() != null && t.getShortName().contains(shortName) && t.getRegionLevel().equals(region.getLevel())).findFirst().isPresent()) {
                    continue;
                }
                BaseRegion dbRegion = sourceRegions.stream().filter(t -> t != null && t.getShortName() != null && t.getRegionLevel() != null && t.getShortName().contains(shortName) && t.getRegionLevel().equals(region.getLevel())).findFirst().get();

                // 匹配的上编码 直接跳过
                if (dbRegion.getCode() != null && dbRegion.getCode().equals(region.getId())) {
                    continue;
                }
                // 设置美团编码到对象上
                dbRegion.setCode(region.getId());
                regionService.update(dbRegion);
            }
            return true;

        });


    }

    @PostMapping(value = "/createRegion", consumes = "application/json")
    @Override
    public ResultDto<Long> createRegion(@RequestBody BaseRegionReq regionReq) throws TException {

        return ThriftResponseHelper.responseInvoke("createRegion", regionReq, req -> {
            regionReq.checkParameter();
            Long aLong = null;
            try {
                aLong = distributedLockService.tryLock(new LockKey("createRegion", "createRegion"), () -> {
                    Long id = regionService.createRegion(req);
                    return id;
                }, 5L, TimeUnit.SECONDS);
            } catch (Throwable e) {
                throw new BusinessException("添加地区失败");
            }
            if (aLong == null) {
                throw new BusinessException("添加地区失败");
            }
            return aLong;
        });

    }

    @PostMapping(value = "/updateRegion", consumes = "application/json")
    @Override
    public ResultDto<Boolean> updateRegion(@RequestBody BaseUpdateRegionReq regionReq) throws TException {
        regionReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("updateRegion", regionReq, req -> {
            distributedLockService.tryLock(new LockKey("createRegion", "createRegion"), () -> {
                regionService.update(req);

            });
            return true;
        });
    }

    @PostMapping(value = "/deleteRegion", consumes = "application/json")
    @Override
    public ResultDto<Boolean> deleteRegion(@RequestBody BaseIdsReq idsReq) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteRegion", idsReq, req -> {
            Boolean result = regionService.deleteRegion(req);
            return result;
        });
    }

    @GetMapping(value = "/reviewRegion")
    @Override
    public ResultDto<Boolean> reviewRegion() throws TException {
        return ThriftResponseHelper.responseInvoke("deleteRegion", null, req -> {
            Boolean result = regionService.deleteByCustom();
            return result;
        });
    }


    private String MTTitleConvertShortName(String title) {
        String result = title;
        result = result.replace("特别行政区", "");
        result = result.replace("省", "");
        result = result.replace("维吾尔", "");
        result = result.replace("回族", "");
        result = result.replace("壮族", "");
        result = result.replace("自治区", "");
        result = result.replace("市", "");
        result = result.replace("盟", "");
        result = result.replace("林区", "");
        result = result.replace("地区", "");
        result = result.replace("土家族", "");
        result = result.replace("苗族", "");
        result = result.replace("回族", "");
        result = result.replace("黎族", "");
        result = result.replace("藏族", "");
        result = result.replace("傣族", "");
        result = result.replace("彝族", "");
        result = result.replace("哈尼族", "");
        result = result.replace("壮族", "");
        result = result.replace("白族", "");
        result = result.replace("景颇族", "");
        result = result.replace("傈僳族", "");
        result = result.replace("朝鲜族", "");
        result = result.replace("蒙古", "");
        result = result.replace("哈萨克", "");
        result = result.replace("柯尔克孜", "");
        result = result.replace("自治州", "");
        result = result.replace("自治县", "");
        result = result.replace("县", "");
        result = result.replace("区", "");
        return result;
    }

    /**
     * 平铺地区
     *
     * @param list   最终返回的地区数据
     * @param parent 父级地区
     * @param level  地区级别
     */
    private void fillRegion(List<GdRegionItem> list, GdRegionItem parent, int level, RegionIdDto idDto) {
        parent.setRegionLevel(level);
        list.add(parent);
        level++;
        if (parent.getDistricts() == null || parent.getDistricts().size() < 1) {
            return;
        }
        for (GdRegionItem subRegion : parent.getDistricts()) {
            idDto.updateValue();
            subRegion.setId(idDto.getValue());
            subRegion.setParentAdcode(parent.getAdcode());
            long partentId = parent.getId();
            subRegion.setParentId(partentId);
            fillRegion(list, subRegion, level, idDto);
        }
    }


    private void fillMTRegion(List<MTRegionDto> list, MTRegionDto parent) {
        list.add(parent);
        if (parent.getChildren() == null || parent.getChildren().size() < 1) {
            return;
        }
        for (MTRegionDto subRegion : parent.getChildren()) {

            fillMTRegion(list, subRegion);
        }
    }

    /**
     * 获取所有地区 tree类型
     *
     * @return
     */
    private List<GdRegionItem> getAllRegionWithTree() {
        String jDRegionAppKey = this.siteSettingService.querySettingsValueByKey("jDRegionAppKey");
        if (StringUtils.isNotBlank(jDRegionAppKey)) {
            throw new BusinessException("请先配置高德appKey");
        }
        String dataPath = "https://restapi.amap.com/v3/config/district?key=" + jDRegionAppKey + "&subdistrict=4&keywords=100000";
        try {
            InputStream stream = s3plusStorageService.readExcelFromOnlineUrl(dataPath);
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            GdRegionDto region = objectMapper.readValue(stream, new TypeReference<GdRegionDto>() {
            });
            return region.getDistricts().get(0).getDistricts();

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private List<HishopRegionDto> getHishopAllRegionWithTree() {
        String dataPath = hostName + bucketName + "/system/region.json";
        try {
            InputStream stream = s3plusStorageService.readExcelFromOnlineUrl(dataPath);
//            ArrayList list = (ArrayList) JsonUtil.parseArray(stream, ArrayList.class);
            ObjectMapper objectMapper = new ObjectMapper();
            List<HishopRegionDto> list = objectMapper.readValue(stream, new TypeReference<List<HishopRegionDto>>() {
            });
            return list;

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取类团地址库
     *
     * @return
     */
    private List<MTRegionDto> getMTRegionWithTree() {
        String dataPath = hostName + "system/region/MTRegion.json";
        try {
            InputStream stream = s3plusStorageService.readExcelFromOnlineUrl(dataPath);
            MTRegionResult result = JsonUtil.parseObject(stream, MTRegionResult.class);
            if (result.getCode() == 0) {
                return result.getData();
            }
            return new ArrayList<>();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
