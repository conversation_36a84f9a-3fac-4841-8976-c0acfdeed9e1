package com.sankuai.shangou.seashop.base.core.service;

import com.github.pagehelper.Page;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseAgreement;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomForm;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSettled;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSetting;
import com.sankuai.shangou.seashop.base.thrift.core.request.*;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseIdsReq;

import com.sankuai.shangou.seashop.base.thrift.core.response.AppSitSettingRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseAllAgreementRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseShopSitSettingRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.ProductSettingResp;

import com.sankuai.shangou.seashop.base.thrift.core.response.*;


import java.util.List;

public interface SiteSettingService {
    Boolean saveSettings(List<BaseSiteSetting> settings);

    Boolean saveAppSettings(BaseSiteSettingReq req);

    Boolean saveMsgTemplateApplet(MsgTemplateAppletReq req);

    Boolean updateSettingsByKey(List<BaseSiteSetting> settings);

    List<BaseSiteSetting> query(List<String> keys);


    /**
     * 保存入驻设置
     *
     * @param settled 入驻设置对象
     * @return
     */
    Boolean saveSettled(BaseSettledReq settled);

    /**
     * 获取入驻设置
     *
     * @return 入驻设置对象
     */
    BaseSettled getSettled();

    BaseAgreement getAgreement(int type);

    BaseAllAgreementRes getAgreements();

    Boolean saveAgreement(BaseAgreement agreement);

    Long createCustomForm(BaseCustomFormReq customForm);

    Boolean updateCustomForm(BaseCustomFormReq customForm);

    Boolean deletesCustomForm(BaseIdsReq baseIdsReq);

    Boolean existCustomFrom(String name);

    Page<BaseCustomForm> queryCustomFormWithPage(BaseCustFormQueryReq queryReq);

    BaseCustomForm getCustomForm(long formId);

    Boolean saveShopSettings(BaseShopSitSettingReq settingReq);

    BaseShopSitSettingRes getShopSettings();

    AppSitSettingRes getAppSettings();

    void saveProductSettings(SaveProductSettingReq req);

    ProductSettingResp getProductSettings();

    Boolean saveAllAgreement(BaseAllAgreementReq req);

    String querySettingsValueByKey(String key);

    SmsSettingRes querySmsSetting();

    BaseResp saveSmsSetting(SmsSettingReq req);

    BaseResp saveShopStyle(SystemStyleReq fun1);

    String queryShopStyle(String key);

    BaseResp saveExpressConfig(ExpressConfigReq fun1);

    ExpressConfigRes queryExpressConfig();
}
