package com.sankuai.shangou.seashop.base.core.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.RegionService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegion;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegionExample;
import com.sankuai.shangou.seashop.base.dao.core.repository.BaseRegionRepository;
import com.sankuai.shangou.seashop.base.thrift.core.enums.RegionLevelEnum;
import com.sankuai.shangou.seashop.base.thrift.core.enums.RegionStatusEnum;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseRegionReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseUpdateRegionReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.RegionIdsReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseIdsReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.AllPathRegionResp;

import cn.hutool.core.collection.CollectionUtil;

@Service
public class RegionServiceImpl extends ServiceImpl<BaseMapper<BaseRegion>, BaseRegion> implements RegionService {
    @Resource
    private BaseRegionRepository repository;

    @Override
    public Long create(BaseRegion region) {
        long id = repository.getMaxId() + 1;
        region.setId(id);
        repository.create(region);
        return id;
    }

    @Override
    public Long createNoId(BaseRegion region) {

        repository.create(region);
        return region.getId();
    }

    @Override
    public List<BaseRegion> getRegionByParentId(long parentId) {

        List<BaseRegion> regions = repository.getRegionByParentId(parentId);
        return regions;
    }

    @Override
    public BaseRegion getRegionByShortNameAndLevel(String shortName, int level) {
        BaseRegion region = repository.getRegionByShortNameAndLevel(shortName, level);
        return region;
    }

    @Override
    public List<BaseRegion> getAllRegions() {
        List<BaseRegion> regions = repository.getAllRegions();
        return regions;
    }

    @Override
    public List<BaseRegion> getAllRegions(int level) {
        List<BaseRegion> regions = repository.getAllRegions(level);
        return regions;
    }

    @Override
    public Boolean update(BaseUpdateRegionReq updateRegion) {
        BaseRegion region = new BaseRegion();
        region.setId(updateRegion.getId());
        region.setName(updateRegion.getName());
        repository.update(region);
        return true;
    }

    @Override
    public Boolean update(BaseRegion dbRegion) {
        repository.update(dbRegion);
        return true;
    }

    @Override
    public List<BaseRegion> getParentRegions(Long id) {
        if (id == null || id <= 0) {
            return Collections.EMPTY_LIST;
        }

        BaseRegion region = repository.getById(id);
        if (region == null) {
            return new ArrayList<>();
        }
        BaseRegionExample example = new BaseRegionExample();
        BaseRegionExample.Criteria criteria = example.createCriteria();
        criteria.andLeftLessThanOrEqualTo(region.getLeft());
        criteria.andRightGreaterThanOrEqualTo(region.getRight());
        criteria.andStatusEqualTo(0);
        example.setOrderByClause(" region_level asc ");
        List<BaseRegion> list = repository.getRegions(example);
        return list;
    }

    @Override
    public List<BaseRegion> getParentRegionsByCode(String code) {
        BaseRegionExample queryByCode = new BaseRegionExample();
        queryByCode.setOrderByClause("id desc");
        BaseRegionExample.Criteria queryByCodeCri = queryByCode.createCriteria();
        queryByCodeCri.andCodeEqualTo(code);
        List<BaseRegion> regions = repository.getRegions(queryByCode);
        if (CollectionUtil.isEmpty(regions)) {
            return Collections.emptyList();
        }
        BaseRegion region = regions.get(0);
        BaseRegionExample example = new BaseRegionExample();
        BaseRegionExample.Criteria criteria = example.createCriteria();
        criteria.andLeftLessThanOrEqualTo(region.getLeft());
        criteria.andRightGreaterThanOrEqualTo(region.getRight());
        criteria.andStatusEqualTo(0);
        example.setOrderByClause(" region_level asc ");
        return repository.getRegions(example);
    }

    @Override
    public List<BaseRegion> getSubRegions(Long id) {
        BaseRegion region = repository.getById(id);
        if (region == null) {
            return new ArrayList<>();
        }
        BaseRegionExample example = new BaseRegionExample();
        BaseRegionExample.Criteria criteria = example.createCriteria();
        criteria.andLeftGreaterThanOrEqualTo(region.getLeft());
        criteria.andRightLessThanOrEqualTo(region.getRight());
        criteria.andStatusEqualTo(0);
        example.setOrderByClause(" region_level asc ");
        List<BaseRegion> list = repository.getRegions(example);
        return list;
    }

    @Override
    public List<BaseRegion> getTrackRegionsById(Long id) {
        BaseRegion region = repository.getById(id);
        if (region == null) {
            return new ArrayList<>();
        }

        // 查子节点
        BaseRegionExample example = new BaseRegionExample();
        BaseRegionExample.Criteria criteria = example.createCriteria();
        criteria.andLeftGreaterThanOrEqualTo(region.getLeft());
        criteria.andRightLessThanOrEqualTo(region.getRight());
        criteria.andStatusEqualTo(0);
        List<BaseRegion> subList = repository.getRegions(example);


        // 查父节点
        BaseRegionExample exampleParent = new BaseRegionExample();
        BaseRegionExample.Criteria criteriaParent = exampleParent.createCriteria();
        criteriaParent.andLeftLessThan(region.getLeft());
        criteriaParent.andRightGreaterThan(region.getRight());
        criteriaParent.andStatusEqualTo(0);
        List<BaseRegion> list = repository.getRegions(exampleParent);
        list.addAll(subList);

        return list;
    }

    // 根据regionId获取这些地区的全路径
    @Override
    public Map<String, AllPathRegionResp> getAllPathRegions(RegionIdsReq regionIdsReq) {
        Map<String, AllPathRegionResp> result = new HashMap<>();
        if (!CollectionUtils.isEmpty(regionIdsReq.getRegionIds())) {
            List<BaseRegion> baseRegionList = repository.getAllPathRegions(regionIdsReq.getRegionIds());
            // 将list转换成map
            Map<Long, BaseRegion> maps = baseRegionList.stream().collect(Collectors.toMap(BaseRegion::getId, Function.identity(), (key1, key2) -> key2));
            // 遍历maps
            maps.entrySet().forEach(entry -> {
                String key = entry.getKey().toString();
                List<BaseRegion> allRegionPath = this.getParentRegions(entry.getKey());
                AllPathRegionResp allPathRegionResp = new AllPathRegionResp();
                if (CollectionUtils.isEmpty(allRegionPath)) {
                    return;
                }
                // 1.省 2.市 3.区 4.街道
                allRegionPath.stream().filter(v -> v.getRegionLevel() == RegionLevelEnum.Province.getCode()).findFirst().ifPresent(v -> {
                    allPathRegionResp.setProvinceId(v.getId());
                    allPathRegionResp.setProvinceName(v.getName());
                });
                allRegionPath.stream().filter(v -> v.getRegionLevel() == RegionLevelEnum.City.getCode()).findFirst().ifPresent(v -> {
                    allPathRegionResp.setCityId(v.getId());
                    allPathRegionResp.setCityName(v.getName());
                });
                allRegionPath.stream().filter(v -> v.getRegionLevel() == RegionLevelEnum.County.getCode()).findFirst().ifPresent(v -> {
                    allPathRegionResp.setCountyId(v.getId());
                    allPathRegionResp.setCountyName(v.getName());
                });
                allRegionPath.stream().filter(v -> v.getRegionLevel() == RegionLevelEnum.Town.getCode()).findFirst().ifPresent(v -> {
                    allPathRegionResp.setTownIds(v.getId().toString());
                    allPathRegionResp.setTownsNames(v.getName());
                });
                result.put(key, allPathRegionResp);
            });
        }
        // 特殊处理那些用逗号隔开传进来的乡镇
        if (!CollectionUtils.isEmpty(regionIdsReq.getTownIds())) {
            for (String townIds : regionIdsReq.getTownIds()) {
                List<String> townIdArr = Arrays.asList(townIds.split(","));
                // 这些特殊的逗号隔开的乡镇，有个共性是归属于同一个省市区下
                // 所以只需要取第一个乡镇的省市区就可以了
                List<BaseRegion> allRegionPath = this.getParentRegions(Long.parseLong(townIdArr.get(0)));
                AllPathRegionResp allPathRegionResp = new AllPathRegionResp();
                // 1.省 2.市 3.区 4.街道
                allRegionPath.stream().filter(v -> v.getRegionLevel() == RegionLevelEnum.Province.getCode()).findFirst().ifPresent(v -> {
                    allPathRegionResp.setProvinceId(v.getId());
                    allPathRegionResp.setProvinceName(v.getName());
                });
                allRegionPath.stream().filter(v -> v.getRegionLevel() == RegionLevelEnum.City.getCode()).findFirst().ifPresent(v -> {
                    allPathRegionResp.setCityId(v.getId());
                    allPathRegionResp.setCityName(v.getName());
                });
                allRegionPath.stream().filter(v -> v.getRegionLevel() == RegionLevelEnum.County.getCode()).findFirst().ifPresent(v -> {
                    allPathRegionResp.setCountyId(v.getId());
                    allPathRegionResp.setCountyName(v.getName());
                });
                allPathRegionResp.setTownIds(townIds);
                allPathRegionResp.setTownsNames(repository.getTownsNameByIds(townIdArr).stream().collect(Collectors.joining(",")));
                result.put(townIds, allPathRegionResp);
            }
        }
        return result;
    }

    @Override
    @Transactional
    public Long createRegion(BaseRegionReq regionReq) {
        BaseRegion region = JsonUtil.copy(regionReq, BaseRegion.class);
        region.setCustom(true);
        region.setStatus(0);
        int left = 0;
        int right = 0;
        int level = 0;
        if (!region.getParentId().equals(0L)) {
            BaseRegion parentRegion = repository.getById(region.getParentId());
            AssertUtil.throwIfNull(parentRegion, "找不到父级地址");
            left = parentRegion.getLeft();
            right = parentRegion.getRight();
            level = parentRegion.getRegionLevel() + 1;
        } else {
            right = (int) repository.getMaxRight();
            left = 1;
            level = 1;
        }

        // 新增一个节点 左右值腾位
        BaseRegionExample insertExample = new BaseRegionExample();
        BaseRegionExample.Criteria insertCriteria = insertExample.createCriteria();
        insertCriteria.andLeftGreaterThan(right);

        //新增的节点 左右值初始化  左值:父级的右值  右值：左值加1
        repository.updateParentLeftAndRightByInsert(insertExample);
        region.setRegionLevel(level);
        region.setLeft(right);
        region.setRight(right + 1);
        long id = repository.getMaxId() + 1;
        region.setId(id);
        // 插入新地区
        repository.create(region);
        //新增节点的所有父级 右值+2
        BaseRegionExample parentExample = new BaseRegionExample();
        BaseRegionExample.Criteria parentExampleCriteria = parentExample.createCriteria();
        parentExampleCriteria.andRightGreaterThanOrEqualTo(right);
        parentExampleCriteria.andLeftLessThanOrEqualTo(left);
        repository.updateParentLeftAndRightByInsert(parentExample);
        return id;
    }

    @Override
    public Boolean deleteRegion(BaseIdsReq req) {
        BaseRegion region = new BaseRegion();
        region.setStatus(RegionStatusEnum.Delete.getCode());
        BaseRegionExample example = new BaseRegionExample();
        BaseRegionExample.Criteria insertCriteria = example.createCriteria();
        insertCriteria.andIdIn(req.getIds());
        boolean result = repository.update(region, example);
        return result;
    }

    @Override
    public Boolean deleteByCustom() {
        BaseRegionExample example = new BaseRegionExample();
        BaseRegionExample.Criteria insertCriteria = example.createCriteria();
        insertCriteria.andCustomEqualTo(true);
        boolean result = repository.delete(example);
        return result;
    }
}
