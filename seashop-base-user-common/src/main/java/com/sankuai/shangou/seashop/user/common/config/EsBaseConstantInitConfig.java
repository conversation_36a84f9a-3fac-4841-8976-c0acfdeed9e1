package com.sankuai.shangou.seashop.user.common.config;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.user.common.constant.EsConstant;

/**
 * <AUTHOR>
 * @date 2024/04/25 17:15
 */
@Component
public class EsBaseConstantInitConfig implements InitializingBean {


    @Value("${es.index.shop:shop_index}")
    private String idxShop;
    @Value("${spring.profiles.active}")
    private String indexPrefix;

    @Override
    public void afterPropertiesSet() {
        EsConstant.INDEX_SHOP = indexPrefix + "." + idxShop;
    }

}
