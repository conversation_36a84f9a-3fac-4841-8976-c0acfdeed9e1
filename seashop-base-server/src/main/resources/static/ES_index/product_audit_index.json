{"mappings": {"properties": {"addedTime": {"type": "long"}, "allImagePath": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "auditReason": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "auditStatus": {"type": "long"}, "brandId": {"type": "long"}, "categoryId": {"type": "long"}, "categoryPath": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "createTimeStamp": {"type": "long"}, "displaySequence": {"type": "long"}, "freightTemplateId": {"type": "long"}, "hasSku": {"type": "boolean"}, "id": {"type": "long"}, "mainImagePath": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "marketPrice": {"type": "float"}, "maxBuyCount": {"type": "long"}, "maxSalePrice": {"type": "float"}, "measureUnit": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "minSalePrice": {"type": "float"}, "multipleCount": {"type": "long"}, "onsaleTime": {"type": "long"}, "productCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "productId": {"type": "long"}, "productName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}, "analyzer": "ik_max_word", "search_analyzer": "ik_smart"}, "saleCount": {"type": "long"}, "saleCounts": {"type": "long"}, "saleStatus": {"type": "long"}, "shopCategoryIds": {"type": "long"}, "shopDisplaySequence": {"type": "long"}, "shopId": {"type": "long"}, "shortDescription": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "skuAutoIds": {"type": "long"}, "skuCodes": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "skuIds": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "source": {"type": "long"}, "spec1Alias": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "spec2Alias": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "spec3Alias": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "submitAuditTime": {"type": "long"}, "totalSaleCounts": {"type": "long"}, "updateTimeStamp": {"type": "long"}, "virtualSaleCounts": {"type": "long"}, "whetherDelete": {"type": "boolean"}, "whetherOpenLadder": {"type": "boolean"}}}, "settings": {"number_of_shards": "1", "number_of_replicas": "1"}}