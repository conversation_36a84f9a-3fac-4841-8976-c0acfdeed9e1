# web服务端口号
server:
  port: 8080

spring:
  application:
    name: himall-base
  profiles:
    active: develop_local
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  redis:
    database: 1
  cloud:
    nacos:
      server-addr: ${NACOS_SERVER:124.71.221.117:8848}
      username: ${NACOS_USERNAME:dev}
      password: ${NACOS_PASSWORD:dev@hishop}
      config:
        namespace: ${spring.profiles.active}
        group: 1.0.0
      discovery:
        namespace: ${spring.profiles.active}
        group: 1.0.0
  config:
    import:
      - optional:nacos:himall-common.yml
      - optional:nacos:${spring.application.name}.yml
mybatis:
  type-aliases-package: com.sankuai.shangou.seashop.base.dao.core.mapper
  mapper-locations: classpath:mapper/*.xml
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql
