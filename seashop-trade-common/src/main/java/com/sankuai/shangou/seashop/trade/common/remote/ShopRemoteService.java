package com.sankuai.shangou.seashop.trade.common.remote;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.common.remote.model.shop.RemoteShopBo;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopEsQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopInvoiceQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.*;
import com.sankuai.shangou.seashop.user.thrift.shop.response.*;
import com.sankuai.shangou.seashop.user.thrift.shop.response.es.UserInvisibleShopResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ShopRemoteService {

    @Resource
    private ShopQueryFeign shopQueryFeign;
    @Resource
    private ShopInvoiceQueryFeign shopInvoiceQueryFeign;
    @Resource
    private ShopEsQueryFeign shopEsQueryFeign;

    public ExclusiveShopResp queryShopWithExclusive(List<Long> shopIdList, Long userId) {
        log.info("【店铺】查询店铺信息并基于用户是否专享商家, shopIdList={}, userId={}", JsonUtil.toJsonString(shopIdList), userId);
        QueryShopPageReq req = new QueryShopPageReq();
        req.setShopIds(shopIdList);
        req.setUserId(userId);
        ExclusiveShopResp resp = new ExclusiveShopResp();
        //TODO 这里调用了base里不存在的接口，可能是base里删除了，这里没有删除，后续需要确认 2024-11-20
        //ExclusiveShopResp resp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.queryShopWithExclusive(req));
        log.info("【店铺】查询店铺信息并基于用户是否专享商家, resp={}", resp);
        return resp;
    }

    public RemoteShopBo queryShopById(Long shopId) {
        BaseIdReq req = new BaseIdReq();
        req.setId(shopId);
        log.info("【店铺】查询店铺信息, shopId={}", req);
        ShopDetailResp resp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.queryDetail(req));
        log.info("【店铺】查询店铺信息, resp={}", JsonUtil.toJsonString(resp));
        return JsonUtil.copy(resp, RemoteShopBo.class);
    }

    public List<ShopResp> queryShopByIds(List<Long> shopIds) {
        ShopQueryReq req = new ShopQueryReq();
        req.setShopIds(shopIds);
        log.info("【店铺】查询店铺信息, shopIds={}", JsonUtil.toJsonString(req));
        List<ShopResp> resp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.queryShopsByIds(req));
        log.info("【店铺】查询店铺信息, resp={}", JsonUtil.toJsonString(resp));
        return resp;
    }

    public String getShopName(Long shopId) {
        List<ShopResp> shopList = queryShopByIds(Arrays.asList(shopId));
        return CollectionUtils.isEmpty(shopList) ? null : shopList.get(0).getShopName();
    }


    public List<QueryShopInvoiceResp> queryShopInvoiceSetting(List<Long> shopIdList) {
        BatchQueryShopInvoiceReq req = new BatchQueryShopInvoiceReq();
        req.setShopIdList(shopIdList);
        log.info("【店铺】查询店铺发票信息, shopIdList={}", JsonUtil.toJsonString(req));
        QueryShopInvoiceListResp resp = ThriftResponseHelper.executeThriftCall(() -> shopInvoiceQueryFeign.batchQueryInvoiceSetting(req));
        log.info("【店铺】查询店铺发票信息, resp={}", JsonUtil.toJsonString(resp));
        if (resp == null) {
            return Collections.emptyList();
        }
        return resp.getInvoiceSettingList();
    }

    public Map<Long, QueryShopInvoiceResp> queryShopInvoiceSettingMap(List<Long> shopIdList) {
        List<QueryShopInvoiceResp> list = queryShopInvoiceSetting(shopIdList);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(QueryShopInvoiceResp::getShopId, v -> v, (v1, v2) -> v1));
    }

    /**
     * 获取当前登录用户可见店铺列表
     * <p>这里的不可见是指店铺状态以及综合考虑专属商家后的，基于全量店铺过滤出的结果</p>
     * <AUTHOR>
     * @param userId 登录用户ID
     */
    public List<Long> searchInvisibleShopFromEs(Long userId) {
        QueryUserInvisibleShopReq req = new QueryUserInvisibleShopReq();
        req.setUserId(userId);
        log.info("【店铺】查询当前用户不可见店铺, 请求参数: {}", JsonUtil.toJsonString(req));
        UserInvisibleShopResp resp = ThriftResponseHelper.executeThriftCall(() -> shopEsQueryFeign.getUserInvisibleShop(req));
        log.debug("【店铺】查询当前用户不可见店铺, resp: {}", JsonUtil.toJsonString(resp));
        if (resp == null || CollUtil.isEmpty(resp.getShopIdList())) {
            return Collections.emptyList();
        }
        return resp.getShopIdList();
    }

    public ShopSimpleListResp querySimpleList(ShopSimpleQueryReq build) {
        return ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.querySimpleList(build));
    }

}
