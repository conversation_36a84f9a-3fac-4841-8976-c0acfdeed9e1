package com.sankuai.shangou.seashop.product.common.es.service;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.trade.common.enums.ProductSortFieldMappingEnum;
import org.apache.commons.lang3.ObjectUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.boot.utils.PageUtil;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.common.es.EagleProductService;
import com.sankuai.shangou.seashop.product.common.es.model.EagleQueryResult;
import com.sankuai.shangou.seashop.product.common.es.model.EsBase;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/26 14:38
 */
@Slf4j
public abstract class AbsEsService<P, M extends EsBase> {

    @Resource
    protected EagleProductService eagleProductService;

    public BasePageResp<M> page(BasePageParam pageParam, P queryBo, List<FieldSortReq> sortList) {
        // 构建搜索请求
        SearchRequest searchRequest = buildProductSearchRequest(pageParam, queryBo, sortList);
        log.info("【{}搜索】搜索条件为: {}", getLogPrefix(), JsonUtil.toJsonString(searchRequest));
        // 调用ES进行查询和聚合
        EagleQueryResult searchResult = eagleProductService.queryByCondition(searchRequest, pageParam.getScrollId(), pageParam.getKeepAliveMinutes());
        log.info("【{}搜索】搜索条件为: {}, 搜索结果为: {}", getLogPrefix(), JsonUtil.toJsonString(queryBo), JsonUtil.toJsonString(searchResult));
        // 解析搜索结果
        BasePageResp<M> pageResult = resolveProductSearchResult(pageParam, searchResult);
        log.debug("【{}搜索】搜索条件为: {}, 搜索结果为: {}", getLogPrefix(), JsonUtil.toJsonString(queryBo), JsonUtil.toJsonString(pageResult));
        return pageResult;
    }

    public List<M> list(P queryBo) {
        BasePageParam pageParam = new BasePageParam();
        pageParam.setPageNum(CommonConstant.DEFAULT_PAGE_NO);
        pageParam.setPageSize(CommonConstant.ES_QUERY_LIMIT);
        return PageResultHelper.getData(page(pageParam, queryBo, null));
    }

    public M getById(String id) {
        // 构建搜索请求
        SearchResponse searchResult = eagleProductService.queryById(getIndexName(), Arrays.asList(id));
        log.info("【{}搜索】搜索条件为: {}, 搜索结果为: {}", getLogPrefix(), JsonUtil.toJsonString(id), JsonUtil.toJsonString(searchResult));
        SearchHits hits = searchResult.getHits();
        List<String> productStrList = Lists.newArrayList();
        for (SearchHit next : hits) {
            productStrList.add(next.getSourceAsString());
        }
        if (CollectionUtils.isEmpty(productStrList)) {
            return null;
        }
        return JsonUtil.parseObject(productStrList.get(0), getModelClass());
    }

    public void deleteById(String id) {
        eagleProductService.batchDelete(getIndexName(), Arrays.asList(id));
    }

    public long count(P queryBo) {
        return eagleProductService.countByCondition(buildProductCountRequest(queryBo)).getCount();
    }

    /**
     * 构建查询条件
     *
     * @param pageParam 分页参数
     * @param queryBo   查询参数
     * @param sortList  排序字段
     * @return 搜索请求
     */
    private SearchRequest buildProductSearchRequest(BasePageParam pageParam, P queryBo, List<FieldSortReq> sortList) {
        boolean useScroll = ObjectUtils.defaultIfNull(pageParam.getUseScroll(), false);

        // 构建查询条件
        BoolQueryBuilder boolQueryBuilder = buildProductSearchCondition(queryBo);
        // 构建排序
        // 构建排序
        List<SortBuilder<FieldSortBuilder>> sortBuilders = new ArrayList<>();
        sortBuilders.add(SortBuilders.fieldSort(ProductSortFieldMappingEnum.SCORE.getEsFieldName()).order(SortOrder.DESC));
        List<SortBuilder<FieldSortBuilder>> sortBuilders2 = buildProductFieldSortList(sortList);
        if (!CollectionUtils.isEmpty(sortBuilders2)) {
            sortBuilders.addAll(sortBuilders2);
        }
        // 整合查询条件
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(boolQueryBuilder)
                .trackTotalHits(true)
                .size(pageParam.pageSize);
        // 如果没有使用滚动更新，则设置分页
        if (!useScroll) {
            sourceBuilder.from(PageUtil.getStart(pageParam.getPageNum(), pageParam.getPageSize()));
        }

        // 设置排序
        sortBuilders.forEach(sourceBuilder::sort);
        // 创建搜索请求
        SearchRequest searchRequest = new SearchRequest(getIndexName());
        searchRequest.source(sourceBuilder);
        if (useScroll) {
            Integer keepAliveMinutes = ObjectUtils.defaultIfNull(pageParam.getKeepAliveMinutes(), 1);
            Scroll scroll = new Scroll(TimeValue.timeValueMinutes(keepAliveMinutes));
            searchRequest.scroll(scroll);
        }
        return searchRequest;
    }

    private CountRequest buildProductCountRequest(P queryBo) {
        // 构建查询条件
        BoolQueryBuilder boolQueryBuilder = buildProductSearchCondition(queryBo);

        // 整合查询条件
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(boolQueryBuilder)
                .trackTotalHits(true);

        // 创建搜索请求
        CountRequest searchRequest = new CountRequest(getIndexName());
        searchRequest.source(sourceBuilder);
        return searchRequest;
    }

    /**
     * 构建商品排序参数
     *
     * @param sortList 排序列表
     * @return 排序参数
     */
    private List<SortBuilder<FieldSortBuilder>> buildProductFieldSortList(List<FieldSortReq> sortList) {
        if (CollectionUtils.isEmpty(sortList)) {
            sortList = defaultSortList();
        }
        if (CollectionUtils.isEmpty(sortList)) {
            return Lists.newArrayList();
        }

        return sortList.stream()
                .map(sf -> SortBuilders.fieldSort(sf.getSort()).order(sf.getIzAsc() ? SortOrder.ASC : SortOrder.DESC))
                .collect(Collectors.toList());
    }

    /**
     * 解析搜索结果
     *
     * @param pageParam    分页参数
     * @param searchResult 搜索结果
     * @return 解析后的分页对象
     */
    private BasePageResp<M> resolveProductSearchResult(BasePageParam pageParam, EagleQueryResult searchResult) {
        // 解析商品列表
        List<M> productList = searchResult.getHits().stream()
                .map(hit -> JsonUtil.parseObject(hit, getModelClass()))
                .collect(Collectors.toList());
        // 构造分页结果
        int totalHit = searchResult.getTotalHit().intValue();
        BasePageResp<M> productPage = new BasePageResp<>();
        productPage.setData(productList);
        productPage.setPages(PageUtil.totalPage(totalHit, pageParam.getPageSize()));
        productPage.setTotalCount(searchResult.getTotalHit());
        productPage.setPageNo(pageParam.getPageNum());
        productPage.setPageSize(pageParam.getPageSize());
        productPage.setScrollId(searchResult.getScrollId());
        return productPage;
    }

    /**
     * 部分更新
     */
    public void partUpdate(M updateBo) {
        Map<String/*docId*/, Map<String/*字段名称*/, Object/*字段值*/>> paramMap = new HashMap<>(2);
        Map<String/*字段名称*/, Object/*字段值*/> doc = JsonUtil.beanToMap(updateBo);
        paramMap.put(String.valueOf(updateBo.getId()), doc);
        eagleProductService.partUpdate(getIndexName(), paramMap);
    }

    /**
     * 获取实体的真实类型
     */
    private Class<M> getModelClass() {
        Type type = this.getClass().getGenericSuperclass();
        ParameterizedType parameterizedType = (ParameterizedType) type;
        Class<M> clazz = (Class<M>) parameterizedType.getActualTypeArguments()[1];
        return clazz;
    }

    /**
     * 获取日志打印前缀
     */
    public abstract String getLogPrefix();

    /**
     * 获取索引名称
     */
    public abstract String getIndexName();

    /**
     * 查询条件
     */
    public abstract BoolQueryBuilder buildProductSearchCondition(P queryBo);

    /**
     * 默认排序
     */
    public abstract List<FieldSortReq> defaultSortList();

    /**
     * 清空s
     */
    public void clearIndex() {
        eagleProductService.clearIndex(getIndexName());
    }

    /**
     * 清空滚动id
     */
    public void clearScrollId(String scrollId) {
        eagleProductService.clearScroll(scrollId);
    }
}
