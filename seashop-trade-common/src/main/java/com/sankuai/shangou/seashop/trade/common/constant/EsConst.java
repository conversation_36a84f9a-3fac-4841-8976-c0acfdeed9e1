package com.sankuai.shangou.seashop.trade.common.constant;

/**
 * <AUTHOR>
 */
public class EsConst {

    // 交易商品索引
    public static String INDEX_TRADE_PRODUCT = "trade_product_index";
    public static final String IK_ANALYZER = "ik_smart";

    public static class TradeProduct {

        public static final String SEARCH_FIELD_PRODUCT_ID = "productId";
        public static final String SEARCH_FIELD_PRODUCT_NAME = "productName";
        public static final String SEARCH_FIELD_BRAND_ID = "brandId";
        public static final String SEARCH_FIELD_CATEGORY_PATH = "categoryPath.keyword";
        public static final String SEARCH_NEST_FIELD_PRODUCT_ATTRIBUTE = "productAttribute";
        public static final String SEARCH_NEST_FIELD_PRODUCT_ATTRIBUTE_VALUE = "productAttribute.attributeValues";
        public static final String SEARCH_FIELD_TOTAL_SALE_COUNTS = "totalSaleCounts";
        public static final String SEARCH_FIELD_VISIT_COUNTS = "visitCounts";
        public static final String SEARCH_FIELD_CATEGORY_ID = "categoryId";
        public static final String SEARCH_FIELD_SHOP_ID = "shopId";
        public static final String SEARCH_FIELD_ON_SALE_TIME = "onsaleTime";
        public static final String SEARCH_FIELD_ADDED_TIME = "addedTime";
        public static final String AGG_BRAND_ID = "brandIdAggregation";
        public static final String AGG_CATEGORY_ID = "categoryIdAggregation";
        public static final String AGG_ATTRIBUTE = "attributes";
        public static final String SEARCH_FIELD_SHOP_DISPLAY_SEQUENCE = "shopDisplaySequence";
        public static final String SEARCH_FIELD_SALE_STATUS = "saleStatus";
        public static final String SEARCH_FIELD_AUDIT_STATUS = "auditStatus";
        public static final String SEARCH_FIELD_BARS_CODE = "barCodes";
        public static final String SEARCH_FIELD_FAVORITE_COUNT = "favoriteCount";
    }

}
