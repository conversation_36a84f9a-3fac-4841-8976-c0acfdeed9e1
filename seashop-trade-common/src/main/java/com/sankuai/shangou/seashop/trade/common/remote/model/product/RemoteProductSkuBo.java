package com.sankuai.shangou.seashop.trade.common.remote.model.product;

import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class RemoteProductSkuBo {

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 类目路径
     */
    private String categoryPath;

    /**
     * 类目全路径
     */
    private String fullCategoryName;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 销售状态 1-销售中 2-仓库中 3-草稿箱
     */
    private ProductEnum.SaleStatusEnum saleStatus;

    /**
     * 审核状态 1-待审核 2-销售中 3-未通过 4-违规下架
     */
    private ProductEnum.AuditStatusEnum auditStatus;

    /**
     * 添加时间
     */
    private Date addedDate;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 最小销售价
     */
    private BigDecimal minSalePrice;

    /**
     * 是否有sku
     */
    private Boolean hasSku;

    /**
     * 运费模板ID
     */
    private Long freightTemplateId;
    /**
     * 重量
     */
    private BigDecimal weight;
    /**
     * 体积
     */
    private BigDecimal volume;

    /**
     * 最大购买数
     */
    private Integer maxBuyCount;

    /**
     * 是否开启阶梯价
     */
    private Boolean whetherOpenLadder;

    /**
     * 规格1别名
     */
    private String spec1Alias;

    /**
     * 规格2别名
     */
    private String spec2Alias;

    /**
     * 规格3别名
     */
    private String spec3Alias;

    /**
     * 商品主图
     */
    private String imagePath;

    /**
     * 倍数起购量
     */
    private Integer multipleCount;
    /**
     * 是否删除
     */
    private Boolean whetherDelete;

    /**
     * sku自增id
     */
    private Long skuAutoId;

    /**
     * sku 拼接id 商品ID_规格1ID_规格2ID_规格3ID
     */
    private String skuId;

    /**
     * 规格1
     */
    private String spec1Value;

    /**
     * 规格2
     */
    private String spec2Value;

    /**
     * 规格3
     */
    private String spec3Value;

    /**
     * 销售价
     */
    private BigDecimal salePrice;

    /**
     * sku库存
     *
     */
    private Long stock;

    /**
     * 阶梯价信息
     */
    private List<RemoteLadderPriceBo> ladderPriceList;
    /**
     * sku 货号
     */
    private String skuCode;

    /**
     * 计量单位
     */
    private String measureUnit;

    /**
     * sku图片，可能为空
     */
    private String showPic;

}
