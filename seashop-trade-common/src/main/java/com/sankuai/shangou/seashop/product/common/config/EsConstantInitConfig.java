package com.sankuai.shangou.seashop.product.common.config;

import com.sankuai.shangou.seashop.trade.common.constant.EsConst;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.common.constant.EsConstant;

/**
 * <AUTHOR>
 * @date 2024/04/25 17:15
 */
@Component
public class EsConstantInitConfig implements InitializingBean {


    @Value("${es.index.product:trade_product_index}")
    private String idxProduct;

    @Value("${es.index.product-audit:product_audit_index}")
    private String idxProductAudit;

    @Value("${spring.profiles.active}")
    private String indexPrefix;

    @Override
    public void afterPropertiesSet() {
        EsConstant.INDEX_TRADE_PRODUCT = indexPrefix + "." + idxProduct;
        EsConstant.INDEX_PRODUCT_AUDIT = indexPrefix + "." + idxProductAudit;
        EsConst.INDEX_TRADE_PRODUCT = indexPrefix + "." + idxProduct;
    }

}
