package com.sankuai.shangou.seashop.product.common.es.service;

import java.util.Arrays;
import java.util.List;

import com.sankuai.shangou.seashop.trade.common.config.EsIndexProps;
import com.sankuai.shangou.seashop.trade.common.es.EagleService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MatchQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.product.common.constant.EsConstant;
import com.sankuai.shangou.seashop.product.common.es.model.product.EsProductModel;
import com.sankuai.shangou.seashop.product.common.es.model.product.EsProductParam;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/26 14:08
 */
@Service
@Slf4j
public class EsProductAuditService extends AbsEsService<EsProductParam, EsProductModel> {


    @Override
    public String getLogPrefix() {
        return "商品审核";
    }

    @Value("${spring.profiles.active}")
    private String indexPrefix;
    @Resource
    private EagleService eagleService;
    @Resource
    private EsIndexProps esIndexProps;
    @Override
    public String getIndexName() {
        return EsConstant.INDEX_PRODUCT_AUDIT;
    }

    @Override
    public BoolQueryBuilder buildProductSearchCondition(EsProductParam queryBo) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (queryBo.getProductId() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_PRODUCT_ID, queryBo.getProductId()));
        }
        if (CollectionUtils.isNotEmpty(queryBo.getProductIds())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_PRODUCT_ID, queryBo.getProductIds()));
        }
        if (queryBo.getSkuAutoId() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_SKU_AUTO_IDS, queryBo.getSkuAutoId()));
        }
        if (CollectionUtils.isNotEmpty(queryBo.getSkuAutoIds())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_SKU_AUTO_IDS, queryBo.getSkuAutoIds()));
        }
        if (CollectionUtils.isNotEmpty(queryBo.getSkuCodes())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_SKU_CODES, queryBo.getSkuCodes()));
        }
        if (StringUtils.isNotEmpty(queryBo.getProductName())) {
            String searchKey = queryBo.getProductName();
//            searchKey = searchKey.replaceAll("[,，]", " ");
//            // 根据空格拆分为并列多个关键词
//            String[] keys = searchKey.split(" ");
//            for (String key : keys) {
//                if (StringUtils.isEmpty(key)) {
//                    continue;
//                }
                MatchQueryBuilder matchProductNameQueryBuilder = QueryBuilders.matchQuery(EsConstant.Product.SEARCH_FIELD_PRODUCT_NAME, searchKey);
//                List<String> keywords = eagleService.analyze(key);
//                if (CollectionUtils.isNotEmpty(keywords)) {
//                    float minimumShouldMatch = keywords.size() * esIndexProps.getMinimumShouldProportion();
//                    log.info("product size={},minimumShouldMatch={}", keywords.size(), minimumShouldMatch);
//                    matchProductNameQueryBuilder.minimumShouldMatch(Math.round(minimumShouldMatch) + "");
//                }
                boolQueryBuilder.must(matchProductNameQueryBuilder);
//            }
        }
        if (queryBo.getShopId() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_SHOP_ID, queryBo.getShopId()));
        }
        if (queryBo.getStartTime() != null) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery(EsConstant.Product.SEARCH_FIELD_ADDED_TIME).gte(DateUtil.beginOfDay(queryBo.getStartTime()).getTime()));
        }
        if (queryBo.getEndTime() != null) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery(EsConstant.Product.SEARCH_FIELD_ADDED_TIME).lte(DateUtil.endOfDay(queryBo.getEndTime()).getTime()));
        }
        if (StringUtils.isNotEmpty(queryBo.getProductCode())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_PRODUCT_CODE, queryBo.getProductCode()));
        }
        if (CollectionUtils.isNotEmpty(queryBo.getBrandIds())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_BRAND_ID, queryBo.getBrandIds()));
        }
        if (StringUtils.isNotEmpty(queryBo.getCategoryPath())) {
            boolQueryBuilder.filter(QueryBuilders.prefixQuery(EsConstant.Product.SEARCH_FIELD_CATEGORY_PATH, queryBo.getCategoryPath()));
        }
        if (queryBo.getAuditStatusCode() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_AUDIT_STATUS, queryBo.getAuditStatusCode()));
        }
        if (queryBo.getSaleStatusCode() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_SALE_STATUS, queryBo.getSaleStatusCode()));
        }
        if (CollectionUtils.isNotEmpty(queryBo.getAuditStatusCodeList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_AUDIT_STATUS, queryBo.getAuditStatusCodeList()));
        }
        if (CollectionUtils.isNotEmpty(queryBo.getSaleStatusCodeList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_SALE_STATUS, queryBo.getSaleStatusCodeList()));
        }
        if (queryBo.getShopCategoryId() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_SHOP_CATEGORY_IDS, queryBo.getShopCategoryId()));
        }
        if (CollectionUtils.isNotEmpty(queryBo.getShopCategoryIds())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_SHOP_CATEGORY_IDS, queryBo.getShopCategoryIds()));
        }
        if (queryBo.getFreightTemplateId() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_FREIGHT_TEMPLATE_ID, queryBo.getFreightTemplateId()));
        }
        if (queryBo.getWhetherBelowSafeStock() != null && queryBo.getWhetherBelowSafeStock()) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_BELOW_SAFE_STOCK, queryBo.getWhetherBelowSafeStock()));
        }
        if (queryBo.getCreateTimeStart() != null) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery(EsConstant.Product.SEARCH_FIELD_CREATE_TIME).gte(DateUtil.beginOfDay(queryBo.getCreateTimeStart()).getTime()));
        }
        if (queryBo.getCreateTimeEnd() != null) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery(EsConstant.Product.SEARCH_FIELD_CREATE_TIME).lte(DateUtil.endOfDay(queryBo.getCreateTimeEnd()).getTime()));
        }
        if (queryBo.getUpdateTimeStart() != null) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery(EsConstant.Product.SEARCH_FIELD_UPDATE_TIME).gte(DateUtil.beginOfDay(queryBo.getUpdateTimeStart()).getTime()));
        }
        if (queryBo.getUpdateTimeEnd() != null) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery(EsConstant.Product.SEARCH_FIELD_UPDATE_TIME).lte(DateUtil.endOfDay(queryBo.getUpdateTimeEnd()).getTime()));
        }
        if (queryBo.getSource() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_SOURCE, queryBo.getSource()));
        }
        if (queryBo.getWhetherOpenLadder() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_OPEN_LADDER, queryBo.getWhetherOpenLadder()));
        }
        if (CollectionUtils.isNotEmpty(queryBo.getExcludeProductIds())) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_PRODUCT_ID, queryBo.getExcludeProductIds()));
        }
        if (CollectionUtils.isNotEmpty(queryBo.getShopIds())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_SHOP_ID, queryBo.getShopIds()));
        }
        if (queryBo.getCategoryId() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_CATEGORY_ID, queryBo.getCategoryId()));
        }
        // 此处逻辑主要为了满足erp 同时查询草稿箱和违规下架的场景
        if (CollectionUtils.isNotEmpty(queryBo.getStatusList())) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            queryBo.getStatusList().forEach(status -> {
                BoolQueryBuilder subBoolQuery = QueryBuilders.boolQuery();
                if (status.getSaleStatus() != null) {
                    subBoolQuery.should(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_SALE_STATUS, status.getSaleStatus()));
                }
                if (status.getAuditStatus() != null) {
                    subBoolQuery.should(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_AUDIT_STATUS, status.getAuditStatus()));
                }
                boolQuery.should(subBoolQuery);
            });
            boolQueryBuilder.filter(boolQuery);
        }
        boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_WHETHER_DELETE, Boolean.FALSE));
        return boolQueryBuilder;
    }

    @Override
    public List<FieldSortReq> defaultSortList() {
        FieldSortReq sort = new FieldSortReq();
        sort.setSort(EsConstant.Product.SEARCH_FIELD_UPDATE_TIME);
        sort.setIzAsc(Boolean.FALSE);
        return Arrays.asList(sort);
    }
}
