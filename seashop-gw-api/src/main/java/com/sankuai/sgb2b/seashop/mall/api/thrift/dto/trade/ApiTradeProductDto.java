package com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "交易商品品牌对象")
@ToString
@Data
public class ApiTradeProductDto extends BaseThriftDto {

    @FieldDoc(description = "商品编码")
    private String productCode;
    @FieldDoc(description = "商品ID")
    private String productId;
    @FieldDoc(description = "商品名称")
    private String productName;
    @FieldDoc(description = "销量")
    private Long saleCount;
    @FieldDoc(description = "店铺ID")
    private Long shopId;
    @FieldDoc(description = "商品所有图片地址，多个图片地址用逗号分隔")
    @JsonUrlFormat(deserializer = false, split = ",")
    private String allImagePath;
    @FieldDoc(description = "品牌ID")
    private Long brandId;
    @FieldDoc(description = "分类ID")
    private Long categoryId;
    @FieldDoc(description = "分类全路径")
    private String categoryPath;
    @FieldDoc(description = "市场价")
    private BigDecimal marketPrice;
    @FieldDoc(description = "销售价")
    private BigDecimal salePrice;
    @FieldDoc(description = "预估价")
    private BigDecimal estimatePrice;
    @FieldDoc(description = "商品主图")
    @JsonUrlFormat(deserializer = false)
    private String mainImagePath;
    @FieldDoc(description = "上架时间")
    private Long onsaleTime;
    @FieldDoc(description = "店铺名称")
    private String shopName;
    /**
     * 是否单规格商品
     */
    @FieldDoc(description = "是否单规格商品")
    private Boolean whetherSingleSku;
    /**
     * skuId
     */
    @FieldDoc(description = "skuId")
    private String skuId;
    /**
     * 倍数起购量
     */
    @FieldDoc(description = "倍数起购量")
    private Integer multipleCount;
    /**
     * 起购量，根据倍数和阶梯价数量计算得到的最小购买数量
     */
    @FieldDoc(description = "最小购买基数")
    private Integer minBuyCount;
    /**
     * 购物车中商品维度数量
     */
    @FieldDoc(description = "购物车中商品维度数量")
    private Long cartProductCount;

    @FieldDoc(description = "总销量")
    private Long totalSaleCounts;

    @FieldDoc(description = "浏览量")
    private Integer visitCounts;
    /**
     * 是否专享价商品
     */
    @FieldDoc(description = "是否专享价商品")
    private Boolean whetherExclusive;
    /**
     * 单规格商品库存
     */
    @FieldDoc(description = "单规格商品库存")
    private Long singleSkuStock;
    /**
     * 是否缺货（售罄）
     */
    @FieldDoc(description = "是否缺货（售罄）")
    private Boolean whetherOutOfStock;
    /**
     * 原售价，不考虑任何营销前的，sku的最小售价
     */
    @FieldDoc(description = "sku的最小售价")
    private BigDecimal minSalePrice;

    /**
     * 收藏数
     */
    @FieldDoc(description = "收藏数")
    private Integer favoriteCount;
    /**
     * 是否限时购
     */
    @FieldDoc(description = "是否限时购")
    private Boolean whetherFlashSale;
    @FieldDoc(description = "评论数")
    private Integer evaluateNum;
    @FieldDoc(description = "是否显示价格XX起")
    private Boolean showPriceStartAt;


    public String getMarketPriceString() {
        return this.bigDecimal2String(this.marketPrice);
    }


    public void setMarketPriceString(String marketPrice) {
        this.marketPrice = this.string2BigDecimal(marketPrice);
    }


    public String getSalePriceString() {
        return this.bigDecimal2String(this.salePrice);
    }


    public void setSalePriceString(String salePrice) {
        this.salePrice = this.string2BigDecimal(salePrice);
    }


    public String getEstimatePriceString() {
        return this.bigDecimal2String(this.estimatePrice);
    }


    public void setEstimatePriceString(String estimatePrice) {
        this.estimatePrice = this.string2BigDecimal(estimatePrice);
    }


    public String getMinSalePriceString() {
        return this.bigDecimal2String(this.minSalePrice);
    }


    public void setMinSalePriceString(String minSalePrice) {
        this.minSalePrice = this.string2BigDecimal(minSalePrice);
    }


}
