package com.sankuai.shangou.seashop.m.thrift.core.request.product;

import java.util.Date;
import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/11/16 12:43
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "查询商品入参")
public class ApiQueryProductReq extends BasePageReq {

    @FieldDoc(description = "商品id", requiredness = Requiredness.NONE)
    private String productId;

    @FieldDoc(description = "规格自增id", requiredness = Requiredness.NONE)
    private String skuAutoId;

    @FieldDoc(description = "商品名称", requiredness = Requiredness.NONE)
    private String productName;

    @FieldDoc(description = "商品编码", requiredness = Requiredness.NONE)
    private String productCode;

    @FieldDoc(description = "店铺分类id", requiredness = Requiredness.NONE)
    private Long shopCategoryId;

    @FieldDoc(description = "品牌名称", requiredness = Requiredness.NONE)
    private String brandName;

    @FieldDoc(description = "开始时间", requiredness = Requiredness.NONE)
    private Date startTime;

    @FieldDoc(description = "结束时间", requiredness = Requiredness.NONE)
    private Date endTime;

    @FieldDoc(description = "警戒库存", requiredness = Requiredness.NONE)
    private Boolean whetherBelowSafeStock;

    @FieldDoc(description = "商品状态 1-销售中 2-仓库中 3-违规下架 4-草稿箱", requiredness = Requiredness.NONE)
    private Integer statusCode;

    @FieldDoc(description = "商品状态集合", requiredness = Requiredness.NONE)
    private List<Integer> inStatusCodes;

    @FieldDoc(description = "店铺名称", requiredness = Requiredness.NONE)
    private String shopName;

    @FieldDoc(description = "店铺id", requiredness = Requiredness.NONE)
    private Long shopId;

    @FieldDoc(description = "类目路径 | 隔开", requiredness = Requiredness.NONE)
    private String categoryPath;

    @FieldDoc(description = "排序字段", requiredness = Requiredness.NONE)
    private List<FieldSortReq> sortList;

    @FieldDoc(description = "商品id列表", requiredness = Requiredness.NONE)
    private List<Long> productIds;

    @FieldDoc(description = "类目id列表", requiredness = Requiredness.NONE)
    private List<Long> categoryIds;

    @FieldDoc(description = "运费模板id", requiredness = Requiredness.NONE)
    private Long freightTemplateId;

    @FieldDoc(description = "来源 1-商城 2-牵牛花 3-易久批", requiredness = Requiredness.NONE)
    private Integer source;

    @FieldDoc(description = "是否开启梯度", requiredness = Requiredness.NONE)
    private Boolean whetherOpenLadder;

    @FieldDoc(description = "排除商品id列表", requiredness = Requiredness.NONE)
    private List<Long> excludeProductIds;

    @FieldDoc(description = "是否有库存", requiredness = Requiredness.NONE)
    private Boolean hasStock;

    @FieldDoc(description = "父类目id", requiredness = Requiredness.NONE)
    private Long parentCategoryId;

    @Override
    public void checkParameter() {
        productId = StrUtil.emptyToDefault(productId, null);
        AssertUtil.throwInvalidParamIfTrue(productId != null && !NumberUtil.isLong(productId), "请输入正确的商品id");
        skuAutoId = StrUtil.emptyToDefault(skuAutoId, null);
        AssertUtil.throwInvalidParamIfTrue(skuAutoId != null && !NumberUtil.isLong(skuAutoId), "请输入正确的规格id");
    }


    public Long getStartTimeLong() {
        return this.date2Long(this.startTime);
    }


    public void setStartTimeLong(Long startTime) {
        this.startTime = this.long2Date(startTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }


}
