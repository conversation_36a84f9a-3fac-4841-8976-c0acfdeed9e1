package com.sankuai.shangou.seashop.m.thrift.core.response.product;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/16 13:56
 */
@TypeDoc(
    description = "商品审核分页信息"
)
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@Data
public class ApiProductAuditPageResp extends BaseThriftDto {

    @FieldDoc(description = "商品id")
    private String productId;

    @FieldDoc(description = "序号")
    private Long displaySequence;

    @FieldDoc(description = "商品图片")
    @JsonUrlFormat(deserializer = false)
    private String imagePath;

    @FieldDoc(description = "商品名称")
    private String productName;

    @FieldDoc(description = "最小销售价")
    private BigDecimal minSalePrice;

    @FieldDoc(description = "品牌id")
    private Long brandId;

    @FieldDoc(description = "品牌名称")
    private String brandName;

    @FieldDoc(description = "审核状态 1-待审核 2-销售中 3-未通过 4-违规下架")
    private Integer auditStatusCode;

    @FieldDoc(description = "审核状态描述")
    private String auditStatusDesc;

    @FieldDoc(description = "货号")
    private String productCode;

    @FieldDoc(description = "类目id")
    private Long categoryId;

    @FieldDoc(description = "类目名称")
    private String categoryName;

    @FieldDoc(description = "库存")
    private Long stock;

    @FieldDoc(description = "限购")
    private Integer maxBuyCount;

    @FieldDoc(description = "实际销量")
    private Long saleCounts;

    @FieldDoc(description = "虚拟销量")
    private Long virtualSaleCounts;

    @FieldDoc(description = "发布时间")
    private Date addedDate;

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "审核时间")
    private Date checkTime;

    @FieldDoc(description = "拒绝原因")
    private String auditReason;

    @FieldDoc(description = "来源 1-商城 2-牵牛花 3-易久批")
    private Integer source;

    @FieldDoc(description = "来源描述")
    private String sourceDesc;

    @FieldDoc(description = "完整的类目名称")
    private String fullCategoryName;

    @FieldDoc(description = "最大售价")
    private BigDecimal maxSalePrice;

    @FieldDoc(description = "价格区间")
    private String salePriceRange;


    public String getMinSalePriceString() {
        return this.bigDecimal2String(this.minSalePrice);
    }


    public void setMinSalePriceString(String minSalePrice) {
        this.minSalePrice = this.string2BigDecimal(minSalePrice);
    }


    public Long getAddedDateLong() {
        return this.date2Long(this.addedDate);
    }


    public void setAddedDateLong(Long addedDate) {
        this.addedDate = this.long2Date(addedDate);
    }


    public Long getCheckTimeLong() {
        return this.date2Long(this.checkTime);
    }


    public void setCheckTimeLong(Long checkTime) {
        this.checkTime = this.long2Date(checkTime);
    }


    public String getMaxSalePriceString() {
        return this.bigDecimal2String(this.maxSalePrice);
    }


    public void setMaxSalePriceString(String maxSalePrice) {
        this.maxSalePrice = this.string2BigDecimal(maxSalePrice);
    }


}
