package com.sankuai.shangou.seashop.m.thrift.system.setting.response;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import lombok.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(
    description = "站点设置返回对象"
)
public class ApiBaseSitSettingRes extends BaseThriftDto {
    /**
     * 站点名称
     */
    @FieldDoc(description = "站点名称")
    private String siteName;

    /**
     * 站点logo
     */
    @FieldDoc(description = "站点logo")
    @JsonUrlFormat(deserializer = false)
    private String logo;

    /**
     * 微信logo
     */
    @FieldDoc(description = "微信logo")
    @JsonUrlFormat(deserializer = false)
    private String wxLogo;

    /**
     * 卖家端logo
     */
    @FieldDoc(description = "卖家端logo")
    private String memberLogo;

    /**
     * 二维码
     */
    @FieldDoc(description = "二维码")
    private String qrCode;

    /**
     * pc商城登录图片
     */
    @FieldDoc(description = "pc商城登录图片")
    private String pcLoginPic;

    /**
     * pc商城登录按钮
     */
    @FieldDoc(description = "pc商城登录按钮")
    private String pcBottomPic;

    /**
     * 客服电话
     */
    @FieldDoc(description = "客服电话")
    private String sitePhone;

    /**
     * 网店授权域名
     */
    @FieldDoc(description = "网店授权域名")
    private String siteUrl;

    /**
     * 输入框关键字
     */
    @FieldDoc(description = "输入框关键字")
    private String inputBoxKeyWords;

    /**
     * 热门关键字
     */
    @FieldDoc(description = "热门关键字")
    private String popularKeyWords;

    /**
     * 页脚
     */
    @FieldDoc(description = "页脚")
    private String pageFoot;

    /**
     * SEO
     */
    @FieldDoc(description = "SEO标题")
    private String siteSEOTitle;


    @FieldDoc(description = "SEO关键字")
    private String siteSEOKeywords;
    @FieldDoc(description = "SEO详情")
    private String siteSEODescription;

    /**
     * 注册类型 0 普通账号  1 手机号
     */
    @FieldDoc(description = "注册类型 0 普通账号  1 手机号")
    private String registerType;

    /**
     * 是否打开邮箱效验
     */
    @FieldDoc(description = "是否打开邮箱效验")
    private String emailVerifOpen;

    /**
     * 是否强制绑定手机
     */
    @FieldDoc(description = "是否强制绑定手机")
    private String isConBindCellPhone;

    /**
     * QQ地图key
     */
    @FieldDoc(description = "QQ地图key")
    private String qQMapAPIKey;

    /**
     * 高德地图key
     */
    @FieldDoc(description = "高德地图key")
    private String jDRegionAppKey;

    /**
     * 底部服务
     */
    @FieldDoc(description = "底部服务")
    private String footServer;

    //第三方流量统计代码
    @FieldDoc(description = "第三方流量统计代码")
    @ExaminField(description = "第三方流量统计代码")
    private String thirdPartyFlowCode;

    //    是否提供app下载
    @FieldDoc(description = "是否提供app下载")
    @ExaminField(description = "是否提供app下载")
    private String isAppDownload;

    //    商城app版本号
    @FieldDoc(description = "商城app版本号")
    @ExaminField(description = "商城app版本号")
    private String appVersion;

    //    appleStore
    @FieldDoc(description = "appleStore")
    @ExaminField(description = "appleStore")
    private String appleStore;

    //    android
    @FieldDoc(description = "android")
    @ExaminField(description = "android")
    private String android;

    //    供应商app更新说明
    @FieldDoc(description = "供应商app更新说明")
    @ExaminField(description = "供应商app更新说明")
    private String appUpdate;


}
