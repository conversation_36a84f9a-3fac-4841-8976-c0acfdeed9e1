package com.sankuai.shangou.seashop.m.thrift.system.setting.request;

import org.apache.commons.lang3.StringUtils;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;

import lombok.Data;

@TypeDoc(description = "入驻设置")
@Data
public class ApiBaseSiteSettingReq extends BaseParamReq {
    /**
     * 站点名称
     */
    @FieldDoc(description = "站点名称", requiredness = Requiredness.REQUIRED)
    private String siteName;

    /**
     * 站点logo
     */
    @FieldDoc(description = "站点logo", requiredness = Requiredness.REQUIRED)
    private String logo;

    /**
     * 卖家中心Logo
     */
    @FieldDoc(description = "卖家中心Logo", requiredness = Requiredness.REQUIRED)
    private String memberLogo;

    /**
     * 微信logo
     */
    @FieldDoc(description = "微信logo")
    private String wxLogo;

    /**
     * 客服电话
     */
    @FieldDoc(description = "客服电话", requiredness = Requiredness.REQUIRED)
    private String sitePhone;

    /**
     * 网店授权域名
     */
    @FieldDoc(description = "网店授权域名")
    private String siteUrl;

    //第三方流量统计代码
    @FieldDoc(description = "第三方流量统计代码")
    private String thirdPartyFlowCode;

//    是否提供app下载
    @FieldDoc(description = "是否提供app下载")
    private String isAppDownload;

//    商城app版本号
    @FieldDoc(description = "商城app版本号")
    private String appVersion;

//    appleStore
    @FieldDoc(description = "appleStore")
    private String appleStore;

//    android
    @FieldDoc(description = "android")
    private String android;

//    供应商app更新说明
    @FieldDoc(description = "供应商app更新说明")
    private String appUpdate;

    /**
     * 高德地图key
     */
    @FieldDoc(description = "高德地图key")
    private String jDRegionAppKey;

    @FieldDoc(description = "热门搜索关键字")
    private String popularKeyWords;

    public void checkParameter() {
        if (StringUtils.isEmpty(this.siteName) || this.siteName.length() > 15) {
            throw new IllegalArgumentException("站点不能为空,并且不能大于15个字");
        }
//        if (StringUtils.isEmpty(this.logo)) {
//            throw new IllegalArgumentException("站点logo不能为空");
//        }
        if (StringUtils.isEmpty(this.sitePhone)) {
            throw new IllegalArgumentException("客服不能为空");
        }
    }


}
