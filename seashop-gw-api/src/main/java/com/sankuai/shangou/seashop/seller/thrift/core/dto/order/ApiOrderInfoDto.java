package com.sankuai.shangou.seashop.seller.thrift.core.dto.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "订单基本信息对象")
public class ApiOrderInfoDto extends BaseThriftDto {

    @FieldDoc(description = "订单号")
    private String orderId;
    @FieldDoc(description = "店铺id")
    private Long shopId;
    @FieldDoc(description = "店铺名称")
    private String shopName;
    @FieldDoc(description = "下单时间")
    private Date orderDate;
    @FieldDoc(description = "商品总数量")
    private Long productQuantity;
    @FieldDoc(description = "订单总额(实付金额)")
    private BigDecimal totalAmount;
    @FieldDoc(description = "商品总金额")
    private BigDecimal productTotalAmount;
    @FieldDoc(description = "订单实收金额(订单实付-退款金额)")
    private BigDecimal actualPayAmount;
    @FieldDoc(description = "订单状态。1:待付款；2：待发货；3：待收货；4：已关闭；5：已完成；6：支付中")
    private Integer orderStatus;
    @FieldDoc(description = "订单状态描述")
    private String orderStatusDesc;
    @FieldDoc(description = "订单商品列表")
    private List<ApiOrderItemInfoDto> itemList;
    @FieldDoc(description = "预计完成时间。待发货状态才有，显示自动确认收货时间")
    private String estimateCompleteTime;
    @FieldDoc(description = "优惠券金额")
    private BigDecimal couponAmount;
    /**
     * 运费金额
     */
    @FieldDoc(description = "运费金额")
    private BigDecimal freight;
    /**
     * 改运费时备份的运费
     */
    @FieldDoc(description = "改运费时备份的运费")
    private BigDecimal backupFreight;
    /**
     * 运费修改描述
     */
    @FieldDoc(description = "运费修改描述")
    private String freightUpdateDesc;
    @FieldDoc(description = "税费金额")
    private BigDecimal tax;
    @FieldDoc(description = "买家留言")
    private String userRemark;
    @FieldDoc(description = "折扣金额")
    private BigDecimal discountAmount;
    @FieldDoc(description = "满减金额")
    private BigDecimal moneyOffAmount;
    /**
     * 总的改价金额
     */
    @FieldDoc(description = "总的改价金额")
    private BigDecimal totalUpdatedAmount;
    @FieldDoc(description = "卖家备注")
    private String sellerRemark;
    /**
     * 卖家备注标记
     */
    @FieldDoc(description = "卖家备注标记")
    private Integer sellerRemarkFlag;
    @FieldDoc(description = "支付渠道")
    private String payChannelName;
    @FieldDoc(description = "是否有售后")
    private Boolean hasRefund;
    @FieldDoc(description = "售后ID")
    private Long refundId;
    @FieldDoc(description = "牵牛花订单号")
    private String sourceOrderId;
    /**
     * 1. 发起了售后，订单退款，或仅退款，且是待供应商审核或待平台审核时，显示
     * 2. 发起了售后，退货退款时，待供应商审核或待买家寄货时，显示
     */
    @FieldDoc(description = "是否可以取消售后。用于控制前端【取消售后】按钮显示")
    private Boolean showCancelRefundBtn;
    /**
     * 订单退款，审核中时显示
     */
    @FieldDoc(description = "是否显示【退款中】状态文案")
    private Boolean showRefundingDesc;
    /**
     * 整单退，审核中时显示
     */
    @FieldDoc(description = "是否显示【退货/退款中】状态文案")
    private Boolean showRefundingAndReturnDesc;
    /**
     * 订单退款，供应商拒绝或者平台驳回
     */
    @FieldDoc(description = "是否显示【退款被拒】按钮")
    private Boolean showRefundingRejectBtn;
    /**
     * 整单退，供应商拒绝或者平台驳回
     */
    @FieldDoc(description = "是否显示【退货/退款被拒】按钮")
    private Boolean showRefundingAndReturnRejectBtn;
    @FieldDoc(description = "商家ID")
    private Long userId;
    @FieldDoc(description = "付款日期")
    private Date payDate;
    @FieldDoc(description = "用户名称")
    private String userName;
    /**
     * 商家手机号
     */
    @FieldDoc(description = "商家手机号")
    private String userPhone;
    /**
     * 用户昵称
     */
    @FieldDoc(description = "用户昵称")
    private String nick;
    /**
     * 下单平台。0:PC;2:小程序
     */
    @FieldDoc(description = "下单平台。0:PC;2:小程序")
    private Integer platform;
    /**
     * 下单平台描述
     */
    @FieldDoc(description = "下单平台描述")
    private String platformDesc;
    /**
     * 完成时间
     */
    @FieldDoc(description = "完成时间")
    private Date finishDate;
    /**
     * 支付方式
     */
    @FieldDoc(description = "支付方式")
    private Integer payment;
    /**
     * 支付方式描述
     */
    @FieldDoc(description = "支付方式描述")
    private String paymentDesc;
    /**
     * 交易单号
     */
    @FieldDoc(description = "交易单号")
    private String gatewayOrderId;
    /**
     * 发货时间
     */
    @FieldDoc(description = "发货时间")
    private Date shippingDate;
    /**
     * 支付方式：1=綫上支付
     */
    @FieldDoc(description = "支付方式：1=綫上支付")
    private Integer paymentType;
    @FieldDoc(description = "支付方式描述")
    private String paymentTypeDesc;
    /**
     * 收货信息
     */
    @FieldDoc(description = "最后一级收货区域ID")
    private Integer regionId;
    @FieldDoc(description = "第一级收货区域ID")
    private Integer topRegionId;
    @FieldDoc(description = "收货人")
    private String shipTo;
    @FieldDoc(description = "收货人电话")
    private String cellPhone;
    @FieldDoc(description = "收货区域全拼")
    private String regionFullName;
    @FieldDoc(description = "收货地址")
    private String address;
    @FieldDoc(description = "发票信息")
    private ApiOrderInvoiceDto orderInvoice;
    /**
     * 是否已过售后维权期，简单的按钮，前端需要根据状态和这个字段综合判断是否显示
     */
    @FieldDoc(description = "是否已过售后维权期，简单的按钮，前端需要根据状态和这个字段综合判断是否显示")
    private Boolean hasOverAfterSales;
    /**
     * 售后状态。
     */
    @FieldDoc(description = "售后状态")
    private Integer refundStatus;
    /**
     * 售后状态描述
     */
    @FieldDoc(description = "售后状态描述")
    private String refundStatusDesc;
    /**
     * 是否有审核中的售后，包括 待平台确认、待供应商审核
     */
    @FieldDoc(description = "是否有审核中的售后，包括 待平台确认、待供应商审核")
    private Boolean hasAuditingRefund;
    /**
     * 剩余支付时间，单位毫秒，待付款、支付中状态下显示
     */
    @FieldDoc(description = "剩余支付时间，单位毫秒，待付款、支付中状态下显示")
    private Long remainPayTime;
    @FieldDoc(description = "剩余支付时间描述")
    private String remainPayTimeDesc;

    @FieldDoc(description = "订单类型。1：正常订单；2：组合购订单；3：限时购订单")
    private Integer orderType;
    @FieldDoc(description = "订单类型描述")
    private String orderTypeDesc;
    //@FieldDoc(description = "订单来源。0:商城；1:牵牛花")
    //private Integer orderSource;
    //@FieldDoc(description = "订单来源描述")
    //private String orderSourceDesc;
    @FieldDoc(description = "客服列表")
    private List<ApiCustomServiceDto> customServiceList;
    /**
     * 订单评价状态
     */
    @FieldDoc(description = "订单评价状态。-1：不可评价；0：未评价；1：已评价；2：已追评")
    private Integer commentStatus;
    @FieldDoc(description = "订单评价状态描述")
    private String commentStatusDesc;


    public Long getOrderDateLong() {
        return this.date2Long(this.orderDate);
    }


    public void setOrderDateLong(Long orderDate) {
        this.orderDate = this.long2Date(orderDate);
    }


    public String getTotalAmountString() {
        return this.bigDecimal2String(this.totalAmount);
    }


    public void setTotalAmountString(String totalAmount) {
        this.totalAmount = this.string2BigDecimal(totalAmount);
    }


    public String getProductTotalAmountString() {
        return this.bigDecimal2String(this.productTotalAmount);
    }


    public void setProductTotalAmountString(String productTotalAmount) {
        this.productTotalAmount = this.string2BigDecimal(productTotalAmount);
    }


    public String getActualPayAmountString() {
        return this.bigDecimal2String(this.actualPayAmount);
    }


    public void setActualPayAmountString(String actualPayAmount) {
        this.actualPayAmount = this.string2BigDecimal(actualPayAmount);
    }


    public String getCouponAmountString() {
        return this.bigDecimal2String(this.couponAmount);
    }


    public void setCouponAmountString(String couponAmount) {
        this.couponAmount = this.string2BigDecimal(couponAmount);
    }


    public String getFreightString() {
        return this.bigDecimal2String(this.freight);
    }


    public void setFreightString(String freight) {
        this.freight = this.string2BigDecimal(freight);
    }


    public String getBackupFreightString() {
        return this.bigDecimal2String(this.backupFreight);
    }


    public void setBackupFreightString(String backupFreight) {
        this.backupFreight = this.string2BigDecimal(backupFreight);
    }


    public String getTaxString() {
        return this.bigDecimal2String(this.tax);
    }


    public void setTaxString(String tax) {
        this.tax = this.string2BigDecimal(tax);
    }


    public String getDiscountAmountString() {
        return this.bigDecimal2String(this.discountAmount);
    }


    public void setDiscountAmountString(String discountAmount) {
        this.discountAmount = this.string2BigDecimal(discountAmount);
    }


    public String getMoneyOffAmountString() {
        return this.bigDecimal2String(this.moneyOffAmount);
    }


    public void setMoneyOffAmountString(String moneyOffAmount) {
        this.moneyOffAmount = this.string2BigDecimal(moneyOffAmount);
    }


    public String getTotalUpdatedAmountString() {
        return this.bigDecimal2String(this.totalUpdatedAmount);
    }


    public void setTotalUpdatedAmountString(String totalUpdatedAmount) {
        this.totalUpdatedAmount = this.string2BigDecimal(totalUpdatedAmount);
    }


    public Long getPayDateLong() {
        return this.date2Long(this.payDate);
    }


    public void setPayDateLong(Long payDate) {
        this.payDate = this.long2Date(payDate);
    }


    public Long getFinishDateLong() {
        return this.date2Long(this.finishDate);
    }


    public void setFinishDateLong(Long finishDate) {
        this.finishDate = this.long2Date(finishDate);
    }


    public Long getShippingDateLong() {
        return this.date2Long(this.shippingDate);
    }


    public void setShippingDateLong(Long shippingDate) {
        this.shippingDate = this.long2Date(shippingDate);
    }


    //
    //public Integer getOrderSource() {
    //    return orderSource;
    //}
    //
    //
    //public void setOrderSource(Integer orderSource) {
    //    this.orderSource = orderSource;
    //}
    //
    //
    //public String getOrderSourceDesc() {
    //    return orderSourceDesc;
    //}
    //
    //
    //public void setOrderSourceDesc(String orderSourceDesc) {
    //    this.orderSourceDesc = orderSourceDesc;
    //}


}
