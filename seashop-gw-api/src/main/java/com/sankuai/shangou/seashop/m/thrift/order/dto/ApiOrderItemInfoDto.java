package com.sankuai.shangou.seashop.m.thrift.order.dto;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "订单明细请求入参")
public class ApiOrderItemInfoDto extends BaseThriftDto {

    @FieldDoc(description = "订单明细ID")
    private Long orderItemId;
    @FieldDoc(description = "商品ID")
    private String productId;
    @FieldDoc(description = "商品名称")
    private String productName;
    @FieldDoc(description = "商品主图")
    @JsonUrlFormat(deserializer = false)
    private String mainImagePath;
    @FieldDoc(description = "商品SKU ID")
    private String skuId;
    @FieldDoc(description = "商品SKU code")
    private String sku;
    @FieldDoc(description = "商品数量")
    private Long quantity;
    @FieldDoc(description = "商品单价")
    private BigDecimal salePrice;
    @FieldDoc(description = "商品总价")
    private BigDecimal realTotalPrice;
    @FieldDoc(description = "商品SKU 名称 color;size;version")
    private String skuName;
    @FieldDoc(description = "折扣金额")
    private BigDecimal discountAmount;
    @FieldDoc(description = "sku自增ID")
    private Long skuAutoId;
    @FieldDoc(description = "可退金额")
    private BigDecimal enabledRefundAmount;

    @FieldDoc(description = "订单ID")
    private String orderId;
    /**
     * 是否显示【退货退款】按钮
     * 前提：订单状态是待收货或者已完成；没有整单售后，明细没有发起售后，没有过售后维权期
     */
    @FieldDoc(description = "是否显示【退货退款】按钮")
    private Boolean showRefundAndReturnBtn;
    /**
     * 当前明细是否有售后
     */
    @FieldDoc(description = "当前明细是否有售后")
    private Boolean hasRefund;
    /**
     * 售后状态。
     */
    @FieldDoc(description = "售后状态。")
    private Integer refundStatus;
    /**
     * 售后状态描述
     */
    @FieldDoc(description = "售后状态描述")
    private String refundStatusDesc;
    /**
     * 售后ID
     */
    @FieldDoc(description = "售后ID")
    private Long refundId;
    /**
     * 是否允许7天无理由退货
     */
    @FieldDoc(description = "是否允许7天无理由退货")
    private Boolean enableNoReasonReturn;
    /**
     * 是否显示保障(保证金)标志
     */
    @FieldDoc(description = "是否显示保障(保证金)标志")
    private Boolean showGuaranteeFlag;
    /**
     * 汇总的满减金额(综合所有优惠的金额)
     */
    @FieldDoc(description = "汇总的满减金额(综合所有优惠的金额)")
    private BigDecimal realDiscountAmount;
    /**
     * 直接价格*数量的金额，realTotalPrice是计算了优惠的
     */
    @FieldDoc(description = "商品原总金额(价格*数量)")
    private BigDecimal originTotalAmount;
    /**
     * 是否显示闪电标识(是否立即发货)
     */
    @FieldDoc(description = "是否显示闪电标识(是否立即发货)")
    private Boolean showThunderFlag;
    @FieldDoc(description = "佣金")
    private BigDecimal commission;


    public String getSalePriceString() {
        return this.bigDecimal2String(this.salePrice);
    }


    public void setSalePriceString(String salePrice) {
        this.salePrice = this.string2BigDecimal(salePrice);
    }


    public String getRealTotalPriceString() {
        return this.bigDecimal2String(this.realTotalPrice);
    }


    public void setRealTotalPriceString(String realTotalPrice) {
        this.realTotalPrice = this.string2BigDecimal(realTotalPrice);
    }


    public String getDiscountAmountString() {
        return this.bigDecimal2String(this.discountAmount);
    }


    public void setDiscountAmountString(String discountAmount) {
        this.discountAmount = this.string2BigDecimal(discountAmount);
    }


    public String getEnabledRefundAmountString() {
        return this.bigDecimal2String(this.enabledRefundAmount);
    }


    public void setEnabledRefundAmountString(String enabledRefundAmount) {
        this.enabledRefundAmount = this.string2BigDecimal(enabledRefundAmount);
    }


    public String getRealDiscountAmountString() {
        return this.bigDecimal2String(this.realDiscountAmount);
    }


    public void setRealDiscountAmountString(String realDiscountAmount) {
        this.realDiscountAmount = this.string2BigDecimal(realDiscountAmount);
    }


    public String getOriginTotalAmountString() {
        return this.bigDecimal2String(this.originTotalAmount);
    }


    public void setOriginTotalAmountString(String originTotalAmount) {
        this.originTotalAmount = this.string2BigDecimal(originTotalAmount);
    }


    public String getCommissionString() {
        return this.bigDecimal2String(this.commission);
    }


    public void setCommissionString(String commission) {
        this.commission = this.string2BigDecimal(commission);
    }

}
