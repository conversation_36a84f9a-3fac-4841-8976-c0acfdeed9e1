package com.sankuai.shangou.seashop.seller.thrift.core.request.product;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.dto.ApiSaveProductLadderPriceDto;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.dto.ApiSaveProductSkuDto;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/13 19:16
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "发布商品入参")
public class ApiSaveProductReq extends BaseParamReq {

    @FieldDoc(description = "商品id 新增时不填/填0", requiredness = Requiredness.NONE)
    private Long productId;

    @FieldDoc(description = "类目id", requiredness = Requiredness.REQUIRED)
    private Long categoryId;

    @FieldDoc(description = "品牌id", requiredness = Requiredness.REQUIRED)
    private Long brandId;

    @FieldDoc(description = "商品名称(限100字)", requiredness = Requiredness.REQUIRED)
    private String productName;

    @FieldDoc(description = "广告词", requiredness = Requiredness.NONE)
    private String shortDescription;

    @FieldDoc(description = "是否开启阶梯价", requiredness = Requiredness.REQUIRED)
    private Boolean whetherOpenLadder;

    @FieldDoc(description = "阶梯价", requiredness = Requiredness.NONE)
    private List<ApiSaveProductLadderPriceDto> ladderPriceList;

    @FieldDoc(description = "商城价(可不传,多个规格的最小商城价,会获取sku集合中的最小商城价)", requiredness = Requiredness.NONE)
    private BigDecimal minSalePrice;

    @FieldDoc(description = "市场价", requiredness = Requiredness.NONE)
    private BigDecimal marketPrice;

    @FieldDoc(description = "库存(可不传,会计算sku集合中的库存之和)", requiredness = Requiredness.NONE)
    private Long stock;

    @FieldDoc(description = "商品货号(限100个字符)(同一店铺不能重复)", requiredness = Requiredness.REQUIRED)
    private String productCode;

    @FieldDoc(description = "计量单位", requiredness = Requiredness.NONE)
    private String measureUnit;

    @FieldDoc(description = "限购数", requiredness = Requiredness.REQUIRED)
    private Integer maxBuyCount;

    @FieldDoc(description = "倍数起购量", requiredness = Requiredness.REQUIRED)
    private Integer multipleCount;

    @FieldDoc(description = "警戒库存(可不传, 会获取第一个规格的安全库存)", requiredness = Requiredness.NONE)
    private Long safeStock;

    @FieldDoc(description = "店铺分类", requiredness = Requiredness.REQUIRED)
    private List<Long> shopCategoryIdList;

    @FieldDoc(description = "是否开启规格", requiredness = Requiredness.REQUIRED)
    private Boolean hasSku;

    @FieldDoc(description = "sku集合 单规格有一条默认值", requiredness = Requiredness.NONE)
    private List<ApiSaveProductSkuDto> skuList;

    @FieldDoc(description = "运费模板", requiredness = Requiredness.REQUIRED)
    private Long freightTemplateId;

    @FieldDoc(description = "重量(根据运费模板确定是否必填)", requiredness = Requiredness.NONE)
    private BigDecimal weight;

    @FieldDoc(description = "体积(根据运费模板确定是否必填)", requiredness = Requiredness.NONE)
    private BigDecimal volume;

    @FieldDoc(description = "主图集合(最多五张)", requiredness = Requiredness.REQUIRED)
    @JsonUrlFormat(deserializer = false)
    private List<String> imageList;

    @FieldDoc(description = "主图视频", requiredness = Requiredness.NONE)
    @JsonUrlFormat(deserializer = false)
    private String videoPath;

    @FieldDoc(description = "PC端描述", requiredness = Requiredness.REQUIRED)
    private String description;

    @FieldDoc(description = "移动端描述", requiredness = Requiredness.REQUIRED)
    private String mobileDescription;

    @FieldDoc(description = "顶部版式Id", requiredness = Requiredness.NONE)
    private Long descriptionPrefixId;

    @FieldDoc(description = "底部版式Id", requiredness = Requiredness.NONE)
    private Long descriptionSuffixId;

    @FieldDoc(description = "是否保存到草稿", requiredness = Requiredness.NONE)
    private Boolean draftFlag;

    @FieldDoc(description = "运费模板定价方式 0:按件数，1：按重量，2：按体积计算", requiredness = Requiredness.NONE)
    private Integer freightTemplateIdMethod;

}
