package com.sankuai.shangou.seashop.seller.thrift.core.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/12/05 17:11
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "商品导入入参")
public class ApiProductImportReq extends BaseParamReq {

    @FieldDoc(description = "文件路径", requiredness = Requiredness.REQUIRED)
    private String filePath;

    @FieldDoc(description = "销售状态 1-销售中 2-仓库中", requiredness = Requiredness.OPTIONAL)
    private Integer saleStatus;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isEmpty(filePath), "文件路径不能为空");
    }


}
