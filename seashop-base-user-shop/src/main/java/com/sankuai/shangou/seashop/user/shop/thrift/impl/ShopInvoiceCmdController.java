package com.sankuai.shangou.seashop.user.shop.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.user.shop.service.ShopInvoiceCmdService;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopInvoiceCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveShopInvoiceReq;

/**
 * @author： liweisong
 * @create： 2023/11/29 9:01
 */
@RestController
@RequestMapping("/shop/shopInvoice")
public class ShopInvoiceCmdController implements ShopInvoiceCmdFeign {

    @Resource
    private ShopInvoiceCmdService shopInvoiceCmdService;

    @PostMapping(value = "/saveShopInvoice", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> saveShopInvoice(@RequestBody SaveShopInvoiceReq saveShopInvoiceReq) throws TException {
        return ThriftResponseHelper.responseInvoke("saveShopInvoice", saveShopInvoiceReq, req -> {
            req.checkParameter();
            shopInvoiceCmdService.saveShopInvoice(req);
            return new BaseResp();
        });
    }
}
