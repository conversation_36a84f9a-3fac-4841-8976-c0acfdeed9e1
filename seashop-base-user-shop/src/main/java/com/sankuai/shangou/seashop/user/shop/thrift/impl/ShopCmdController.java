package com.sankuai.shangou.seashop.user.shop.thrift.impl;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.common.remote.base.BankRegionService;
import com.sankuai.shangou.seashop.user.shop.service.ShopService;
import com.sankuai.shangou.seashop.user.shop.task.ShopInfoTask;
import com.sankuai.shangou.seashop.user.thrift.account.response.TreeBankRegionResp;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 供应商服务类
 * @author: LXH
 **/
@Slf4j
@RestController
@RequestMapping("/shop/shop")
public class ShopCmdController implements ShopCmdFeign {
    @Resource
    private ShopService shopService;
    @Resource
    private BankRegionService bankRegionService;
    @Resource
    private ShopInfoTask shopInfoTask;

    @PostMapping(value = "/residencyApplication",consumes = "application/json")
    @Override
    public ResultDto<String> residencyApplication(@RequestBody CmdAgreementReq cmdAgreementReq) {
        return ThriftResponseHelper.responseInvoke("residencyApplication", cmdAgreementReq, req -> shopService.residencyApplication(cmdAgreementReq));

    }

    @PostMapping(value = "/sendCode",consumes = "application/json")
    @Override
    public ResultDto<BaseResp> sendCode(@RequestBody CmdSendCodeReq cmdSendCodeReq) throws TException {
        return ThriftResponseHelper.responseInvoke("sendCode", cmdSendCodeReq, req -> shopService.sendCode(cmdSendCodeReq));
    }

    @PostMapping(value = "/editBaseInfo",consumes = "application/json")
    @Override
    public ResultDto<Long> editBaseInfo(@RequestBody CmdShopStepsOneReq cmdShopStepsOneReq) {
        return ThriftResponseHelper.responseInvoke("editBaseInfo", cmdShopStepsOneReq, req -> shopService.editBaseInfo(cmdShopStepsOneReq));
    }

    @PostMapping(value = "/editBankInfo",consumes = "application/json")
    @Override
    public ResultDto<Long> editBankInfo(@RequestBody CmdShopStepsTwoReq cmdShopStepsTwoReq) {
        return ThriftResponseHelper.responseInvoke("editBankInfo", cmdShopStepsTwoReq, req -> shopService.editBankInfo(cmdShopStepsTwoReq));
    }

    @PostMapping(value = "/editCategoryInfo",consumes = "application/json")
    @Override
    public ResultDto<Long> editCategoryInfo(@RequestBody CmdShopStepsThreeReq cmdShopStepsThreeReq) {
        return ThriftResponseHelper.responseInvoke("editCategoryInfo", cmdShopStepsThreeReq, req -> shopService.editCategoryInfo(cmdShopStepsThreeReq));
    }

    @PostMapping(value = "/saveShippingSettings",consumes = "application/json")
    @Override
    public ResultDto<BaseResp> saveShippingSettings(@RequestBody ShippingSettingsSaveReq request) {
        return ThriftResponseHelper.responseInvoke("saveShippingSettings", request, req -> {
            req.checkParameter();
            shopService.saveShippingSettings(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/editShopPersonal",consumes = "application/json")
    @Override
    public ResultDto<Long> editShopPersonal(@Validated @RequestBody CmdShopReq cmdShopReq) {
        return ThriftResponseHelper.responseInvoke("editShopPersonal", cmdShopReq, req -> shopService.editShopPersonal(cmdShopReq));
    }

    @PostMapping(value = "/editShopEnterprise",consumes = "application/json")
    @Override
    public ResultDto<Long> editShopEnterprise(@Validated @RequestBody CmdShopReq cmdShopReq) {
        return ThriftResponseHelper.responseInvoke("editShopEnterprise", cmdShopReq, req -> shopService.editShopEnterprise(cmdShopReq));
    }

    @PostMapping(value = "/freezeShop",consumes = "application/json")
    @Override
    public ResultDto<Long> freezeShop(@Validated @RequestBody CmdShopStatusReq cmdShopStatusReq) {
        return ThriftResponseHelper.responseInvoke("freezeShop", cmdShopStatusReq, req -> shopService.freezeShop(cmdShopStatusReq));
    }

    @PostMapping(value = "/auditing",consumes = "application/json")
    @Override
    public ResultDto<BaseResp> auditing(@Validated @RequestBody CmdShopStatusReq cmdShopStatusReq) {
        return ThriftResponseHelper.responseInvoke("auditing", cmdShopStatusReq, req -> shopService.auditing(cmdShopStatusReq));
    }

    @PostMapping(value = "/sendDepositRemind", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> sendDepositRemind(@RequestBody BaseIdReq baseIdReq) throws TException {
        return ThriftResponseHelper.responseInvoke("sendDepositRemind", baseIdReq, req -> shopService.sendDepositRemind(baseIdReq));
    }

    @PostMapping(value = "/setShopType",consumes = "application/json")
    @Override
    public ResultDto<BaseResp> setShopType(@RequestBody ShopTypeCmdReq req) {
        return ThriftResponseHelper.responseInvoke("setShopType", req, r -> shopService.setShopType(req));
    }

    @PostMapping(value = "/createQR",consumes = "application/json")
    @Override
    public ResultDto<String> createQR(@RequestBody CmdCreateQRReq cmdCreateQRReq) {
        return ThriftResponseHelper.responseInvoke("createQR", cmdCreateQRReq, req -> shopService.createQR(cmdCreateQRReq));
    }

    @PostMapping(value = "/modifyBankInfo",consumes = "application/json")
    @Override
    public ResultDto<Long> modifyBankInfo(@RequestBody CmdShopStepsTwoReq cmdShopStepsTwoReq) throws TException {
        return ThriftResponseHelper.responseInvoke("modifyBankInfo", cmdShopStepsTwoReq, r -> shopService.modifyBankInfo(r));
    }

    @PostMapping(value = "/modifyBaseInfo",consumes = "application/json")
    @Override
    public ResultDto<Long> modifyBaseInfo(@RequestBody CmdShopStepsOneReq cmdShopBaseReq) throws TException {
        return ThriftResponseHelper.responseInvoke("modifyBaseInfo", cmdShopBaseReq, r -> shopService.modifyBaseInfo(r));
    }

    @PostMapping(value = "/modifyManagerInfo",consumes = "application/json")
    @Override
    public ResultDto<Long> modifyManagerInfo(@RequestBody CmdShopManagerReq cmdShopManagerReq) throws TException {
        return ThriftResponseHelper.responseInvoke("modifyManagerInfo", cmdShopManagerReq, r -> shopService.modifyManagerInfo(r));
    }

    @GetMapping(value = "/getBankRegion")
    @Override
    public ResultDto<String> getBankRegion() throws TException {
        return ThriftResponseHelper.responseInvoke("getBankRegion", null, r -> bankRegionService.getTreeRegions());
    }

    @PostMapping(value = "/getRegionByParentId",consumes = "application/json")
    @Override
    public ResultDto<List<TreeBankRegionResp>> getRegionByParentId(@RequestBody BaseIdReq baseIdReq) throws TException {
        return ThriftResponseHelper.responseInvoke("getRegionByParentId", baseIdReq, r -> JsonUtil.copyList(bankRegionService.getRegionByParentId(r),TreeBankRegionResp.class));
    }

    @PostMapping(value = "/getRegionsById",consumes = "application/json")
    @Override
    public ResultDto<List<TreeBankRegionResp>> getRegionsById(@RequestBody BaseIdReq regionId) throws TException {
        return ThriftResponseHelper.responseInvoke("getRegionsById", regionId, r -> JsonUtil.copyList(bankRegionService.getRegionsById(r),TreeBankRegionResp.class));
    }

    @PostMapping(value = "/updateSeq", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> updateSeq(@RequestBody CmdShopSeqReq cmdShopSeqReq) throws TException {
        return ThriftResponseHelper.responseInvoke("updateSeq", cmdShopSeqReq, r -> {
            if (CollUtil.isNotEmpty(r.getShopIdList())) {
                r.getShopIdList().forEach(shopId -> {
                    CmdSingleShopSeqReq req = new CmdSingleShopSeqReq();
                    req.setShopId(shopId);
                    req.setSerialNumber(r.getSerialNumber());
                    shopService.updateSeq(req);
                });
            }
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/checkShopArrears", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> checkShopArrears(@RequestBody BaseIdReq baseIdReq) throws TException {
        return ThriftResponseHelper.responseInvoke("checkShopArrears", baseIdReq, r -> {
            shopInfoTask.recalculateArrears(CollUtil.toList(r.getId()));
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/setRegisterState", consumes = "application/json")
    @Override
    public ResultDto<Boolean> setRegisterState(@RequestBody BaseIdReq baseIdReq) throws TException {
        return ThriftResponseHelper.responseInvoke("setRegisterState", baseIdReq, r -> shopService.setRegisterState(r.getId()));
    }

    @GetMapping(value = "/goExpressBills")
    @Override
    public ResultDto<String> goExpressBills(Long shopId) throws TException {
        return ThriftResponseHelper.responseInvoke("goExpressBills", shopId, r -> shopService.goExpressBills(r));
    }
}
