package com.sankuai.shangou.seashop.user.shop.thrift.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.user.shop.service.ShopService;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.EnoughCashFlagReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ProductShopQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopEsQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopQueryPagerReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopSimpleQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ProductShopInfoResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShippingSettingsResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopDetailResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopEsResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopIdsResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopIntroductionResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopRespList;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleListResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopUserCountResp;

/**
 * @description: 供应商服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/shop/shop")
public class ShopQueryController implements ShopQueryFeign {

    @Resource
    private ShopService shopService;

    @PostMapping(value = "/getShopList", consumes = "application/json")
    @Override
    public ResultDto<ShopRespList> getShopList(@RequestBody QueryShopPageReq queryShopPageReq) {
        return ThriftResponseHelper.responseInvoke("getShopList", queryShopPageReq, req -> shopService.getShopList(queryShopPageReq));
    }

    @PostMapping(value = "/getShopIds", consumes = "application/json")
    @Override
    public ResultDto<ShopIdsResp> getShopIds(@RequestBody QueryShopReq queryShopReq) {
        return ThriftResponseHelper.responseInvoke("getList", queryShopReq, req -> shopService.getShopIds(queryShopReq));
    }

    @PostMapping(value = "/getShippingSettings", consumes = "application/json")
    @Override
    public ResultDto<ShippingSettingsResp> getShippingSettings(@RequestBody BaseIdReq request) {
        return ThriftResponseHelper.responseInvoke("getShippingSettings", request,
                req -> shopService.getShippingSettings(request.getId())
        );
    }

    @PostMapping(value = "/queryPage", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<ShopResp>> queryPage(@RequestBody ShopQueryPagerReq request) {
        return ThriftResponseHelper.responseInvoke("selectList", request,
                req -> shopService.queryPage(request)
        );
    }

    @PostMapping(value = "/queryShopCategoryDetail", consumes = "application/json")
    @Override
    public ResultDto<String> queryShopCategoryDetail(@RequestBody BaseIdReq shopId) {
        return ThriftResponseHelper.responseInvoke("queryShopCategoryDetail", shopId,
            req -> shopService.queryShopCategoryDetail(req)
        );
    }

    @PostMapping(value = "/queryDetail", consumes = "application/json")
    @Override
    public ResultDto<ShopDetailResp> queryDetail(@RequestBody BaseIdReq shopId) {
        return ThriftResponseHelper.responseInvoke("queryDetail", shopId,
                req -> shopService.queryDetail(shopId)
        );
    }

    @PostMapping(value = "/queryDetailIncludeFreeze", consumes = "application/json")
    @Override
    public ResultDto<ShopDetailResp> queryDetailIncludeFreeze(@RequestBody BaseIdReq shopId) throws TException {
        return ThriftResponseHelper.responseInvoke("queryDetailIncludeFreeze", shopId,
                req -> shopService.queryDetailIncludeFreeze(shopId)
        );
    }

    @PostMapping(value = "/querySimpleList", consumes = "application/json")
    @Override
    public ResultDto<ShopSimpleListResp> querySimpleList(@RequestBody ShopSimpleQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("querySimpleList", request,
                req -> shopService.querySimpleList(request)
        );
    }

    @PostMapping(value = "/querySimplePage", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<ShopSimpleResp>> querySimplePage(@RequestBody ShopQueryPagerReq queryPagerReq) throws TException {
        return ThriftResponseHelper.responseInvoke("querySimplePage", queryPagerReq,
                req -> shopService.querySimplePage(queryPagerReq)
        );
    }

    @PostMapping(value = "/queryProductShop", consumes = "application/json")
    @Override
    public ResultDto<ProductShopInfoResp> queryProductShop(@RequestBody ProductShopQueryReq baseIdReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductShop", baseIdReq,
                req -> shopService.queryProductShop(baseIdReq)
        );
    }

    @PostMapping(value = "/queryShopsByIds", consumes="application/json")
    public ResultDto<List<ShopResp>> queryShopsByIds(@RequestBody ShopQueryReq request) throws TException {
        request.checkParameter();
        return ThriftResponseHelper.responseInvoke("queryShopsByIds", request, req -> shopService.queryShopsByIds(req));
    }

    @GetMapping(value = "/countShopUser")
    @Override
    public ResultDto<ShopUserCountResp> countShopUser() throws TException {
        return ThriftResponseHelper.responseInvoke("countShopUser", null, req -> shopService.countShopUser());
    }

    @PostMapping(value = "/queryByShopEs", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<ShopEsResp>> queryByShopEs(@RequestBody ShopEsQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryByShopEs", request, req -> shopService.queryByShopEs(req));
    }

    @GetMapping(value = "/getYesterdayShopUV")
    @Override
    public ResultDto<Long> getYesterdayShopUV(@RequestParam Long shopId) {
        return ThriftResponseHelper.responseInvoke("getYesterdayShopUV", shopId, req -> shopService.getYesterdayShopUV(shopId));
    }

    @PostMapping(value = "/shopIntroduction", consumes = "application/json")
    @Override
    public ResultDto<ShopIntroductionResp> shopIntroduction(@RequestBody BaseIdReq baseIdReq) {
        return ThriftResponseHelper.responseInvoke("shopIntroduction", baseIdReq, req -> shopService.shopIntroduction(baseIdReq));
    }

    @GetMapping(value = "/selfShopInfo")
    @Override
    public ResultDto<ShopSimpleResp> selfShopInfo() throws TException {
        return ThriftResponseHelper.responseInvoke("shopIntroduction", null, req -> shopService.selfShopInfo());
    }

    @PostMapping(value = "/shopInfoByUserId", consumes = "application/json")
    @Override
    public ResultDto<ShopSimpleResp> shopInfoByUserId(@RequestBody BaseIdReq baseIdReq) throws TException {
        return ThriftResponseHelper.responseInvoke("shopInfoByUserId", baseIdReq, req -> shopService.shopInfoByUserId(baseIdReq));
    }

    @PostMapping(value = "/enoughCashFlag", consumes = "application/json")
    @Override
    public ResultDto<Boolean> enoughCashFlag(@RequestBody EnoughCashFlagReq enoughCashFlagReq) throws TException {
        return ThriftResponseHelper.responseInvoke("enoughCashFlag", enoughCashFlagReq, req -> shopService.enoughCashFlag(req));
    }

    @PostMapping(value = "/queryShop", consumes = "application/json")
    @Override
    public ResultDto<ShopResp> queryShop(@RequestBody BaseIdReq shopId) {
        return ThriftResponseHelper.responseInvoke("queryShop", shopId, req -> shopService.queryShop(shopId));
    }


}
