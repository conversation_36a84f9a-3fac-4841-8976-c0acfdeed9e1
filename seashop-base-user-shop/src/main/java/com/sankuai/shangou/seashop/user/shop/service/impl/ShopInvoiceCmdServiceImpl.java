package com.sankuai.shangou.seashop.user.shop.service.impl;

import java.util.Date;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminProcess;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.user.dao.shop.domain.ShopInvoiceConfig;
import com.sankuai.shangou.seashop.user.dao.shop.repository.ShopInvoiceRepository;
import com.sankuai.shangou.seashop.user.shop.service.ShopInvoiceCmdService;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveShopInvoiceReq;

/**
 * @author： liweisong
 * @create： 2023/11/29 9:04
 */
@Service
public class ShopInvoiceCmdServiceImpl implements ShopInvoiceCmdService {

    @Resource
    private ShopInvoiceRepository shopInvoiceRepository;

    @Override
    @ExaminProcess(operationUserId = "operationUserId", shopId = "shopId",
        actionName = "发票管理保存", processModel = ExaminModelEnum.USER,
            processType = ExaProEnum.MODIFY, repository = "shopInvoiceRepository",
            serviceMethod = "saveShopInvoice", dto = SaveShopInvoiceReq.class, entity = ShopInvoiceConfig.class)
    public void saveShopInvoice(SaveShopInvoiceReq saveShopInvoiceReq) {
        //普通税率：设置税率之后，商家索要普通发票、电子普通发票时需要支付额外的税费。税费=商品支付金额（不包括运费）*税率。0～100，百分比填写，支持小数点后两位
        //订单结束交易超过X天开具增值税发票：订单完成后X天开具增值税发，可以为0，最大999999
        //增值税税率：设置税率之后，商家索要增值税发票时需要支付额外的税费。税费=商品支付金额（不包括运费）*税率，0～100，百分比填写，支持小数点后两位
        if(saveShopInvoiceReq.getWhetherInvoice()) {
            AssertUtil.throwIfTrue(saveShopInvoiceReq.getPlainInvoiceRate().doubleValue() < 0 || saveShopInvoiceReq.getPlainInvoiceRate().doubleValue() > 100, "普通税率0～100之间，百分比填写，支持小数点后两位");
            AssertUtil.throwIfTrue(saveShopInvoiceReq.getVatInvoiceDay() < 0 || saveShopInvoiceReq.getVatInvoiceDay() > 999999, "订单完成后X天开具增值税发，可以为0，最大999999");
            AssertUtil.throwIfTrue(saveShopInvoiceReq.getVatInvoiceRate().doubleValue() < 0 || saveShopInvoiceReq.getVatInvoiceRate().doubleValue() > 100, "增值税率0～100之间，百分比填写，支持小数点后两位");
        } else {
            saveShopInvoiceReq.setWhetherElectronicInvoice(false);
            saveShopInvoiceReq.setWhetherVatInvoice(false);
            saveShopInvoiceReq.setWhetherPlainInvoice(false);
        }
//        ShopInvoiceConfig shopInvoiceConfig = new ShopInvoiceConfig();
//        shopInvoiceConfig.setId(saveShopInvoiceReq.getId());
//        shopInvoiceConfig.setShopId(saveShopInvoiceReq.getShopId());
//        shopInvoiceConfig.setWhetherInvoice(saveShopInvoiceReq.getWhetherInvoice());
//        shopInvoiceConfig.setWhetherPlainInvoice(saveShopInvoiceReq.getWhetherPlainInvoice());
//        shopInvoiceConfig.setWhetherElectronicInvoice(saveShopInvoiceReq.getWhetherElectronicInvoice());
//        shopInvoiceConfig.setPlainInvoiceRate(saveShopInvoiceReq.getPlainInvoiceRate());
//        shopInvoiceConfig.setWhetherVatInvoice(saveShopInvoiceReq.getWhetherVatInvoice());
//        shopInvoiceConfig.setVatInvoiceDay(saveShopInvoiceReq.getVatInvoiceDay());
//        shopInvoiceConfig.setVatInvoiceRate(saveShopInvoiceReq.getVatInvoiceRate());
//        shopInvoiceConfig.setCreateTime(saveShopInvoiceReq.getCreateTime());

        ShopInvoiceConfig shopInvoiceConfig = JsonUtil.copy(saveShopInvoiceReq, ShopInvoiceConfig.class);
        shopInvoiceConfig.setUpdateTime(new Date());
        shopInvoiceRepository.save(shopInvoiceConfig);
    }
}
