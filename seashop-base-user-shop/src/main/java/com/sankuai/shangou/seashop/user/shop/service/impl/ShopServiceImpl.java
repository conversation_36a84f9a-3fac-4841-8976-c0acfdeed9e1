package com.sankuai.shangou.seashop.user.shop.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.hishop.starter.storage.client.StorageClient;
import com.hishop.starter.storage.model.StorageFileInfo;
import com.hishop.starter.storage.util.StorageUtil;
import com.hishop.starter.util.HishopErpSignUtil;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminProcess;
import com.sankuai.shangou.seashop.base.boot.log.assist.BaseLogAssist;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AesUtil;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.CustomFormService;
import com.sankuai.shangou.seashop.base.core.service.RegionService;
import com.sankuai.shangou.seashop.base.core.service.SiteSettingRemoteService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomFormField;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegion;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSettled;
import com.sankuai.shangou.seashop.base.leaf.LeafService;
import com.sankuai.shangou.seashop.base.lock.DistributedLockService;
import com.sankuai.shangou.seashop.base.lock.model.LockKey;
import com.sankuai.shangou.seashop.base.thrift.core.MessageCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.RegionQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.ContactReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.RegionIdsReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SmsBodyReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.AllPathRegionResp;
import com.sankuai.shangou.seashop.base.utils.SquirrelUtil;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.base.utils.ValidatorHelper;
import com.sankuai.shangou.seashop.order.thrift.core.OrderCommentQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.response.ShopMarkResp;
import com.sankuai.shangou.seashop.order.thrift.finance.CashDepositQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.ShopAccountCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.ShopAccountQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CreateAccountReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.ShopIdReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositResp;
import com.sankuai.shangou.seashop.pay.thrift.core.dto.AdaPayConfigModelDto;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayCorpMemberReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayMemberReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PaySettleAccountReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.UpdatePaySettleAccountReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.dto.PaySettleAccountInfoDto;
import com.sankuai.shangou.seashop.pay.thrift.core.response.PaySettleAccountResp;
import com.sankuai.shangou.seashop.pay.thrift.core.service.ChannelConfigQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.ProductQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductQueryDetailReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductDetailResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductDetailDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.ShopUserPromotionCmdFeign;
import com.sankuai.shangou.seashop.user.account.dto.ShopMemberInfoBO;
import com.sankuai.shangou.seashop.user.account.service.ManagerService;
import com.sankuai.shangou.seashop.user.account.service.MemberContactService;
import com.sankuai.shangou.seashop.user.common.config.EncryptConfig;
import com.sankuai.shangou.seashop.user.common.config.UserLionConfigClient;
import com.sankuai.shangou.seashop.user.common.constant.*;
import com.sankuai.shangou.seashop.user.common.enums.SmsEnum;
import com.sankuai.shangou.seashop.user.common.enums.UserResultCodeEnum;
import com.sankuai.shangou.seashop.user.common.enums.account.ContactUserTypeEnum;
import com.sankuai.shangou.seashop.user.common.es.model.shop.EsShopModel;
import com.sankuai.shangou.seashop.user.common.es.model.shop.EsShopParam;
import com.sankuai.shangou.seashop.user.common.es.service.EsShopService;
import com.sankuai.shangou.seashop.user.common.model.TreeRegionBO;
import com.sankuai.shangou.seashop.user.common.remote.base.BankRegionService;
import com.sankuai.shangou.seashop.user.common.remote.base.MessageRemoteService;
import com.sankuai.shangou.seashop.user.common.remote.pay.FinanceMemberRemoteService;
import com.sankuai.shangou.seashop.user.common.remote.product.RemoteCategoryService;
import com.sankuai.shangou.seashop.user.common.remote.product.model.RemoteCategoryBo;
import com.sankuai.shangou.seashop.user.dao.account.domain.Manager;
import com.sankuai.shangou.seashop.user.dao.account.domain.Member;
import com.sankuai.shangou.seashop.user.dao.account.domain.MemberContact;
import com.sankuai.shangou.seashop.user.dao.account.repository.ManagerRepository;
import com.sankuai.shangou.seashop.user.dao.account.repository.MemberContactRepository;
import com.sankuai.shangou.seashop.user.dao.account.repository.MemberRepository;
import com.sankuai.shangou.seashop.user.dao.shop.domain.*;
import com.sankuai.shangou.seashop.user.dao.shop.model.ShopModel;
import com.sankuai.shangou.seashop.user.dao.shop.model.ShopQueryModel;
import com.sankuai.shangou.seashop.user.dao.shop.repository.ShopExtRepository;
import com.sankuai.shangou.seashop.user.dao.shop.repository.ShopFreeShippingAreaRepository;
import com.sankuai.shangou.seashop.user.dao.shop.repository.ShopRepository;
import com.sankuai.shangou.seashop.user.shop.helper.LogoHelper;
import com.sankuai.shangou.seashop.user.shop.log.ShopLogBO;
import com.sankuai.shangou.seashop.user.shop.mq.publisher.ShopMafkaProducer;
import com.sankuai.shangou.seashop.user.shop.service.*;
import com.sankuai.shangou.seashop.user.shop.service.assist.FormFieldAssistant;
import com.sankuai.shangou.seashop.user.shop.service.assist.ShopAssistant;
import com.sankuai.shangou.seashop.user.shop.service.model.AuditBusinessCategoryApplyBo;
import com.sankuai.shangou.seashop.user.shop.service.model.BusinessCategoryApplyBo;
import com.sankuai.shangou.seashop.user.shop.service.model.BusinessCategoryFormBo;
import com.sankuai.shangou.seashop.user.thrift.account.enums.MemberContactEnum;
import com.sankuai.shangou.seashop.user.thrift.account.request.BindContactCmdReq;
import com.sankuai.shangou.seashop.user.thrift.shop.constant.ShopConstant;
import com.sankuai.shangou.seashop.user.thrift.shop.constant.ShopSquirrelConstant;
import com.sankuai.shangou.seashop.user.thrift.shop.dto.ShopFreeShippingAreaDto;
import com.sankuai.shangou.seashop.user.thrift.shop.enums.ShopEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.enums.ShopTypeEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.enums.VerificationTypeEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.group.AddCompanyGroup;
import com.sankuai.shangou.seashop.user.thrift.shop.group.AddPersonGroup;
import com.sankuai.shangou.seashop.user.thrift.shop.request.*;
import com.sankuai.shangou.seashop.user.thrift.shop.response.*;
import com.sankuai.shangou.seashop.user.thrift.shop.response.dto.CategoryApplyFormDto;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 权限服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class ShopServiceImpl implements ShopService {
    private final static String DEFAULT_ENCRYPTION_MODE = "SHA-256";
    @Value("${weiXin.envVersion:}")
    public String envVersion;
    @Resource
    private ShopRepository shopRepository;
    @Resource
    private ShopAssistant shopAssistant;
    @Resource
    private ShopExtRepository shopExtRepository;
    @Resource
    private MemberRepository memberRepository;
    @Resource
    private ManagerService managerService;
    @Resource
    private ManagerRepository managerRepository;
    @Resource
    private BusinessCategoryApplyService businessCategoryApplyService;
    @Resource
    private ShopFreeShippingAreaRepository shopFreeShippingAreaRepository;
    @Resource
    private BusinessCategoryService businessCategoryService;
    @Resource
    private BusinessCategoryFormService businessCategoryFormService;
    @Resource
    private ShopMafkaProducer shopMafkaProducer;
    @Resource
    private SiteSettingRemoteService siteSettingRemoteService;
    @Resource
    private MessageRemoteService messageRemoteService;
    @Resource
    private MessageCMDFeign messageCMDFeign;
    @Resource
    private LeafService leafService;
    @Resource
    private RegionService regionService;
    @Resource
    private RemoteCategoryService remoteCategoryService;
    @Resource
    private StorageClient storageClient;
    @Resource
    private EsShopService esShopService;
    @Resource
    private SquirrelUtil squirrelUtil;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private WxMaService wxMaService;
    @Resource
    private MemberContactService memberContactService;
    @Resource
    private FormFieldAssistant formFieldAssistant;
    @Resource
    private BankRegionService bankRegionService;
    @Resource
    private ShopAccountCmdFeign shopAccountCmdFeign;
    @Resource
    private EncryptConfig encryptConfig;
    @Resource
    private CustomFormService customFormService;

    @Resource
    private RegionQueryFeign regionQueryFeign;
    @Resource
    private MemberContactRepository memberContactRepository;

    @Resource
    private EsShopBuildService esShopBuildService;
    //电子面单相关配置begin
    @Value("${current.domain.url:}")
    private String currentDomainUrl;
    @Value("${hishop.erp.url:}")
    private String erpUri;
    //end
    @Resource
    private BaseLogAssist baseLogAssist;
    @Resource
    private DistributedLockService distributedLockService;
    @Resource
    private UserLionConfigClient userLionConfigClient;
    @Resource
    private CashDepositQueryFeign cashDepositQueryFeign;
    @Resource
    private OrderCommentQueryFeign orderCommentQueryFeign;
    @Resource
    private ShopAccountQueryFeign shopAccountQueryFeign;
    @Resource
    private ProductQueryFeign productQueryFeign;
    @Resource
    private FinanceMemberRemoteService financeMemberRemoteService;
    @Resource
    private ShopUserPromotionCmdFeign shopUserPromotionCmdFeign;
    @Resource
    private ChannelConfigQueryFeign channelConfigQueryFeign;

    public static void main(String[] args) {
//        System.out.println(AesUtil.decrypt("W08kXfP2oakOmLQ3BYT3CQ==", "ae125efkk4454eeff444ferfkny6oxi8"));
        Long a = 6206L;
        String format = String.format("%03d", a);
        System.out.println(format);
//        try {
//            File.createTempFile("001", CommonConstant.BANK_PHOTO_SUFFIX);
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
    }

    @Override
    public ShopIdsResp getShopIds(QueryShopReq queryShopReq) {
        Shop shop = new Shop();
        shop.setShopName(queryShopReq.getShopName());
        List<Shop> shopIds = shopRepository.getList(shop, null);
        if (CollectionUtil.isEmpty(shopIds)) {
            return new ShopIdsResp();
        }
        return new ShopIdsResp(shopIds.stream().map(Shop::getId).collect(Collectors.toList()));
    }

    private Manager addSellerManager(Member member) {
        Manager manager = managerRepository.getByUserName(member.getUserName());
        if (manager != null) {
            return manager;
        }
        Shop shop = createEmptyShop(member);
        Date createDate = new Date();
        manager = new Manager();
        manager.setCreateTime(createDate);
        manager.setUpdateTime(createDate);
        manager.setCreateUser(ManagerConstant.DEFAULT_OPERATE_ID);
        manager.setUpdateUser(ManagerConstant.DEFAULT_OPERATE_ID);
        manager.setUserName(member.getUserName());
        manager.setPassword(member.getPassword());
        manager.setPasswordSalt(member.getPasswordSalt());
        manager.setEncryptionMode(DEFAULT_ENCRYPTION_MODE);
        manager.setShopId(shop.getId());
        manager.setRoleId(ManagerConstant.DEFAULT_ROLE_ID);
        managerRepository.insert(manager);
        return manager;
    }

    @Override
    public Shop createEmptyShop(Member member) {
        Shop shop = new Shop();
        Date createDate = new Date();
        shop.setShopName("");
        shop.setGradeId(0L);
        shop.setWhetherSelf(false);
        shop.setShopStatus(ShopEnum.AuditStatus.Unusable.getCode());
        shop.setCreateDate(createDate);
        shop.setCreateTime(createDate);
        shop.setUpdateTime(createDate);
        shop.setBankRegionId(0);
        shop.setFreeFreight(BigDecimal.ZERO);
        shop.setFreight(BigDecimal.ZERO);
        shop.setStage(ShopEnum.ShopStage.Agreement.getCode());
        shop.setPlateStatus(ShopEnum.PlatAuditStatus.Unusable.getCode());
        shop.setAdapayStatus(ShopEnum.AdapayAuditStatus.AdapayNoRequest.getCode());
        shop.setAutoAllotOrder(false);
        shop.setLogo(CommonConstant.DEFAULT_IMAGE);

        shopRepository.save(shop);
        ShopExt shopExt = new ShopExt();
        shopExt.setShopId(shop.getId());
        shopExt.setCompanyRegionId(CommonConstant.DEFAULT_INT_ID);
        shopExt.setCompanyEmployeeCount(ShopEnum.CompanyEmployeeCount.LessThanFive.getCode());
        shopExt.setCompanyRegisteredCapital(BigDecimal.ZERO);
        shopExt.setBusinessLicenseNumberPhoto("");
        shopExt.setBusinessLicenseRegionId(CommonConstant.DEFAULT_INT_ID);
        shopExt.setCreateTime(createDate);
        shopExt.setUpdateTime(createDate);
        shopExtRepository.save(shopExt);
        return shop;

    }

    private void checkSettledInfo(CmdShopStepsOneReq cmdShopStepsOneReq, Member member, ShopEnum.BusinessType businessType) {
        //获取入驻信息
        BaseSettled settledRes = siteSettingRemoteService.getSettled();
        //如果联系方式未变动不校验验证码
        String phone = memberContactService.queryPhone(member.getId());
        String email = memberContactService.queryEmail(member.getId());
        if (StrUtil.equals(phone, StrUtil.nullToEmpty(cmdShopStepsOneReq.getMemberPhone()))
                && StrUtil.equals(email, StrUtil.nullToEmpty(cmdShopStepsOneReq.getMemberEmail()))) {
            return;
        }
//        判断业务类型
        if (businessType != ShopEnum.BusinessType.PERSONAL) {
            ValidatorHelper.validate(cmdShopStepsOneReq, AddCompanyGroup.class);
//            验证验证码
            AssertUtil.throwIfTrue(!verificationCode(cmdShopStepsOneReq, Objects.requireNonNull(VerificationTypeEnum.getEnumByCode(settledRes.getCompanyVerificationType()))), UserResultCodeEnum.VERIFICATION_CODE_ERROR);
        } else {
            ValidatorHelper.validate(cmdShopStepsOneReq, AddPersonGroup.class);//            验证验证码
            AssertUtil.throwIfTrue(!verificationCode(cmdShopStepsOneReq, Objects.requireNonNull(VerificationTypeEnum.getEnumByCode(settledRes.getSelfVerificationType()))), UserResultCodeEnum.VERIFICATION_CODE_ERROR);
        }
    }

    @Override
    public String residencyApplication(CmdAgreementReq cmdAgreementReq) {
        if (cmdAgreementReq.getAgree()) {
            //获取商家信息
            Member member = memberRepository.getById(cmdAgreementReq.getUserId());
            Manager manager = addSellerManager(member);
            Shop model = shopRepository.getById(manager.getShopId());
            AssertUtil.throwIfNull(model, "当前账号无法申请入驻, 如有疑问请联系管理员");
            //已经提交汇付审核的不能修改入驻类型
            PayMemberReq payMemberReq = new PayMemberReq();
            payMemberReq.setMemberId(model.getId().toString());
            payMemberReq.setPaymentChannel(ShopConstant.PAYMENT_CHANNEL);
            // 汇付配置
            AdaPayConfigModelDto adaPayConfigModelDto = ThriftResponseHelper.executeThriftCall(() -> channelConfigQueryFeign.getAdaPayConfigModel());
            if (Boolean.TRUE.equals(adaPayConfigModelDto.getIzOpen())) {
                // 汇付渠道开启了才需要进行校验
                //已经存在汇付id
                if (financeMemberRemoteService.queryMember(payMemberReq)) {
                    //                    判断类型是否有变更
                    if (!model.getBusinessType().equals(cmdAgreementReq.getBusinessType())) {
                        throw new BusinessException(UserResultCodeEnum.SHOP_BUSINESS_TYPE_ERROR);
                    }
                }
            }

            // 申请类型有变动 重置步骤
            if (!model.getBusinessType().equals(cmdAgreementReq.getBusinessType())) {
                model.setStage(ShopEnum.ShopStage.CompanyInfo.getCode());
            }

            if (ShopEnum.ShopStage.Agreement.getCode().equals(model.getStage())) {
                model.setBusinessType(cmdAgreementReq.getBusinessType());
                model.setStage(ShopEnum.ShopStage.CompanyInfo.getCode());
            } else {
                model.setBusinessType(cmdAgreementReq.getBusinessType());
            }

            shopRepository.updateById(model);
            return model.getId().toString();
        } else {
            return AgreementConst.EDIT_PROFILE_ZERO;
        }
    }

    @Override
    public Long editBaseInfo(CmdShopStepsOneReq cmdShopStepsOneReq) {
//        获取供应商信息
        Shop shop = shopRepository.getById(cmdShopStepsOneReq.getShopId());        //获取商家信息
        Manager manager = managerRepository.getByShopId(cmdShopStepsOneReq.getShopId());
        Member member = memberRepository.queryMemberByUserName(manager.getUserName());
        //校验入驻信息
        checkSettledInfo(cmdShopStepsOneReq, member, ShopEnum.BusinessType.getEnumByCode(shop.getBusinessType()));

        //公司信息
        ShopExt shopExt = shopExtRepository.getByShopId(cmdShopStepsOneReq.getShopId());
        shop.setId(cmdShopStepsOneReq.getShopId());
        shop.setStage(ShopEnum.ShopStage.FinancialInfo.getCode());

        shopExt.setCompanyRegionId(cmdShopStepsOneReq.getCompanyRegionId());
        shopExt.setCompanyAddress(cmdShopStepsOneReq.getCompanyAddress());
        shop.setIdCard(AesUtil.encrypt(cmdShopStepsOneReq.getIdCard(), encryptConfig.getAesSecret()));
        shop.setIdCardurl(cmdShopStepsOneReq.getIdCardUrl());
        shop.setIdCardurl2(cmdShopStepsOneReq.getIdCardUrl2());
        shop.setIdCardExpireType(cmdShopStepsOneReq.getIdCardExpireType());
        shop.setIdCardStartDate(cmdShopStepsOneReq.getIdCardStartDate());
        shop.setIdCardEndDate(cmdShopStepsOneReq.getIdCardEndDate());
        shop.setFormData(cmdShopStepsOneReq.getFormData());
        if (ShopEnum.BusinessType.ENTERPRISE.getCode().equals(shop.getBusinessType())) {
            shop.setContactsName(cmdShopStepsOneReq.getRealName());
            shop.setContactsPhone(cmdShopStepsOneReq.getContactsPhone());
            shop.setContactsEmail(cmdShopStepsOneReq.getMemberEmail());
            shop.setBankPhoto(cmdShopStepsOneReq.getBankPhoto());
            shopExt.setCompanyName(cmdShopStepsOneReq.getCompanyName());
            shopExt.setLegalPerson(cmdShopStepsOneReq.getLegalPerson());
            shopExt.setBusinessLicenseNumber(cmdShopStepsOneReq.getBusinessLicenseNumber());
            shopExt.setBusinessLicenseStart(cmdShopStepsOneReq.getBusinessLicenseStart());
            shopExt.setBusinessLicenseEnd(cmdShopStepsOneReq.getBusinessLicenseEnd());
            shopExt.setBusinessSphere(cmdShopStepsOneReq.getBusinessSphere());
            shopExt.setBusinessLicenseNumberPhoto(cmdShopStepsOneReq.getBusinessLicenseNumberPhoto());
        } else {
            shop.setContactsName(cmdShopStepsOneReq.getCompanyName());
            shop.setContactsPhone(cmdShopStepsOneReq.getMemberPhone());
            shop.setContactsEmail(cmdShopStepsOneReq.getMemberEmail());
            shopExt.setCompanyName(cmdShopStepsOneReq.getCompanyName());
        }
        TransactionHelper.doInTransaction(() -> {
            shopRepository.updateById(shop);
            shopExtRepository.updateById(shopExt);


            member.setRealName(cmdShopStepsOneReq.getRealName());
            memberRepository.editMember(member);

            //绑定联系方式
            bindContact(cmdShopStepsOneReq, member, shop.getBusinessType());
        });

        return shop.getId();
    }

    @Override
    public Long editBankInfo(CmdShopStepsTwoReq cmdShopStepsTwoReq) {
        Shop shop = shopRepository.getById(cmdShopStepsTwoReq.getShopId());
        setBankInfo(shop, cmdShopStepsTwoReq);
        shop.setStage(ShopEnum.ShopStage.ShopInfo.getCode());
        TransactionHelper.doInTransaction(() -> {
            shopRepository.updateById(shop);
        });
        return shop.getId();
    }

    @Override
    public Long editCategoryInfo(CmdShopStepsThreeReq cmdShopStepsThreeReq) {
        Shop shopName = shopRepository.getByShopName(cmdShopStepsThreeReq.getShopName());
        if (shopName != null && !shopName.getId().equals(cmdShopStepsThreeReq.getShopId())) {
            throw new BusinessException(UserResultCodeEnum.SHOP_NAME_ALREADY_EXISTS);
        }
        Shop shop = shopRepository.getById(cmdShopStepsThreeReq.getShopId());
        //类目数据
        List<Long> subCategoryIds = new ArrayList<>();
        cmdShopStepsThreeReq.getCategories().forEach(categoryId -> {
            remoteCategoryService.queryLastCategoryList(categoryId).forEach(remoteCategoryBo -> {
                subCategoryIds.add(remoteCategoryBo.getId());
            });
        });
        //自定义表单数据
        List<BusinessCategoryFormBo> businessCategoryFormBos = new ArrayList<>();
        if (!CollectionUtil.isEmpty(cmdShopStepsThreeReq.getCustomFormCategory())) {
            for (CmdBusinessCategoryFormReq cmdBusinessCategoryFormReq : cmdShopStepsThreeReq.getCustomFormCategory()) {
                BusinessCategoryFormBo businessCategoryFormBo = new BusinessCategoryFormBo();
                businessCategoryFormBo.setFormId(cmdBusinessCategoryFormReq.getFormId());
                businessCategoryFormBo.setFormName(cmdBusinessCategoryFormReq.getFormName());
                businessCategoryFormBo.setCategoryId(cmdBusinessCategoryFormReq.getCategoryId());
                businessCategoryFormBo.setFieldList(formFieldAssistant.transToFieldList(cmdBusinessCategoryFormReq.getCustomJson()));
                businessCategoryFormBos.add(businessCategoryFormBo);
            }
        }
        shop.setShopName(cmdShopStepsThreeReq.getShopName());
        shop.setPlateStatus(ShopEnum.PlatAuditStatus.WaitAudit.getCode());
        shop.setShopStatus(ShopEnum.AuditStatus.WaitAudit.getCode());
        shop.setStage(ShopEnum.ShopStage.UploadPayOrder.getCode());
        // 汇付配置
        AdaPayConfigModelDto adaPayConfigModelDto = ThriftResponseHelper.executeThriftCall(() -> channelConfigQueryFeign.getAdaPayConfigModel());
        if (Boolean.TRUE.equals(adaPayConfigModelDto.getIzOpen())) {
            // 汇付渠道开启了才需要进行校验/创建
            //判断业务状态
            if (ShopEnum.BusinessType.PERSONAL.getCode().equals(shop.getBusinessType())) {
                //汇付账号
                PayMemberReq adaPayMemberReq = new PayMemberReq();
                adaPayMemberReq.setMemberId(cmdShopStepsThreeReq.getShopId().toString());
                adaPayMemberReq.setPaymentChannel(ShopConstant.PAYMENT_CHANNEL);
                financeMemberRemoteService.createMember(adaPayMemberReq);
                updateSettleAccount(shop);
                shop.setAdapayStatus(ShopEnum.AdapayAuditStatus.AdapaySuccess.getCode());
            } else {
                //查询店铺拓展信息
                ShopExt shopExt = shopExtRepository.getByShopId(cmdShopStepsThreeReq.getShopId());
                PayCorpMemberReq adaPayCorpMemberReq = getPayCorpMemberReq(shop, shopExt);
                financeMemberRemoteService.createCompanyMember(adaPayCorpMemberReq);
                shop.setAdapayStatus(ShopEnum.AdapayAuditStatus.AdapayPending.getCode());
            }
        }
        TransactionHelper.doInTransaction(() -> {
            //删除之前的类目申请
            businessCategoryApplyService.deleteApply(shop.getId());
            //第一次更新
            shopRepository.updateById(shop);
            //添加类目申请
            businessCategoryApplyService.addApply(BusinessCategoryApplyBo.builder()
                    .shopId(shop.getId())
                    .categoryIdList(subCategoryIds)
                    .formList(businessCategoryFormBos).build());
            shopRepository.updateById(shop);
        });
        return cmdShopStepsThreeReq.getShopId();
    }

    @NotNull
    private PayCorpMemberReq getPayCorpMemberReq(Shop shop, ShopExt shopExt) {
        PayCorpMemberReq adaPayCorpMemberReq = new PayCorpMemberReq();
        adaPayCorpMemberReq.setMemberId(shop.getId().toString());
        adaPayCorpMemberReq.setName(shopExt.getCompanyName());
        //设置银行省份和城市
        setBankProvince(shop, adaPayCorpMemberReq);
        adaPayCorpMemberReq.setAddress(shopExt.getCompanyAddress());
        //调用三证合一方法
        adaPayCorpMemberReq.setAttachFile(integrationCertificates(shop, shopExt));
        adaPayCorpMemberReq.setSocialCreditCode(shopExt.getBusinessLicenseNumber());
        adaPayCorpMemberReq.setSocialCreditCodeExpires(DateUtil.format(shopExt.getBusinessLicenseEnd(), "yyyyMMdd"));
        adaPayCorpMemberReq.setBusinessScope(shopExt.getBusinessSphere());
        adaPayCorpMemberReq.setLegalPerson(shopExt.getLegalPerson());
        adaPayCorpMemberReq.setLegalCertId(AesUtil.decrypt(shop.getIdCard(), encryptConfig.getAesSecret()));
        adaPayCorpMemberReq.setLegalCertIdExpires(DateUtil.format(shop.getIdCardEndDate(), "yyyyMMdd"));
        if (shop.getIdCardExpireType() == 1) {  //长期有效
            // 长期有效, 获取从今天开始100年后的日期, 格式yyyyMMdd
            adaPayCorpMemberReq.setLegalCertIdExpires(DateUtil.format(DateUtil.offsetMonth(new Date(), 100 * 12), "yyyyMMdd"));
        }
        adaPayCorpMemberReq.setLegalMp(shop.getContactsPhone());

        adaPayCorpMemberReq.setBankCode(shop.getBankCode());
        adaPayCorpMemberReq.setBankAcctType(shop.getBankType().toString());
        adaPayCorpMemberReq.setCardNo(AesUtil.decrypt(shop.getBankAccountNumber(), encryptConfig.getAesSecret()));
        adaPayCorpMemberReq.setCardName(shop.getBankAccountName());
        adaPayCorpMemberReq.setPaymentChannel(ShopConstant.PAYMENT_CHANNEL);
        shop.setAdapayStatus(ShopEnum.AdapayAuditStatus.AdapayPending.getCode());
        return adaPayCorpMemberReq;
    }

    @NotNull
    private PaySettleAccountReq getPaySettleAccountReq(Shop shop) {
        //结算账号
        PaySettleAccountReq adaPaySettleAccountReq = new PaySettleAccountReq();
        adaPaySettleAccountReq.setMemberId(shop.getId().toString());
        PaySettleAccountInfoDto adaPaySettleAccountInfoDto = new PaySettleAccountInfoDto();
        adaPaySettleAccountInfoDto.setCardId(AesUtil.decrypt(shop.getBankAccountNumber(), encryptConfig.getAesSecret()));
        adaPaySettleAccountInfoDto.setCardName(shop.getBankAccountName());
        adaPaySettleAccountInfoDto.setTelNo(shop.getContactsPhone());
        adaPaySettleAccountInfoDto.setBankAcctType(shop.getBankType().toString());
        adaPaySettleAccountInfoDto.setBankCode(shop.getBankCode());
        adaPaySettleAccountInfoDto.setBankName(shop.getBankName());
        adaPaySettleAccountInfoDto.setCertId(AesUtil.decrypt(shop.getIdCard(), encryptConfig.getAesSecret()));
        adaPaySettleAccountInfoDto.setCertType(ShopConstant.CERT_TYPE);

        //如果是个人
        if (ShopEnum.BankType.PUBLIC.getCode().equals(shop.getBankType())) {
            //设置银行省份和城市
            setBankProvince(shop, adaPaySettleAccountInfoDto);
        }
        adaPaySettleAccountReq.setAccountInfo(adaPaySettleAccountInfoDto);
        adaPaySettleAccountReq.setChannel(ShopConstant.BANK_ACCOUNT);
        adaPaySettleAccountReq.setPaymentChannel(ShopConstant.PAYMENT_CHANNEL);
        return adaPaySettleAccountReq;
    }

    private void setBankProvince(Shop shop, PaySettleAccountInfoDto paySettleAccountInfoDto) {
        List<TreeRegionBO> baseRegionResp = bankRegionService.getFullList(shop.getBankRegionId());
        if (CollUtil.isNotEmpty(baseRegionResp) && baseRegionResp.size() > 1) {
            paySettleAccountInfoDto.setProvCode(baseRegionResp.get(0).getCode());
            paySettleAccountInfoDto.setAreaCode(baseRegionResp.get(1).getCode());
        } else {
            throw new BusinessException(UserResultCodeEnum.BANK_ADDRESS_INFO_ERROR);
        }
    }

    private void setBankProvince(Shop shop, PayCorpMemberReq adaPayCorpMemberReq) {
        List<TreeRegionBO> baseRegionResp = bankRegionService.getFullList(shop.getBankRegionId());
        if (CollUtil.isNotEmpty(baseRegionResp) && baseRegionResp.size() > 1) {
            adaPayCorpMemberReq.setProvCode(baseRegionResp.get(0).getCode());
            adaPayCorpMemberReq.setAreaCode(baseRegionResp.get(1).getCode());
        } else {
            throw new BusinessException(UserResultCodeEnum.BANK_ADDRESS_INFO_ERROR);
        }
    }

    private String integrationCertificates(Shop shop, ShopExt shopExt) {
        File bankPhotoFile = null, idCardUrlFile = null, idCardUrl2File = null, businessLicenceNumberPhotoFile = null, zipFile = null;
        try {
            // prefix 最短3位长度，否则报错
            String prefix = String.format("%03d", shop.getId());
            bankPhotoFile = File.createTempFile(prefix, CommonConstant.BANK_PHOTO_SUFFIX);
            idCardUrlFile = File.createTempFile(prefix, CommonConstant.ID_CARD_URL_SUFFIX);
            idCardUrl2File = File.createTempFile(prefix, CommonConstant.ID_CARD_URL2_SUFFIX);
            businessLicenceNumberPhotoFile = File.createTempFile(prefix, CommonConstant.BUSINESS_LICENCE_NUMBER_PHOTO_SUFFIX);
            zipFile = File.createTempFile(prefix, CommonConstant.CERTIFICATES_SUFFIX);

            AssertUtil.throwIfTrue(StrUtil.isEmpty(shop.getBankPhoto()), "请上传开户银行许可证照");
            AssertUtil.throwIfTrue(StrUtil.isEmpty(shop.getIdCardurl()), "请上传法人身份证正面照");
            AssertUtil.throwIfTrue(StrUtil.isEmpty(shop.getIdCardurl2()), "请上传法人身份证反面照");
            AssertUtil.throwIfTrue(StrUtil.isEmpty(shopExt.getBusinessLicenseNumberPhoto()), "请上传营业执照");

            // String storageHost = storageClient.formatHost();
            //开户许可证
            byte[] bankPhoto = HttpUtil.downloadBytes(shop.getBankPhoto());
            FileUtil.writeBytes(bankPhoto, bankPhotoFile);
            //法人身份证
            byte[] idCardUrl = HttpUtil.downloadBytes(shop.getIdCardurl());
            idCardUrlFile = FileUtil.writeBytes(idCardUrl, idCardUrlFile);
            byte[] idCardUrl2 = HttpUtil.downloadBytes(shop.getIdCardurl2());
            idCardUrl2File = FileUtil.writeBytes(idCardUrl2, idCardUrl2File);
            //营业执照
            byte[] businessLicenceNumberPhoto = HttpUtil.downloadBytes(shopExt.getBusinessLicenseNumberPhoto());
            businessLicenceNumberPhotoFile = FileUtil.writeBytes(businessLicenceNumberPhoto, businessLicenceNumberPhotoFile);

            //合并文件
            List<File> files = new ArrayList<>();
            files.add(bankPhotoFile);
            files.add(idCardUrlFile);
            files.add(idCardUrl2File);
            files.add(businessLicenceNumberPhotoFile);
            //定义压缩文件
            ZipUtil.zip(zipFile, false, files.toArray(new File[files.size()]));
            //上传文件
            StorageFileInfo sd = storageClient.putObject(zipFile.getName(), zipFile, null, null);

            return sd.getHost() + "/" + sd.getKey();
        } catch (Exception e) {
            log.error("integrationCertificates-error:{}", shop, e);
            throw new BusinessException(UserResultCodeEnum.CERTIFICATES_UPLOAD_ERROR);
        } finally {
            if (bankPhotoFile != null) {
                bankPhotoFile.delete();
            }
            if (idCardUrlFile != null) {
                idCardUrlFile.delete();
            }
            if (idCardUrl2File != null) {
                idCardUrl2File.delete();
            }
            if (businessLicenceNumberPhotoFile != null) {
                businessLicenceNumberPhotoFile.delete();
            }
            if (zipFile != null) {
                zipFile.delete();
            }
        }
    }


    @Override
    public ShippingSettingsResp getShippingSettings(Long shopId) {

        log.info("getShippingSettings-shopId:{}", shopId);
        Shop shop = shopRepository.getById(shopId);
        if (shop == null) {
            throw new BusinessException(UserResultCodeEnum.SHOP_NOT_FIND.getCode(), UserResultCodeEnum.SHOP_NOT_FIND.getMsg());
        }
        ShippingSettingsResp resp = new ShippingSettingsResp();
        resp.setShopId(shop.getId());
        resp.setAmountFreightCondition(shop.getAmountFreightCondition());
        resp.setAmountFreight(shop.getAmountFreight());
        resp.setQuantityFreightCondition(shop.getQuantityFreightCondition());
        resp.setQuantityFreight(shop.getQuantityFreight());

        List<ShopFreeShippingArea> shopFreeShippingAreas = shopFreeShippingAreaRepository.listByShopId(shopId);
        if (CollectionUtils.isNotEmpty(shopFreeShippingAreas)) {
            resp.setAreaList(JsonUtil.copyList(shopFreeShippingAreas, ShopFreeShippingAreaDto.class));
        }

        return resp;
    }

    @Override
    public BasePageResp<ShopResp> queryPage(ShopQueryPagerReq request) {
        //获取所有需要补充资料的店铺
        com.github.pagehelper.Page<ShopModel> shopPage = PageHelper.startPage(request.getPageNo(), request.getPageSize());
        ShopQueryModel shopQueryDto = JsonUtil.copy(request, ShopQueryModel.class);
        List<ShopModel> shopModels = shopRepository.getList(shopQueryDto);
        BasePageResp<ShopResp> basePageResp = PageResultHelper.transfer(shopPage, ShopResp.class);
        //判断数据是否为空
        if (CollUtil.isNotEmpty(basePageResp.getData())) {
            //先获取所有类目id 欠费
            Set<Long> categoryIds = new LinkedHashSet<>();
            basePageResp.getData().forEach(shopResp -> {
                List<Long> tempCategoryIds = new ArrayList<>();
                if (Objects.equals(shopResp.getShopStatus(), ShopEnum.AuditStatus.WaitAudit.getCode())) {
                    tempCategoryIds = businessCategoryApplyService.queryDetailByShopIdLimit(shopResp.getId()).stream().map(BusinessCategoryResp::getCategoryId).collect(Collectors.toList());
                } else {
                    tempCategoryIds = businessCategoryService.queryIdListByShopIdLimit(shopResp.getId());
                }
                categoryIds.addAll(tempCategoryIds);
                shopResp.setCategoryIds(tempCategoryIds);
            });
            //set转list
            List<Long> categoryIdList = new ArrayList<>(categoryIds);
            List<CategoryResp> categoryBos = remoteCategoryService.queryCategoryByTreeId(categoryIdList);
            basePageResp.getData().forEach(shopResp -> {
//                获取原来的数据
                ShopModel shopModel = shopModels.stream().filter(shopModel1 -> shopModel1.getId().equals(shopResp.getId())).findFirst().orElse(new ShopModel());
                List<CategoryResp> tempCategoryBos = categoryBos.stream().filter(categoryBo -> shopResp.getCategoryIds().contains(categoryBo.getId())).collect(Collectors.toList());
                shopResp.setFullCategoryName(StrUtil.join(StrUtil.COMMA, tempCategoryBos.stream().map(CategoryResp::getFullCategoryName).collect(Collectors.toList())));
                shopResp.setShopStatusDesc(ShopEnum.AuditStatus.getDescByCode(shopResp.getShopStatus()));
                shopResp.setPlateStatusDesc(ShopEnum.PlatAuditStatus.getDescByCode(shopResp.getPlateStatus()));
                shopResp.setAdapayStatusDesc(ShopEnum.AdapayAuditStatus.getDescByCode(shopResp.getAdapayStatus()));
                shopResp.setShopTypeDesc(ShopTypeEnum.getDescByCode(shopResp.getShopType()));
                //判断是否需要补充资料
                shopResp.setWhetherSupply(shopModel.getWhetherSupply());
                shopResp.setWhetherPayBond(shopModel.getWhetherArrear());
                //判断是否显示汇付账号 汇付状态为成功或者店铺状态为6或7地
                shopResp.setAdaMemberId(
                        ShopEnum.AdapayAuditStatus.AdapaySuccess.getCode().equals(shopResp.getAdapayStatus()) ||
                                ShopEnum.AuditStatus.Open.getCode().equals(shopResp.getShopStatus())
                                || ShopEnum.AuditStatus.Freeze.getCode().equals(shopResp.getShopStatus())
                                ? shopResp.getId() : null);
                if ((ShopEnum.AuditStatus.Open.getCode().equals(shopResp.getShopStatus())
                        || ShopEnum.AuditStatus.Freeze.getCode().equals(shopResp.getShopStatus()))
                        && !ShopEnum.AdapayAuditStatus.AdapaySuccess.getCode().equals(shopResp.getPlateStatus())) {
                    shopResp.setUpdateAdaPayStatusDesc(shopResp.getAdapayStatusDesc());
                }
            });
        }
        return basePageResp;
    }

    //查询店铺类目详情
    @Override
    public String queryShopCategoryDetail(BaseIdReq shopId) {
        Shop shop = shopRepository.selectById(shopId.getId());
        List<Long> tempCategoryIds;
        if (Objects.equals(shop.getShopStatus(), ShopEnum.AuditStatus.WaitAudit.getCode())) {
            tempCategoryIds = businessCategoryApplyService.queryDetailByShopIdLimit(shop.getId()).stream().map(BusinessCategoryResp::getCategoryId).collect(Collectors.toList());
        } else {
            tempCategoryIds = businessCategoryService.queryIdListByShopIdLimit(shop.getId());
        }
        List<CategoryResp> categoryBos = remoteCategoryService.queryCategoryByTreeId(tempCategoryIds);
        return StrUtil.join(StrUtil.COMMA, categoryBos.stream().map(CategoryResp::getFullCategoryName).collect(Collectors.toList()));
    }

    @Override
    public ShopDetailResp queryDetail(BaseIdReq shopId) {
        Shop shop = shopRepository.getById(shopId.getId());
        //判空
        if (shop == null) {
            throw new BusinessException(UserResultCodeEnum.SHOP_NOT_FIND);
        }
        //判断异常状态
        if (ShopEnum.AuditStatus.Freeze.getCode().equals(shop.getShopStatus())) {
            throw new BusinessException(UserResultCodeEnum.SHOP_FROZEN);
        }
        //获取店铺扩展信息
        ShopExt shopExt = shopExtRepository.getByShopId(shopId.getId());
        ShopDetailResp shopDetailResp = new ShopDetailResp();
        BeanUtil.copyProperties(shopExt, shopDetailResp);
        BeanUtil.copyProperties(shop, shopDetailResp);
        shopDetailResp.setLogo(shop.getLogo());

        //设置idCardUrl
        shopDetailResp.setIdCardUrl(shop.getIdCardurl());
        shopDetailResp.setIdCardUrl2(shop.getIdCardurl2());
        //加密字段还原
        shopDetailResp.setBankAccountNumber(AesUtil.decrypt(shop.getBankAccountNumber(), encryptConfig.getAesSecret()));
        shopDetailResp.setIdCard(AesUtil.decrypt(shop.getIdCard(), encryptConfig.getAesSecret()));

        if (shopDetailResp.getCompanyRegionId() != null) {
            RegionIdsReq req = new RegionIdsReq();
            req.setRegionIds(Collections.singletonList(shopDetailResp.getCompanyRegionId()));
            Map<String, AllPathRegionResp> regionMap = ThriftResponseHelper.executeThriftCall(() -> regionQueryFeign.getAllPathRegions(req));
            if (MapUtils.isNotEmpty(regionMap) && regionMap.containsKey(shopDetailResp.getCompanyRegionId().toString())) {
                AllPathRegionResp region = regionMap.get(shopDetailResp.getCompanyRegionId().toString());
                shopDetailResp.setProvinceId(region.getProvinceId());
                shopDetailResp.setProvinceName(region.getProvinceName());
                shopDetailResp.setCityId(region.getCityId());
                shopDetailResp.setCityName(region.getCityName());
                shopDetailResp.setCountyId(region.getCountyId());
                shopDetailResp.setCountyName(region.getCountyName());
                shopDetailResp.setTownIds(region.getTownIds());
                shopDetailResp.setTownsNames(region.getTownsNames());
            }
        }
        //获取店铺类目
        List<BusinessCategoryResp> businessCategories = null;
        if (!ShopEnum.AuditStatus.Freeze.getCode().equals(shop.getBusinessType()) || !ShopEnum.AuditStatus.Open.getCode().equals(shop.getBusinessType())) {
            businessCategories = businessCategoryApplyService.queryDetailByShopId(shop.getId());
            businessCategoryService.setCategoryName(businessCategories);
        } else {
            businessCategories = businessCategoryService.queryListByShopId(shop.getId());
        }
        if (CollUtil.isNotEmpty(businessCategories)) {
            shopDetailResp.setBusinessCategory(businessCategories);
            //获取所有类目ID
            List<Long> categoryIds = businessCategories.stream().map(BusinessCategoryResp::getCategoryId).collect(Collectors.toList());
            //获取所有类目ID的1级类目
            List<RemoteCategoryBo> parentCategoryIds = remoteCategoryService.queryFirstCategoryList(categoryIds);
            categoryIds.addAll(parentCategoryIds.stream().map(RemoteCategoryBo::getId).collect(Collectors.toList()));
            //获取店铺类目自定义表单
            List<CategoryApplyFormDto> businessCategoryForms = businessCategoryFormService.queryFormListByShopId(shop.getId(), categoryIds);
            //完善自定义表单字段
            shopDetailResp.setFieldList(businessCategoryForms);
        }

        //获取店铺商家信息
        ShopMemberInfoBO member = managerService.queryEpManagerByShopId(shop.getId());
        shopDetailResp.setRealName(member.getRealName());
        shopDetailResp.setMemberPhone(member.getPhone());
        shopDetailResp.setMemberEmail(member.getEmail());
        shopDetailResp.setShopAccount(member.getMemberName());
        dealRegionName(shopDetailResp);
        return shopDetailResp;
    }

    /**
     * 处理地区名称
     *
     * @param shopDetailResp 店铺详情
     */
    private void dealRegionName(ShopDetailResp shopDetailResp) {
        if (shopDetailResp.getCompanyRegionId() != null) {
            List<BaseRegion> regions = regionService.getParentRegions(shopDetailResp.getCompanyRegionId().longValue());
            //排序 从小到大
            regions.sort(Comparator.comparingInt(BaseRegion::getRegionLevel));
            //获取区域全名
            StringBuilder regionFullName = new StringBuilder();
            for (int i = 0; i < regions.size(); i++) {
                regionFullName.append(regions.get(i).getName());
            }
            String regionName = regionFullName.toString();
            shopDetailResp.setCompanyRegionName(regionName);

        }
        if (shopDetailResp.getBankRegionId() != null) {
            shopDetailResp.setBankRegionName(bankRegionService.getRegionFullName(shopDetailResp.getBankRegionId()));
        }
    }

    @Override
    public BasePageResp<ShopEsResp> queryByShopEs(ShopEsQueryReq request) {
        EsShopParam esShopParam = JsonUtil.copy(request, EsShopParam.class);
        BasePageResp<EsShopModel> page = esShopService.page(request.buildPage(), esShopParam, request.getSortList());
        return PageResultHelper.transfer(page, ShopEsResp.class);
    }

    @Override
    public Long getYesterdayShopUV(Long shopId) {
        //        获取昨日时间
        String yesterday = DateUtil.format(DateUtil.yesterday(), "yyyyMMdd");
        String key = StrUtil.format(ShopSquirrelConstant.SHOP_UV_KEY, yesterday, shopId);
        String yesterdayUserVisit = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(yesterdayUserVisit)) {
            return Long.valueOf(yesterdayUserVisit);
        }
        return 0L;
    }

    @Override
    public BasePageResp<ShopSimpleResp> querySimplePage(ShopQueryPagerReq queryPagerReq) {
        //获取所有需要补充资料的店铺
//        List<Long> needSupplyShopIds = businessCategoryAssistant.queryNeedSupplyShopIds();
        ShopQueryModel shopQueryDto = JsonUtil.copy(queryPagerReq, ShopQueryModel.class);
        Page<Shop> shopPage = PageHelper.startPage(queryPagerReq.getPageNo(), queryPagerReq.getPageSize());
        BasePageResp<ShopSimpleResp> respBasePageResp = PageResultHelper.transfer(shopPage, ShopSimpleResp.class);
        //获取店铺等级枚举
        //如果不为空设置店铺等级
        if (CollUtil.isNotEmpty(respBasePageResp.getData())) {
            respBasePageResp.getData().forEach(shop -> {
                shop.setGradeName(ShopEnum.Grade.getDescByCode(shop.getGradeId()));
            });
        }
        return respBasePageResp;
    }

    @Override
    public String createQR(CmdCreateQRReq cmdCreateQRReq) {
        return createQR(cmdCreateQRReq, null);
    }

    @Override
    public String createQR(CmdCreateQRReq cmdCreateQRReq, String logo) {
        try {
            String fileName = LogoHelper.getFileName(cmdCreateQRReq.getPath(), logo);
            String cacheKey = CacheConstant.QR_CODE + fileName;
//            if (squirrelUtil.get(cacheKey) != null) {
//                String httpFileUrl = venusService + squirrelUtil.get(cacheKey).toString();
//                //判断图片是否存在
//                if (LogoHelper.exist(httpFileUrl)) {
//                    log.error("二维码已存在，无需重新生成，直接返回");
//                    return fileName;
//                }
//            }
            //判断图片是否存在
            byte[] file = wxMaService
                    .getQrcodeService()
                    .createWxaCodeBytes(cmdCreateQRReq.getPath(), envVersion, 430, true, null, false);
            //是否存在logo
            if (StrUtil.isNotEmpty(logo)) {
                file = LogoHelper.changLogo(file, StorageUtil.formatUrl(storageClient.formatHost(), logo));
            }

            StorageFileInfo storageFileInfo = storageClient.putObject(fileName, file, null, null, null);
//            String fileKey = venusService.uploadImage(file, fileName).getFileKey();
//            fileKey = fileKey.replace("/" + bucket + "/", StrUtil.EMPTY);
            return StrUtil.join("/", storageFileInfo.getHost(), storageFileInfo.getKey());
        } catch (WxErrorException e) {
            log.error("创建小程序码失败", e);
            throw new BusinessException(UserResultCodeEnum.CREATE_QR_ERROR);
        }
    }

    private void dealFailed(CmdShopStatusReq shopId, Shop shop, BusinessCategoryApply businessCategoryApply) {
        //先判断平台状态和汇付状态是否有待审核
        shop.setStage(ShopEnum.ShopStage.CompanyInfo.getCode());
        shop.setShopStatus(ShopEnum.AuditStatus.Refuse.getCode());
        //判断拒绝原因
        if (StringUtils.isBlank(shopId.getRefuseReason())) {
            throw new BusinessException(UserResultCodeEnum.REFUSE_REASON_NOT_NULL);
        }
        shopRepository.updateById(shop);
        //审核拒绝 申请类目也拒绝
        if (businessCategoryApply != null) {
            AuditBusinessCategoryApplyBo applyBo = new AuditBusinessCategoryApplyBo();
            applyBo.setId(businessCategoryApply.getId());
            applyBo.setPass(false);
            applyBo.setRefuseReason(shopId.getRefuseReason());
            businessCategoryApplyService.auditBusinessCategoryApply(applyBo);
        }

        //发送拒绝短信
        try {
            Map<String, String> map = new HashMap<>();
            map.put("reason", shopId.getRefuseReason());
            messageRemoteService.sendSms(userLionConfigClient.getReviewFailureReminder(), JsonUtil.toJsonString(map), shop.getContactsPhone());
        } catch (Exception e) {
            log.error("[审核失败] 发送短信失败", e);
        }
    }

    @Override
    public BaseResp sendDepositRemind(BaseIdReq baseIdReq) {
        //1. 获取手机号
        String cellphone = getCellPhone(baseIdReq.getId());
        //2. 发送短信
        //2.1 判断手机号是否为空
        if (StringUtils.isBlank(cellphone)) {
            return BaseResp.of();
        }
        //生成短信内容
        Map<String, String> params = new HashMap<>();
        String paramStr = JSONUtil.toJsonStr(params);

        SmsBodyReq smsBodyReq = new SmsBodyReq();
        smsBodyReq.setTemplateId(userLionConfigClient.getDepositRemind());
        smsBodyReq.setParam(paramStr);
        smsBodyReq.setRequestId(leafService.generateNoBySnowFlake(LeafConstant.KEY_USER_NO));
        // 设置收信人
        ContactReq contactReq = new ContactReq();
        contactReq.setMobile(cellphone);
        smsBodyReq.setContactList(Collections.singletonList(contactReq));
        messageCMDFeign.sendSms(smsBodyReq);
        return BaseResp.of();
    }

    @Override
    public BaseResp batchSendSms(List<Long> longs, SmsEnum.Template template) {
        //1. 获取手机号
        List<String> cellphone = shopRepository.getCellPhoneList(longs);
        //2. 发送短信
        //2.1 判断手机号是否为空
        if (CollUtil.isEmpty(cellphone)) {
            return BaseResp.of();
        }
        //生成短信内容
        // 发送短信
        SmsBodyReq smsBodyReq = new SmsBodyReq();
        smsBodyReq.setTemplateId(template.getCode());
        smsBodyReq.setRequestId(leafService.generateNoBySnowFlake(LeafConstant.KEY_USER_NO));
        // 设置收信人
        List<ContactReq> contactReqList = cellphone.stream().map(e -> {
            ContactReq contactReq = new ContactReq();
            contactReq.setMobile(e);
            return contactReq;
        }).collect(Collectors.toList());
        smsBodyReq.setContactList(contactReqList);
        messageCMDFeign.sendSms(smsBodyReq);
        return BaseResp.of();
    }

    @Override
    public ShopRespList getShopList(QueryShopPageReq queryShopPageReq) {
        //获取需筛选的店铺ID
        List<Long> shopIds = queryShopPageReq.getShopIds();
        ////如果商家ID不为空
        //if (queryShopPageReq.getUserId() != null){
        //    //标记是否专属店铺
        //    List<ExclusiveMember> exclusiveMembers = exclusiveMemberRepository.lambdaQuery().eq(ExclusiveMember::getUserId, queryShopPageReq.getUserId()).list();
        //    //判空
        //    if (CollectionUtils.isNotEmpty(exclusiveMembers)){
        //        //获取所有专属店铺的shopId并和筛选的shopId取交集
        //        shopIds = shopIds.stream().filter(shopId -> exclusiveMembers.stream().map(ExclusiveMember::getShopId).collect(Collectors.toList()).contains(shopId)).collect(Collectors.toList());
        //    }
        //}
        //判空
        if (CollectionUtils.isEmpty(shopIds)) {
            return new ShopRespList();
        }
        //获取店铺信息
        List<Shop> shops = shopRepository.lambdaQuery().in(Shop::getId, shopIds).list();
        //转化结果
        List<ShopResp> shopRespList = JsonUtil.copyList(shops, ShopResp.class);
        return new ShopRespList(shopRespList);
    }

    @Override
    public ShopSimpleListResp querySimpleList(ShopSimpleQueryReq request) {
        List<Shop> shops = shopRepository.lambdaQuery()
                .in(CollUtil.isNotEmpty(request.getShopIdList()), Shop::getId, request.getShopIdList())
                .like(StringUtils.isNotBlank(request.getShopName()), Shop::getShopName, request.getShopName())
                .in(CollUtil.isNotEmpty(request.getShopNameList()), Shop::getShopName, request.getShopNameList())
                .eq(Shop::getShopStatus, ShopEnum.AuditStatus.Open.getCode()).list();
        //判空
        if (CollectionUtils.isEmpty(shops)) {
            return new ShopSimpleListResp();
        }
        //转化结果
        List<ShopSimpleResp> shopSimpleRespList = JsonUtil.copyList(shops, ShopSimpleResp.class);
        //判断是否需要查保证金
        if (request.getQueryCashDeposit() != null && request.getQueryCashDeposit()) {
            //获取店铺保证金
            List<BusinessCategory> shopBonds = businessCategoryService.queryShopMaxBondList(shopSimpleRespList.stream().map(ShopSimpleResp::getId).collect(Collectors.toList()));
            //遍历设置保证金
            for (ShopSimpleResp shopSimpleResp : shopSimpleRespList) {
                //获取店铺保证金
                Optional<BusinessCategory> businessCategory = shopBonds.stream().filter(bond -> bond.getShopId().equals(shopSimpleResp.getId())).findFirst();
                //判空
                shopSimpleResp.setMaxCashDeposit(businessCategory.isPresent() ? businessCategory.get().getBond() : BigDecimal.ZERO);
            }
        }
        return new ShopSimpleListResp(shopSimpleRespList);
    }

    @Override
    public ProductShopInfoResp queryProductShop(ProductShopQueryReq baseIdReq) {
        ProductShopInfoResp productShopInfoResp = new ProductShopInfoResp();
        //获取店铺基本信息
        Shop shop = shopRepository.getById(baseIdReq.getShopId());
        //判空
        if (shop == null) {
            throw new BusinessException(UserResultCodeEnum.SHOP_NOT_FIND.getCode(), UserResultCodeEnum.SHOP_NOT_FIND.getMsg());
        }
//设置店铺信息
        productShopInfoResp.setName(shop.getShopName());
        productShopInfoResp.setId(shop.getId());
        productShopInfoResp.setLogo(shop.getLogo());
        productShopInfoResp.setCreateTime(shop.getCreateTime());
//        获取店铺补充信息
        ShopExt shopExt = shopExtRepository.getByShopId(baseIdReq.getShopId());
        //判空
        if (shopExt == null) {
            return productShopInfoResp;
        }
        List<BaseRegion> regions = regionService.getParentRegions(shopExt.getCompanyRegionId().longValue());
        //排序 从小到大
        regions.sort(Comparator.comparingInt(BaseRegion::getRegionLevel));
        //获取区域全名
        StringBuilder regionFullName = new StringBuilder();
        for (int i = 0; i < regions.size(); i++) {
            regionFullName.append(regions.get(i).getName());
        }
        String region = regionFullName.toString();
        //判空
        if (StringUtils.isNotBlank(region)) {
            //设置店铺地址
            productShopInfoResp.setAddress(region + shopExt.getCompanyAddress());
        }
        //设置店铺公司名
        productShopInfoResp.setCompanyName(shopExt.getCompanyName());
        //设置保证金
        setCashDeposit(baseIdReq, productShopInfoResp, shop);
        //设置店铺评分
        setShopMark(baseIdReq, productShopInfoResp);
        //设置店铺商品相关
        setShopProduct(baseIdReq, productShopInfoResp);
        //是否自营店
        productShopInfoResp.setWhetherSelf(shop.getWhetherSelf());
        if (shop.getWhetherSelf() != null && shop.getWhetherSelf()) {
            productShopInfoResp.setCustomerSecurity(true);
        }
        //获取店铺扩展信息
        return productShopInfoResp;
    }

    @Override
    public ShopIntroductionResp shopIntroduction(BaseIdReq baseIdReq) {
        //获取店铺基本信息
        Shop shop = shopRepository.getById(baseIdReq.getId());
        ShopIntroductionResp shopIntroductionResp = new ShopIntroductionResp();
        //判空
        if (shop == null) {
            throw new BusinessException(UserResultCodeEnum.SHOP_NOT_FIND);
        }
//设置店铺信息
        shopIntroductionResp.setShopId(shop.getId());
        shopIntroductionResp.setShopName(shop.getShopName());
        shopIntroductionResp.setLogo(shop.getLogo());
//        设置店铺评分
        setShopMark(shopIntroductionResp);
        //获取店铺二维码
        CmdCreateQRReq cmdCreateQRReq = new CmdCreateQRReq();
        cmdCreateQRReq.setPath("packageShop/home/<USER>" + shop.getId());
        shopIntroductionResp.setQrCode(createQR(cmdCreateQRReq, shop.getLogo()));
        return shopIntroductionResp;
    }

    @Override
    public ShopSimpleResp selfShopInfo() {
        Shop shop = shopRepository.lambdaQuery().eq(Shop::getWhetherSelf, true).one();
        return JsonUtil.copy(shop, ShopSimpleResp.class);
    }

    @Override
    public ShopSimpleResp shopInfoByUserId(BaseIdReq baseIdReq) {
        Member member = memberRepository.getById(baseIdReq.getId());
        Manager manager = managerRepository.getByUserName(member.getUserName());
        Shop shop = shopRepository.getById(manager.getShopId());
        return JsonUtil.copy(shop, ShopSimpleResp.class);
    }

    private void setBankInfo(Shop shop, CmdShopStepsTwoReq cmdShopStepsTwoReq) {
        shop.setBankAccountName(cmdShopStepsTwoReq.getBankAccountName());
        shop.setBankAccountNumber(AesUtil.encrypt(cmdShopStepsTwoReq.getBankAccountNumber(), encryptConfig.getAesSecret()));
        shop.setBankCode(cmdShopStepsTwoReq.getBankCode());
        shop.setBankName(cmdShopStepsTwoReq.getBankName());
        shop.setBankType(cmdShopStepsTwoReq.getBankType());
        shop.setBankRegionId(cmdShopStepsTwoReq.getBankRegionId());
    }

    @Override
    public void updateSettleAccount(Shop shop) {
        //先查询shopAccount判断是否存在汇付结算账号
        String settleAccountId = ThriftResponseHelper
                .executeThriftCall(() -> shopAccountQueryFeign.getShopAccountByShopId(shop.getId()));
        if (StrUtil.isEmpty(settleAccountId) || settleAccountId.equals("0")) {
            //重新创建结算账户
            PaySettleAccountReq paySettleAccountReq = getPaySettleAccountReq(shop);
            PaySettleAccountResp accountResp = financeMemberRemoteService.createSettleAccount(paySettleAccountReq);
            settleAccountId = accountResp.getId();
        } else {
//        //更新店铺结算账号
            PaySettleAccountReq paySettleAccountReq = getPaySettleAccountReq(shop);
            UpdatePaySettleAccountReq updatePaySettleAccountReq = JsonUtil.copy(paySettleAccountReq, UpdatePaySettleAccountReq.class);
            updatePaySettleAccountReq.setSettleAccountId(settleAccountId);
            PaySettleAccountResp accountResp = financeMemberRemoteService.updateSettleAccount(updatePaySettleAccountReq);
            settleAccountId = accountResp.getId();
        }
        CreateAccountReq createAccountReq = new CreateAccountReq();
        createAccountReq.setShopId(shop.getId());
        createAccountReq.setShopName(shop.getShopName());
        createAccountReq.setAccountId(settleAccountId);
        ThriftResponseHelper.executeThriftCall(() -> shopAccountCmdFeign.createAccount(createAccountReq));
    }

    private void setShopProduct(ProductShopQueryReq baseIdReq, ProductShopInfoResp productShopInfoResp) {
        //        获取商品信息
//        判断商品id是否传入
        if (baseIdReq.getProductId() == null) {
            return;
        }
        ProductQueryDetailReq productQueryDetailReq = new ProductQueryDetailReq();
        productQueryDetailReq.setProductId(Long.parseLong(baseIdReq.getProductId()));
        ProductDetailResp productResp = ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.queryProductDetail(productQueryDetailReq));
        //判空
        if (productResp == null) {
            return;
        }
        //是否支持7天无理由
        Boolean sevenDayNoReasonReturn = false;
        //是否支持消费者保障
        Boolean customerSecurity = true;
        //是否支持及时发货
        Boolean timelyDelivery = false;
        //是否虚拟品牌
        Boolean virtual = true;
        //商品详情
        ProductDetailDto productDetailDto = productResp.getResult();
//        查询店铺类目情况
        BusinessCategory businessCategory = businessCategoryService.queryOne(baseIdReq.getShopId(), productDetailDto.getCategoryId());
//判空
        if (businessCategory == null) {
            return;
        }
        //查询类目配置
        CategoryResp categoryResp = remoteCategoryService.queryCategoryById(businessCategory.getCategoryId());
        //判空
        if (categoryResp == null) {
            return;
        }
        //判断是否支持7天无理由
        if (categoryResp.getEnableNoReasonReturn()) {
            sevenDayNoReasonReturn = true;
        }
        //判断是否支持及时发货
        if (productDetailDto.getFreightTemplateId() != null) {
            timelyDelivery = true;
        }
        //判断是否支持消费者服务 保证金>0 且 已缴纳保证金=0
        if (productShopInfoResp.getCashDeposits()
                .compareTo(BigDecimal.ZERO) > 0 && productShopInfoResp.getCashDepositPaid()
                .compareTo(BigDecimal.ZERO) == 0) {
            customerSecurity = false;
        }
        //设置上述状态
        productShopInfoResp.setCustomerSecurity(customerSecurity);
        productShopInfoResp.setSevenDayNoReasonReturn(sevenDayNoReasonReturn);
        productShopInfoResp.setTimelyDelivery(timelyDelivery);
    }

    @Override
    public List<ShopResp> queryShopsByIds(ShopQueryReq request) {
        List<Shop> shops = shopRepository.getList(null, request.getShopIds());
        return JsonUtil.copyList(shops, ShopResp.class);
    }

    private void setCashDeposit(ProductShopQueryReq baseIdReq, ProductShopInfoResp productShopInfoResp, Shop shop) {
        //获取店铺保证金
        ShopIdReq shopIdReq = new ShopIdReq();
        shopIdReq.setShopId(baseIdReq.getShopId());
        //远程调用获取结果
        CashDepositResp cashDepositResp = ThriftResponseHelper.executeThriftCall(() -> cashDepositQueryFeign.queryOneByShopId(shopIdReq));
        BusinessCategory category = businessCategoryService.queryShopMaxBond(shop.getId());
        //判空
        if (cashDepositResp != null && category != null) {
            //设置保证金
            productShopInfoResp.setCashDeposits(category.getBond());
            //设置已缴纳保证金
            productShopInfoResp.setCashDepositPaid(cashDepositResp.getTotalBalance());
            //余额大于零
            if (cashDepositResp.getTotalBalance().compareTo(BigDecimal.ZERO) > 0) {
                //设置保证金状态
                productShopInfoResp.setCustomerSecurity(true);//支持消费者保障
            }
            //判断是否欠费
            if (category.getBond().compareTo(cashDepositResp.getTotalBalance()) > 0) {
                //设置欠费
                productShopInfoResp.setCashDepositNeedPay(category.getBond().subtract(cashDepositResp.getTotalBalance()));
            }
        }
    }

    private void setShopMark(ProductShopQueryReq baseIdReq, ProductShopInfoResp productShopInfoResp) {
        //        获取店铺评分
        ShopIdReq shopIdReq = new ShopIdReq();
        shopIdReq.setShopId(baseIdReq.getShopId());
        ShopMarkResp shopMarkResp = ThriftResponseHelper.executeThriftCall(() -> orderCommentQueryFeign.queryShopMarkByShopId(shopIdReq));
        //判空
        if (shopMarkResp == null) {
            //设置店铺评分
            productShopInfoResp.setPackMark(CommonConstant.DEFAULT_SCORE);
            productShopInfoResp.setServiceMark(CommonConstant.DEFAULT_SCORE);
            productShopInfoResp.setComprehensiveMark(CommonConstant.DEFAULT_SCORE);
        } else {
            //设置店铺评分
            productShopInfoResp.setPackMark(shopMarkResp.getPackMark());
            productShopInfoResp.setServiceMark(shopMarkResp.getServiceMark());
            //综合评分 = 包装评分 + 服务评分 / 2
            productShopInfoResp.setComprehensiveMark(shopMarkResp.getComprehensiveMark());
        }
    }

    private void setShopMark(ShopIntroductionResp shopIntroductionResp) {
        //        获取店铺评分
        ShopIdReq shopIdReq = new ShopIdReq();
        shopIdReq.setShopId(shopIntroductionResp.getShopId());
        ShopMarkResp shopMarkResp = ThriftResponseHelper.executeThriftCall(() -> orderCommentQueryFeign.queryShopMarkByShopId(shopIdReq));
        //判空
        if (shopMarkResp == null) {
            //设置店铺评分
            shopIntroductionResp.setPackMark(CommonConstant.DEFAULT_SCORE);
            shopIntroductionResp.setServiceMark(CommonConstant.DEFAULT_SCORE);
            shopIntroductionResp.setComprehensiveMark(CommonConstant.DEFAULT_SCORE);
        } else {
            //设置店铺评分
            shopIntroductionResp.setPackMark(ObjectUtil.defaultIfNull(shopMarkResp.getPackMark(), CommonConstant.DEFAULT_SCORE));
            shopIntroductionResp.setServiceMark(ObjectUtil.defaultIfNull(shopMarkResp.getServiceMark(), CommonConstant.DEFAULT_SCORE));
            shopIntroductionResp.setComprehensiveMark(ObjectUtil.defaultIfNull(shopMarkResp.getComprehensiveMark(), CommonConstant.DEFAULT_SCORE));
        }

        // 评分保留一位小数
        shopIntroductionResp.setPackMark(shopMarkResp.getPackMark().setScale(1, RoundingMode.HALF_UP));
        shopIntroductionResp.setServiceMark(shopMarkResp.getServiceMark().setScale(1, RoundingMode.HALF_UP));
        shopIntroductionResp.setComprehensiveMark(shopMarkResp.getComprehensiveMark().setScale(1, RoundingMode.HALF_UP));
    }

    @Override
    public String getCellPhone(Long id) {
        //1. 获取手机号
        String cellphone = "";
        //1.1 获取店铺信息
        Shop shop = shopRepository.getById(id);
        //1.1.1 判断店铺是否存在
        if (shop == null) {
            throw new BusinessException(UserResultCodeEnum.SHOP_NOT_FIND.getCode(), UserResultCodeEnum.SHOP_NOT_FIND.getMsg());
        }
        //1.2 获取商家信息
        Manager manager = managerRepository.getByShopId(id);
        //1.2.1 判断商家是否存在
        if (manager == null) {
            return cellphone;
        }
        //1.2.1.1 存在则获取商家信息
        Member member = memberRepository.getByUserName(manager.getUserName());
        //1.2.1.2 判断商家信息是否有手机
        if (member != null) {
            //1.2.1.3.1 获取所有用户联系方式
            cellphone = memberContactService.queryPhone(member.getId());
        }
        return cellphone;
    }

    @Override
    public ShopUserCountResp countShopUser() {
//        初始化统计对象
        ShopUserCountResp shopUserCountResp = new ShopUserCountResp();
        //获取当前时间
        Date dateNow = new Date();
        //依次设置统计对象对象值
        //今日新增商家
        shopUserCountResp.setTodayAddSupplierCount(Math.toIntExact(memberRepository.lambdaQuery()
                .between(Member::getCreateTime, DateUtil.beginOfDay(dateNow), DateUtil.endOfDay(dateNow)).count()));
        //今日新增店铺
        shopUserCountResp.setTodayAddShopCount(Math.toIntExact(shopRepository.lambdaQuery()
                .eq(Shop::getShopStatus, ShopEnum.AuditStatus.Open.getCode())
                .between(Shop::getCreateTime, DateUtil.beginOfDay(dateNow), DateUtil.endOfDay(dateNow)).count()));
        //待审核店铺
        shopUserCountResp.setWaitAuditSupplierCount(Math.toIntExact(shopRepository.lambdaQuery()
                .eq(Shop::getShopStatus, ShopEnum.AuditStatus.WaitAudit.getCode()).count()));
        //昨日新增店铺
        shopUserCountResp.setYesterdayAddSupplierCount(Math.toIntExact(shopRepository.lambdaQuery()
                .eq(Shop::getShopStatus, ShopEnum.AuditStatus.Open.getCode())
                .between(Shop::getCreateTime, DateUtil.beginOfDay(DateUtil.offsetDay(dateNow, -1)), DateUtil.endOfDay(DateUtil.offsetDay(dateNow, -1))).count()));
        //店铺总数
        shopUserCountResp.setShopCount(Math.toIntExact(shopRepository.lambdaQuery().eq(Shop::getShopStatus, ShopEnum.AuditStatus.Open.getCode()).count()));
        //到期店铺
//        shopUserCountResp.setExpireShopCount(shopRepository.lambdaQuery()
//                .eq(Shop::getShopStatus,ShopEnum.AuditStatus.Open.getCode())
//                .between(Shop::getV, DateUtil.beginOfDay(dateNow), DateUtil.endOfDay(dateNow)).count());
        //待提现店铺

        return shopUserCountResp;
    }

    @Override
    public BaseResp sendCode(CmdSendCodeReq cmdSendCodeReq) {
        switch (Objects.requireNonNull(MemberContactEnum.Provider.getEnumByCode(cmdSendCodeReq.getContactType()))) {
            case SMS:
                //messageCMDFeign.sendSms(userLionConfigClient.getVerificationCode(), cmdSendCodeReq.getContact());
                break;
            case EMAIL:
                //messageCMDFeign.sendEmailCode(cmdSendCodeReq.getContact());
                break;
            default:
                throw new BusinessException(UserResultCodeEnum.CONTACT_INFO_ERROR);
        }
        return BaseResp.of();
    }

    private boolean verificationCode(CmdShopStepsOneReq cmdShopStepsOneReq, VerificationTypeEnum enumByCode) {
        switch (enumByCode) {
            case PHONE:
                cmdShopStepsOneReq.setMemberEmail(null);
                return messageRemoteService.checkSmsCode(cmdShopStepsOneReq.getMemberPhone(), cmdShopStepsOneReq.getPhoneCode());
            case EMAIL:
                cmdShopStepsOneReq.setMemberPhone(null);
                return messageRemoteService.checkEmailCode(cmdShopStepsOneReq.getMemberEmail(), cmdShopStepsOneReq.getEmailCode());
            case ALL:
                return messageRemoteService.checkSmsCode(cmdShopStepsOneReq.getMemberPhone(), cmdShopStepsOneReq.getPhoneCode()) &&
                        messageRemoteService.checkEmailCode(cmdShopStepsOneReq.getMemberEmail(), cmdShopStepsOneReq.getEmailCode());
            default:
                throw new BusinessException(UserResultCodeEnum.CONTACT_INFO_ERROR);
        }
    }

//    修改接口

    private void bindContact(CmdShopStepsOneReq req, Member member, Integer businessType) {
        //获取入驻信息
        BaseSettled settledRes = siteSettingRemoteService.getSettled();
        VerificationTypeEnum verificationType;
//        判断业务类型
        if (ShopEnum.BusinessType.PERSONAL.equals(ShopEnum.BusinessType.getEnumByCode(businessType))) {
            verificationType = VerificationTypeEnum.getEnumByCode(settledRes.getSelfVerificationType());
        } else {
            verificationType = VerificationTypeEnum.getEnumByCode(settledRes.getCompanyVerificationType());
        }

        BindContactCmdReq bindContactCmdReq = new BindContactCmdReq();
        bindContactCmdReq.setId(member.getId());
        //判断验证方式
        if (verificationType != null) {
            switch (verificationType) {
                case PHONE:
                    bindContactCmdReq.setUsertype(ContactUserTypeEnum.PHONE.getCode());
                    bindContactCmdReq.setContact(req.getMemberPhone());
                    bindContactCmdReq.setCode(req.getPhoneCode());
                    memberContactService.bindContact(bindContactCmdReq);
                    break;
                case EMAIL:
                    bindContactCmdReq.setUsertype(ContactUserTypeEnum.EMAIL.getCode());
                    bindContactCmdReq.setContact(req.getMemberEmail());
                    bindContactCmdReq.setCode(req.getEmailCode());
                    memberContactService.bindContact(bindContactCmdReq);
                    break;
                case ALL:
                    bindContactCmdReq.setUsertype(ContactUserTypeEnum.PHONE.getCode());
                    bindContactCmdReq.setContact(req.getMemberPhone());
                    bindContactCmdReq.setCode(req.getPhoneCode());
                    memberContactService.bindContact(bindContactCmdReq);
                    bindContactCmdReq.setUsertype(ContactUserTypeEnum.EMAIL.getCode());
                    bindContactCmdReq.setContact(req.getMemberEmail());
                    bindContactCmdReq.setCode(req.getEmailCode());
                    memberContactService.bindContact(bindContactCmdReq);
                    break;
                default:
                    throw new BusinessException(UserResultCodeEnum.CONTACT_INFO_ERROR);
            }
        }
    }

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.USER, actionName = "保存运费设置", processType = ExaProEnum.MODIFY,
            serviceMethod = "saveShippingSettings", dto = ShippingSettingsSaveReq.class, entity = Shop.class)
    public void saveShippingSettings(ShippingSettingsSaveReq request) {
        log.info("saveShippingSettings-request:{}", request);
        Shop shop = shopRepository.getById(request.getShopId());
        if (shop == null) {
            throw new BusinessException(UserResultCodeEnum.SHOP_NOT_FIND.getCode(), UserResultCodeEnum.SHOP_NOT_FIND.getMsg());
        }
        shop.setAmountFreightCondition(request.getAmountFreightCondition());
        shop.setAmountFreight(request.getAmountFreight());
        shop.setQuantityFreightCondition(request.getQuantityFreightCondition());
        shop.setQuantityFreight(request.getQuantityFreight());
        TransactionHelper.doInTransaction(() -> {
            // 保存主体对象
            shopRepository.saveOrUpdate(shop);

            if (null != request.getShopId()) {
                shopFreeShippingAreaRepository.deleteByShopId(request.getShopId());
            }

            /*
             * 保存包邮地区
             */
            List<ShopFreeShippingAreaDto> areaList = request.getAreaList();
            if (CollectionUtils.isNotEmpty(areaList)) {
                List<ShopFreeShippingArea> doList = new ArrayList<>();
                for (ShopFreeShippingAreaDto dto : areaList) {
                    ShopFreeShippingArea area = new ShopFreeShippingArea();
                    area.setShopId(shop.getId());
                    area.setRegionId(dto.getRegionId());
                    area.setRegionPath(dto.getRegionPath());
                    doList.add(area);
                }
                shopFreeShippingAreaRepository.saveBatch(doList);
            }
        });

    }

    @Override
    public Long modifyBankInfo(CmdShopStepsTwoReq cmdShopStepsTwoReq) {
//        加上锁每个店铺锁10秒
        if (squirrelUtil.get(LockConstant.EDIT_SHOP_BANK_INFO_LOCK) != null) {
            throw new BusinessException(UserResultCodeEnum.SHOP_BANK_INFO_EDIT_LOCK);
        }
        squirrelUtil.set(LockConstant.EDIT_SHOP_BANK_INFO_LOCK, cmdShopStepsTwoReq.getShopId(), 10);
        ValidatorHelper.validate(cmdShopStepsTwoReq);
        Shop shop = shopRepository.getById(cmdShopStepsTwoReq.getShopId());
        ShopLogBO shopLogBO = new ShopLogBO(shop, null);
        setBankInfo(shop, cmdShopStepsTwoReq);
        //更新汇付结算账号
        updateSettleAccount(shop);
        shopRepository.updateById(shop);
        baseLogAssist.recordLog(ExaminModelEnum.USER,
                ExaProEnum.MODIFY,
                "修改基本信息",
                cmdShopStepsTwoReq.getOperationUserId(), cmdShopStepsTwoReq.getOperationShopId(),
                shopLogBO, new ShopLogBO(shop, null));
        return shop.getId();
    }

    @Override
    public Long modifyBaseInfo(CmdShopStepsOneReq r) {
        ShopExt shopExt = shopExtRepository.getByShopId(r.getShopId());
        ShopLogBO shopLogBO = new ShopLogBO(null, shopExt);
        if (StrUtil.isEmpty(r.getCompanyName())) {
            throw new BusinessException(UserResultCodeEnum.COMPANY_NAME_NOT_NULL);
        }
        if (StrUtil.isEmpty(r.getCompanyAddress())) {
            throw new BusinessException(UserResultCodeEnum.COMPANY_ADDRESS_NOT_NULL);
        }
        if (r.getCompanyRegionId() == null) {
            throw new BusinessException(UserResultCodeEnum.COMPANY_REGION_NOT_NULL);
        }
        shopExt.setCompanyName(r.getCompanyName());
        shopExt.setCompanyAddress(r.getCompanyAddress());
        shopExt.setCompanyRegionId(r.getCompanyRegionId());
        TransactionHelper.doInTransaction(() -> {
            shopExtRepository.updateById(shopExt);
            baseLogAssist.recordLog(ExaminModelEnum.USER,
                    ExaProEnum.MODIFY,
                    "修改基本信息",
                    r.getOperationUserId(), r.getOperationShopId(),
                    shopLogBO, new ShopLogBO(null, shopExt));
        });
        return r.getShopId();
    }

    @Override
    public Long modifyManagerInfo(CmdShopManagerReq r) {
        if (StrUtil.isEmpty(r.getRealName())) {
            throw new BusinessException(UserResultCodeEnum.REAL_NAME_NOT_NULL);
        }
        Manager manager = managerRepository.getByShopId(r.getShopId());
        Member member = memberRepository.getByUserName(manager.getUserName());
        ShopLogBO shopLogBO = new ShopLogBO();
        shopLogBO.setId(r.getShopId());
        shopLogBO.setRealName(member.getRealName());

        MemberContact contact = memberContactService.queryMemberContactPhone(member.getId());
        member.setRealName(r.getRealName());
        contact.setContact(r.getMemberPhone());

        TransactionHelper.doInTransaction(() -> {
            memberContactRepository.updateById(contact);
            memberRepository.updateById(member);
            ShopLogBO newShopLogBO = new ShopLogBO();
            newShopLogBO.setId(r.getShopId());
            newShopLogBO.setRealName(member.getRealName());
            baseLogAssist.recordLog(ExaminModelEnum.USER,
                    ExaProEnum.MODIFY,
                    "修改管理员信息",
                    r.getOperationUserId(), r.getOperationShopId(),
                    shopLogBO, newShopLogBO);
        });
        return r.getShopId();
    }

    @Override
    public BaseResp auditing(CmdShopStatusReq shopId) {
        Shop shop = shopRepository.getById(shopId.getShopId());
        ShopLogBO shopLogBO = new ShopLogBO(shop, null);
        if (shop == null) {
            throw new BusinessException(UserResultCodeEnum.SHOP_NOT_FIND);
        }
        if (!ShopEnum.PlatAuditStatus.WaitAudit.getCode().equals(shop.getPlateStatus())) {
            //店铺只有待审核才能审核
            throw new BusinessException(UserResultCodeEnum.SHOP_STATUS_ERROR);
        }
        String lockKey = LockConstant.BUSINESS_CATEGORY_APPLY_AUDIT_LOCK + shopId;
        String scene = LockConstant.BUSINESS_CATEGORY_APPLY_AUDIT_SCENE + shopId;
        distributedLockService.tryLock(new LockKey(lockKey, scene), () -> {
            // 获取店铺的类目申请记录
            BusinessCategoryApply businessCategoryApply = businessCategoryApplyService.getByShopIdOnAudit(shopId.getShopId());
            if (ShopEnum.PlatAuditStatus.Open.getCode().equals(shopId.getStatus())) {
                shop.setPlateStatus(shopId.getStatus());
                //判断汇付状态
                if (ShopEnum.AdapayAuditStatus.AdapaySuccess.getCode().equals(shop.getAdapayStatus())) {
                    log.info("店铺{}审核通过,汇付审核也通过", shopId.getShopId());
                    //审核通过 汇付状态也通过
                    shop.setShopStatus(ShopEnum.AuditStatus.Open.getCode());
                    shop.setStage(ShopEnum.ShopStage.Finish.getCode());
                    //审核通过 申请类目也通过
                    AuditBusinessCategoryApplyBo applyBo = new AuditBusinessCategoryApplyBo();
                    applyBo.setId(businessCategoryApply.getId());
                    applyBo.setPass(true);
                    businessCategoryApplyService.auditBusinessCategoryApply(applyBo);
                }
                shopRepository.updateById(shop);
            } else if (ShopEnum.PlatAuditStatus.Refuse.getCode().equals(shopId.getStatus())) {
                shop.setPlateStatus(ShopEnum.PlatAuditStatus.Refuse.getCode());
                shop.setRefuseReason(shopId.getRefuseReason());
                dealFailed(shopId, shop, businessCategoryApply);
            }
            baseLogAssist.recordLog(ExaminModelEnum.USER,
                    ExaProEnum.MODIFY,
                    "审核店铺",
                    shopId.getOperationUserId(), shopId.getOperationShopId(),
                    shopLogBO, new ShopLogBO(shop, null));
        });

        //发送拒绝短信
        Map<String, String> map = new HashMap<>();
        SmsBodyReq smsBodyReq = new SmsBodyReq();

        if (ShopEnum.PlatAuditStatus.Open.getCode().equals(shopId.getStatus())) {
            smsBodyReq.setTemplateId(userLionConfigClient.getReviewApprovedReminder());
        } else {
            //发送拒绝短信
            smsBodyReq.setTemplateId(userLionConfigClient.getReviewFailureReminder());
            map.put("reason", shopId.getRefuseReason());
        }
        smsBodyReq.setParam(JsonUtil.toJsonString(map));
        smsBodyReq.setRequestId(leafService.generateNoBySnowFlake(LeafConstant.KEY_USER_NO));
        // 设置收信人
        ContactReq contactReq = new ContactReq();
        contactReq.setMobile(shop.getContactsPhone());
        smsBodyReq.setContactList(Collections.singletonList(contactReq));
        messageCMDFeign.sendSms(smsBodyReq);
        return BaseResp.of();
    }

    @Override
    public BaseResp adaAuditing(CmdShopStatusReq shopId) {
        Shop shop = shopRepository.getById(shopId.getShopId());
        ShopLogBO shopLogBO = new ShopLogBO(shop, null);
        if (shop == null) {
            throw new BusinessException(UserResultCodeEnum.SHOP_NOT_FIND.getCode(), UserResultCodeEnum.SHOP_NOT_FIND.getMsg());
        }
        // 获取店铺的类目申请记录
        BusinessCategoryApply businessCategoryApply = businessCategoryApplyService.getByShopIdOnAudit(shopId.getShopId());
        //审核通过并且结算id不为null
        if (ShopEnum.AdapayAuditStatus.AdapaySuccess.getCode().equals(shopId.getStatus()) && shopId.getSettleAccountId() != null) {
            log.info("结算账号创建{}", shopId.getShopId());
            //            审核通过创建结算用户
            CreateAccountReq createAccountReq = new CreateAccountReq();
            createAccountReq.setShopId(shop.getId());
            createAccountReq.setShopName(shop.getShopName());
            createAccountReq.setAccountId(shopId.getSettleAccountId());
            ThriftResponseHelper.executeThriftCall(() -> shopAccountCmdFeign.createAccount(createAccountReq));
        }

        TransactionHelper.doInTransaction(() -> {
            if (ShopEnum.AdapayAuditStatus.AdapaySuccess.getCode().equals(shopId.getStatus())) {
                shop.setAdapayStatus(ShopEnum.AdapayAuditStatus.AdapaySuccess.getCode());
                //判断汇付状态
                if (ShopEnum.PlatAuditStatus.Open.getCode().equals(shop.getPlateStatus())) {
                    log.info("店铺{}审核通过,汇付审核也通过", shopId.getShopId());
                    //审核通过 汇付状态也通过
                    shop.setShopStatus(ShopEnum.AuditStatus.Open.getCode());
                    shop.setStage(ShopEnum.ShopStage.Finish.getCode());
                    //审核通过 申请类目也通过
                    if (businessCategoryApply != null) {
                        AuditBusinessCategoryApplyBo applyBo = new AuditBusinessCategoryApplyBo();
                        applyBo.setId(businessCategoryApply.getId());
                        applyBo.setPass(true);
                        businessCategoryApplyService.auditBusinessCategoryApply(applyBo);
                    }
                }
                shopRepository.updateById(shop);
            } else if (ShopEnum.AdapayAuditStatus.AdapayFailed.getCode().equals(shopId.getStatus())) {
                shop.setAdapayStatus(ShopEnum.AdapayAuditStatus.AdapayFailed.getCode());
                shop.setAdapayReason(shopId.getRefuseReason());
                dealFailed(shopId, shop, businessCategoryApply);

            }
            baseLogAssist.recordLog(ExaminModelEnum.USER,
                    ExaProEnum.MODIFY,
                    "汇付审核店铺",
                    shopId.getOperationUserId(), shopId.getOperationShopId(),
                    shopLogBO, new ShopLogBO(shop, null));
        });

        //发送短信
        try {
            Map<String, String> map = new HashMap<>();
            if (ShopEnum.AuditStatus.Open.getCode().equals(shop.getShopStatus())) {
                if (ShopEnum.AdapayAuditStatus.AdapayFailed.getCode().equals(shopId.getStatus())) {
                    messageRemoteService.sendSms(userLionConfigClient.getReviewFailureReminder(), JsonUtil.toJsonString(map), shop.getContactsPhone());
                } else {
                    messageRemoteService.sendSms(userLionConfigClient.getReviewApprovedReminder(), JsonUtil.toJsonString(map), shop.getContactsPhone());
                }

            }
        } catch (Exception e) {
            log.error("【店铺审核】发送短信异常", e);
        }
        return BaseResp.of();
    }

    @Override
    public BaseResp setShopType(ShopTypeCmdReq req) {
        Shop shop = shopRepository.getById(req.getShopId());
        ShopLogBO shopLogBO = new ShopLogBO(shop, null);
        if (shop == null) {
            throw new BusinessException(UserResultCodeEnum.SHOP_NOT_FIND);
        }
        shop.setShopType(req.getShopType());
        TransactionHelper.doInTransaction(() -> {
            shopRepository.updateById(shop);
            baseLogAssist.recordLog(ExaminModelEnum.USER,
                    ExaProEnum.MODIFY,
                    "更改店铺类型",
                    req.getOperationUserId(), req.getOperationShopId(),
                    shopLogBO, new ShopLogBO(shop, null));
        });
        return BaseResp.of();
    }

    @Override
    public Long editShopPersonal(CmdShopReq cmdShopReq) {
        Shop shop = shopRepository.getById(cmdShopReq.getShopId());
        ShopExt shopExt = shopExtRepository.getByShopId(cmdShopReq.getShopId());
        ShopLogBO shopLogBO = new ShopLogBO(shop, shopExt);
        Manager manager = managerRepository.getByShopId(cmdShopReq.getShopId());
        //校验业务类型
        if (!ShopEnum.BusinessType.PERSONAL.getCode().equals(shop.getBusinessType())) {
            throw new BusinessException(UserResultCodeEnum.INCORRECT_BUSINESS_TYPE.getCode(), UserResultCodeEnum.INCORRECT_BUSINESS_TYPE.getMsg());
        }
        TransactionHelper.doInTransaction(() -> {
            //设置基本熟悉
            shopAssistant.setCommonAttr(cmdShopReq, shop, manager);
            //设置拓展信息
            shopAssistant.setPersonalShopExt(cmdShopReq, shopExt);
            esShopBuildService.refreshShopEs(cmdShopReq.getShopId());
            baseLogAssist.recordLog(ExaminModelEnum.USER,
                    ExaProEnum.MODIFY,
                    "平台编辑个人店铺信息",
                    cmdShopReq.getOperationUserId(), cmdShopReq.getOperationShopId(),
                    shopLogBO, new ShopLogBO(shop, shopExt));
        });
        return cmdShopReq.getShopId();
    }

    @Override
    public Long editShopEnterprise(CmdShopReq cmdShopReq) {
        Shop shop = shopRepository.getById(cmdShopReq.getShopId());
        ShopExt shopExt = shopExtRepository.getByShopId(cmdShopReq.getShopId());
        if (ShopEnum.AdapayAuditStatus.AdapayPending.getCode().equals(shop.getAdapayStatus())) {
            //恢复状态为审核中,不允许再次修改
            throw new BusinessException(UserResultCodeEnum.ADAPAY_AUDIT_STATUS_ERROR);
        }
        ShopLogBO shopLogBO = new ShopLogBO(shop, shopExt);
        Manager manager = managerRepository.getByShopId(cmdShopReq.getShopId());
        //校验业务类型
        if (!ShopEnum.BusinessType.ENTERPRISE.getCode().equals(shop.getBusinessType())) {
            throw new BusinessException(UserResultCodeEnum.INCORRECT_BUSINESS_TYPE.getCode(), UserResultCodeEnum.INCORRECT_BUSINESS_TYPE.getMsg());
        }
        //设置汇付审核状态
        shop.setAdapayStatus(ShopEnum.AdapayAuditStatus.AdapayPending.getCode());
//        通知汇付修改企业信息
        PayCorpMemberReq adaPayCorpMemberReq = getPayCorpMemberReq(shop, shopExt);
        log.info("修改企业信息通知汇付,请求参数:{}", JsonUtil.toJsonString(adaPayCorpMemberReq));
        financeMemberRemoteService.updateCompanyMember(adaPayCorpMemberReq);
        TransactionHelper.doInTransaction(() -> {
            //设置基本熟悉
            shopAssistant.setCommonAttr(cmdShopReq, shop, manager);
            //设置企业信息
            shopAssistant.setShopExt(cmdShopReq, shopExt);
            esShopBuildService.refreshShopEs(cmdShopReq.getShopId());
            baseLogAssist.recordLog(ExaminModelEnum.USER,
                    ExaProEnum.MODIFY,
                    "平台编辑企业店铺信息",
                    cmdShopReq.getOperationUserId(), cmdShopReq.getOperationShopId(),
                    shopLogBO, new ShopLogBO(shop, shopExt));
        });
        return cmdShopReq.getShopId();
    }

    @Override
    public Long freezeShop(CmdShopStatusReq cmdShopStatusReq) {
        Shop shop = shopRepository.getById(cmdShopStatusReq.getShopId());
        //判断店铺是否存在
        if (shop == null) {
            throw new BusinessException(UserResultCodeEnum.SHOP_NOT_FIND.getCode(), UserResultCodeEnum.SHOP_NOT_FIND.getMsg());
        }
        ShopLogBO shopLogBO = new ShopLogBO(shop, null);
        shopLogBO.setShopStatus(ShopEnum.AuditStatus.getDescByCode(shop.getShopStatus()));
        if (ShopEnum.AuditStatus.Open.getCode().equals(shop.getShopStatus())) {
            shop.setShopStatus(ShopEnum.AuditStatus.Freeze.getCode());
            //下架商品
            BaseIdReq baseIdReq = new BaseIdReq();
            baseIdReq.setId(shop.getId());
            shopMafkaProducer.sendMessage(shop.getId());
            //下架活动
            ThriftResponseHelper.executeThriftCall(() -> shopUserPromotionCmdFeign.offSaleAllPromotion(baseIdReq));
            log.info("店铺冻结，下架商品和活动成功,店铺ID{}", shop.getId());
        } else if (ShopEnum.AuditStatus.Freeze.getCode().equals(shop.getShopStatus())) {
            shop.setShopStatus(ShopEnum.AuditStatus.Open.getCode());
        }
        TransactionHelper.doInTransaction(() -> {
            shopRepository.updateById(shop);
            esShopBuildService.refreshShopEs(cmdShopStatusReq.getShopId());
            ShopLogBO newValue = new ShopLogBO(shop, null);
            newValue.setShopStatus(ShopEnum.AuditStatus.getDescByCode(shop.getShopStatus()));
            baseLogAssist.recordLog(ExaminModelEnum.USER,
                    ExaProEnum.MODIFY,
                    "店铺冻结/解冻",
                    cmdShopStatusReq.getOperationUserId(), cmdShopStatusReq.getOperationShopId(),
                    shopLogBO, newValue);
        });
        return cmdShopStatusReq.getShopId();
    }

    //    批量更新序号
    @Override
    @ExaminProcess(processModel = ExaminModelEnum.USER, actionName = "更新店铺序号", processType = ExaProEnum.MODIFY,
            serviceMethod = "updateSeq", dto = CmdSingleShopSeqReq.class, entity = Shop.class)
    public BaseResp updateSeq(CmdSingleShopSeqReq cmdShopSeqReq) {
        //判断shopIdList是否为空
        if (cmdShopSeqReq.getShopId() != null) {
            shopRepository.lambdaUpdate()
                    .set(Shop::getSerialNumber, cmdShopSeqReq.getSerialNumber())
                    .eq(Shop::getId, cmdShopSeqReq.getShopId())
                    .update();
        }
        return BaseResp.of();
    }

    @Override
    public ShopDetailResp queryDetailIncludeFreeze(BaseIdReq shopId) {
        Shop shop = shopRepository.getById(shopId.getId());
        //判空
        if (shop == null) {
            throw new BusinessException(UserResultCodeEnum.SHOP_NOT_FIND);
        }
        //获取店铺扩展信息
        ShopExt shopExt = shopExtRepository.getByShopId(shopId.getId());
        ShopDetailResp shopDetailResp = new ShopDetailResp();
        BeanUtil.copyProperties(shopExt, shopDetailResp);
        BeanUtil.copyProperties(shop, shopDetailResp);
        shopDetailResp.setLogo(shop.getLogo());
        //设置idCardUrl
        shopDetailResp.setIdCardUrl(shop.getIdCardurl());
        shopDetailResp.setIdCardUrl2(shop.getIdCardurl2());
        //加密字段还原
        shopDetailResp.setBankAccountNumber(AesUtil.decrypt(shop.getBankAccountNumber(), encryptConfig.getAesSecret()));
        shopDetailResp.setIdCard(AesUtil.decrypt(shop.getIdCard(), encryptConfig.getAesSecret()));
        //获取店铺类目
        List<BusinessCategoryResp> businessCategories = null;
        if (!ShopEnum.AuditStatus.Freeze.getCode().equals(shop.getBusinessType()) || !ShopEnum.AuditStatus.Open.getCode().equals(shop.getBusinessType())) {
            businessCategories = businessCategoryApplyService.queryDetailByShopId(shop.getId());
            businessCategoryService.setCategoryName(businessCategories);
        } else {
            businessCategories = businessCategoryService.queryListByShopId(shop.getId());
        }
        if (CollUtil.isNotEmpty(businessCategories)) {
            shopDetailResp.setBusinessCategory(businessCategories);
            //获取所有类目ID
            List<Long> categoryIds = businessCategories.stream().map(BusinessCategoryResp::getCategoryId).collect(Collectors.toList());
            //获取所有类目ID的1级类目
            List<RemoteCategoryBo> parentCategoryIds = remoteCategoryService.queryFirstCategoryList(categoryIds);
            //获取店铺类目自定义表单
            List<CategoryApplyFormDto> businessCategoryForms = businessCategoryFormService.queryFormListByShopId(shop.getId(), parentCategoryIds.stream().map(RemoteCategoryBo::getId).collect(Collectors.toList()));
            //完善自定义表单字段
            shopDetailResp.setFieldList(businessCategoryForms);
        }
        //获取店铺商家信息
        ShopMemberInfoBO shopMemberInfoBO = managerService.queryEpManagerByShopId(shop.getId());
        shopDetailResp.setRealName(shopMemberInfoBO.getRealName());
        shopDetailResp.setMemberPhone(shopMemberInfoBO.getPhone());
        shopDetailResp.setMemberEmail(shopMemberInfoBO.getEmail());
        dealRegionName(shopDetailResp);
        return shopDetailResp;
    }

    @Override
    public Boolean enoughCashFlag(EnoughCashFlagReq enoughCashFlagReq) {
        BusinessCategory businessCategory = businessCategoryService.queryOne(enoughCashFlagReq.getShopId(), enoughCashFlagReq.getCategoryId());
        BusinessCategory category = businessCategoryService.queryShopMaxBond(enoughCashFlagReq.getShopId());
        if (Objects.isNull(businessCategory)) {
            return false;
        }
        if (Objects.isNull(category)) {
            return false;
        }
        return businessCategory.getBond().compareTo(category.getBond()) > 0;
    }

    @Override
    public void initFrom() {
        log.info("初始化店铺自定义表单");
        //先查询所有店铺
        List<Shop> shopList = shopRepository.lambdaQuery()
                .like(Shop::getFormData, "Field_").list();

        for (Shop shop : shopList) {
            //自定义表单不为空
            if (StrUtil.isNotEmpty(shop.getFormData())) {
                try {
                    JSONArray jsonArray = new JSONArray();
//                    转化为js
                    JSONObject jsonObject = JSONUtil.parseObj(shop.getFormData());
//                    遍历
                    Map<Long, String> map = new HashMap<>();
                    List<Long> list = new ArrayList<>();
                    for (String key : jsonObject.keySet()) {
//                        判断key包含Field_
                        if (key.contains("Field_")) {
//                            获取自定义表单id
                            Long id = Long.valueOf(key.replace("Field_", ""));
                            list.add(id);
                            map.put(id, jsonObject.getStr(key));
                        }
                    }

                    List<BaseCustomFormField> baseCustomFormFields = customFormService.queryCustomFieldByFieldIds(list);
                    if (CollUtil.isNotEmpty(baseCustomFormFields)) {
                        for (BaseCustomFormField baseCustomFormFieldRes : baseCustomFormFields) {
                            JSONObject jsonObject1 = new JSONObject();
//                                {\"name\":\"sad\",\"type\":0,\"format\":0,\"options\":\"\",\"required\":true,\"sort\":0,\"value\":\"asaas\"}
                            jsonObject1.set("name", baseCustomFormFieldRes.getFieldName());
                            jsonObject1.set("type", baseCustomFormFieldRes.getType());
                            jsonObject1.set("format", baseCustomFormFieldRes.getFormat());
                            jsonObject1.set("options", baseCustomFormFieldRes.getOption());
                            jsonObject1.set("required", baseCustomFormFieldRes.getIsRequired());
                            jsonObject1.set("sort", baseCustomFormFieldRes.getDisplaySequence());
                            jsonObject1.set("value", map.get(baseCustomFormFieldRes.getId()));
                            jsonArray.add(jsonObject1);
                        }
                        shop.setFormData(jsonArray.toString());
                        shopRepository.updateById(shop);
                    } else {
                        shop.setFormData(null);
                        shopRepository.updateById(shop);
                    }
                } catch (Exception e) {
                    log.error("同步店铺表单数据失败", e);
                }
            }
        }
        log.info("初始化店铺自定义表单-结束");
    }

    @Override
    public ShopResp queryShop(BaseIdReq shopId) {
        Shop shop = shopRepository.selectById(shopId.getId());
        return JsonUtil.copy(shop, ShopResp.class);
    }

    @Override
    public Boolean setRegisterState(Long shopId) {
        Shop shop = shopRepository.getById(shopId);
        if (shop == null) {
            throw new BusinessException("未初始开放平台");
        }
        String postData = String.format("appKey=%s&appSecret=%s&routeAddress=%s", shop.getAppKey(), shop.getAppSecret(), currentDomainUrl);
        String rData = this.registeWayBill(erpUri, postData);
        log.info("电子面单注册返回：{}", rData);
        boolean isReged = false;
        if (StringUtils.isNotBlank(rData)) {
            if (rData.replace(" ", "").toLowerCase().contains("\"success\":true")) {
                isReged = true;
            }
        }
        shop.setRegisted(isReged);
        //更新注册状态
        shopRepository.updateById(shop);
        if (!isReged) {
            log.warn("远程注册异常!,原因：{}", rData);
        }
        return isReged;
    }

    @Override
    public String goExpressBills(Long shopId) {
        String goUrl = erpUri + "/ExpressBill/Allot?app_key={0}&timestamp={1}&sign={2}";
        Shop shopOpen = shopRepository.getById(shopId);
        if (shopOpen == null) {
            throw new BusinessException("未开启开放平台");
        }
        if (!StringUtils.isEmpty(shopOpen.getAppKey()) && !StringUtils.isEmpty(shopOpen.getAppSecret())) {
            if (shopOpen.getEnable() && shopOpen.getRegisted()) {
                SortedMap<String, String> data = new TreeMap<>();
                data.put("app_key", shopOpen.getAppKey());
                String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                data.put("timestamp", timestamp);
                String sign = HishopErpSignUtil.getSign(data, shopOpen.getAppSecret());
                goUrl = String.format(goUrl, shopOpen.getAppKey(), timestamp, sign);
                return goUrl;
            }
            if (!shopOpen.getRegisted()) {
                throw new BusinessException("未完成ERP电子面单系统注册");
            }
        } else {
            throw new BusinessException("未开启开放平台");
        }
        return "";
    }

    @Override
    public void saveShopLogo(SaveShopLogoReq req) {
        Shop shop = new Shop();
        shop.setId(req.getShopId());
        shop.setLogo(Optional.ofNullable(req.getLogo()).orElse(StrUtil.EMPTY));
        shopRepository.updateById(shop);
        esShopBuildService.refreshShopEs(req.getShopId());
    }

    @Override
    public ShopLogoResp getShopLogo(Long shopId) {
        ShopLogoResp resp = new ShopLogoResp();
        resp.setShopId(shopId);

        Shop shop = shopRepository.getById(shopId);
        if (shop != null) {
            resp.setLogo(shop.getLogo());
        }
        return resp;
    }

    private String registeWayBill(String uri, String postData) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(postData, headers);
//        return restTemplate.postForObject(uri, entity, String.class);
        return null;
    }


}
