package com.sankuai.shangou.seashop.user.shop.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.shop.service.ShopInvoiceQueryService;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopInvoiceQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.BatchQueryShopInvoiceReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopInvoiceReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopInvoiceListResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopInvoiceResp;

import lombok.extern.slf4j.Slf4j;

/**
 * @author： liweisong
 * @create： 2023/11/29 8:59
 */
@Slf4j
@RestController
@RequestMapping("/shop/shopInvoice")
public class ShopInvoiceQueryController implements ShopInvoiceQueryFeign {

    @Resource
    private ShopInvoiceQueryService shopInvoiceQueryService;

    @Override
    public ResultDto<QueryShopInvoiceResp> queryShopInvoice(QueryShopInvoiceReq queryShopInvoiceReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryShopInvoice", queryShopInvoiceReq, req -> {
            queryShopInvoiceReq.checkParameter();
            return shopInvoiceQueryService.queryShopInvoice(req);
        });
    }

    @Override
    public ResultDto<QueryShopInvoiceListResp> batchQueryInvoiceSetting(BatchQueryShopInvoiceReq queryShopInvoiceReq) throws TException {
        log.info("【店铺】批量查询发票设置, 请求参数={}", JsonUtil.toJsonString(queryShopInvoiceReq));
        return ThriftResponseHelper.responseInvoke("batchQueryInvoiceSetting", queryShopInvoiceReq, req -> {
            queryShopInvoiceReq.checkParameter();
            return shopInvoiceQueryService.batchQueryInvoiceSetting(req);
        });
    }
}
