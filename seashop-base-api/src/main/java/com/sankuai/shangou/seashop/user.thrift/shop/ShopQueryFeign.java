package com.sankuai.shangou.seashop.user.thrift.shop;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.*;
import com.sankuai.shangou.seashop.user.thrift.shop.response.*;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商查询服务类
 */
@FeignClient(name = "himall-base", contextId = "ShopQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/shop/shop")
public interface ShopQueryFeign {

    /**
     * 供应商简要信息列表
     * @return 供应商简要信息列表
     */
    @PostMapping(value = "/getShopList", consumes = "application/json")
    ResultDto<ShopRespList> getShopList(@RequestBody QueryShopPageReq queryShopPageReq) throws TException;


    /**
     * 查询供应商id的集合
     */
    @PostMapping(value = "/getShopIds", consumes = "application/json")
    ResultDto<ShopIdsResp> getShopIds(@RequestBody QueryShopReq queryShopReq) throws TException;

    /**
     * 通过id查询运费设置信息
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/getShippingSettings", consumes = "application/json")
    ResultDto<ShippingSettingsResp> getShippingSettings(@RequestBody BaseIdReq request) throws TException;

    /**
     * 分页查询店铺信息
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryPage", consumes = "application/json")
    ResultDto<BasePageResp<ShopResp>> queryPage(@RequestBody ShopQueryPagerReq request) throws TException;

    /**
     * 查询店铺分类详情
     * @param shopId
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryShopCategoryDetail", consumes = "application/json")
    ResultDto<String> queryShopCategoryDetail(@RequestBody BaseIdReq shopId) throws TException;

    /**
     * 查询店铺详情
     *
     * @param shopId 店铺ID
     * @return 查询结果
     */
    @PostMapping(value = "/queryDetail", consumes = "application/json")
    ResultDto<ShopDetailResp> queryDetail(@RequestBody BaseIdReq shopId) throws TException;

    /**
     * 查询店铺详情(包括冻结的店铺)
     * @param shopId
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryDetailIncludeFreeze", consumes = "application/json")
    ResultDto<ShopDetailResp> queryDetailIncludeFreeze(@RequestBody BaseIdReq shopId) throws TException;

    /**
     * TODO 待实现 注意：（已签约的店铺，即使后续合同过期未续签合同）
     * 通过名称或者id查询已签约店铺列表（id、名称、需要缴纳的最大保证金）
     */
    @PostMapping(value = "/querySimpleList", consumes = "application/json")
    ResultDto<ShopSimpleListResp> querySimpleList(@RequestBody ShopSimpleQueryReq request) throws TException;

    /**
     * TODO 待实现 注意：（已签约的店铺，即使后续合同过期未续签合同）
     * 通过名称或者id查询已签约店铺列表（id、名称、需要缴纳的最大保证金）
     */
    @PostMapping(value = "/querySimplePage", consumes = "application/json")
    ResultDto<BasePageResp<ShopSimpleResp>> querySimplePage(@RequestBody ShopQueryPagerReq queryPagerReq) throws TException;

    /**
     * 查询商品店铺信息
     * @param baseIdReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryProductShop", consumes = "application/json")
    ResultDto<ProductShopInfoResp> queryProductShop(@RequestBody ProductShopQueryReq baseIdReq) throws TException;

    /**
     * 根据店铺ID集合查询店铺基本信息集合（最多一次性查200个）
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryShopsByIds", consumes="application/json")
    ResultDto<List<ShopResp>> queryShopsByIds(@RequestBody ShopQueryReq request) throws TException;

    /**
     * 统计平台用户
     * @return
     * @throws TException
     */
    @GetMapping("/countShopUser")
    ResultDto<ShopUserCountResp> countShopUser() throws TException;

    /**
     * 通过ES查询店铺信息（主要是商城店铺查询使用）
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryByShopEs", consumes = "application/json")
    ResultDto<BasePageResp<ShopEsResp>> queryByShopEs(@RequestBody ShopEsQueryReq request) throws TException;

    /**
     * 通过店铺ID集合查询店铺信息
     *
     * @param shopId 店铺ID集合
     * @return 店铺信息列表
     * @throws TException RPC调用异常
     */
    @GetMapping(value = "/getYesterdayShopUV")
    ResultDto<Long> getYesterdayShopUV(@RequestParam Long shopId) throws TException;

    /**
     * 查询店铺介绍
     * @param baseIdReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/shopIntroduction", consumes = "application/json")
    ResultDto<ShopIntroductionResp> shopIntroduction(@RequestBody BaseIdReq baseIdReq) throws TException;

    /**
     * 查询自营店铺介绍
     * @return
     * @throws TException
     */
    @GetMapping("/selfShopInfo")
    ResultDto<ShopSimpleResp> selfShopInfo() throws TException;

    /**
     * 查询店铺介绍根据UserID
     * @param baseIdReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/shopInfoByUserId", consumes = "application/json")
    ResultDto<ShopSimpleResp> shopInfoByUserId(@RequestBody BaseIdReq baseIdReq) throws TException;

    /**
     * 查询店铺和经营类目ID查询保证金是否足够
     * @param enoughCashFlagReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/enoughCashFlag", consumes = "application/json")
    ResultDto<Boolean> enoughCashFlag(@RequestBody EnoughCashFlagReq enoughCashFlagReq) throws TException;

    /**
     * 查询店铺简要信息
     * @param shopId
     * @return
     */
    @PostMapping(value = "/queryShop", consumes = "application/json")
    ResultDto<ShopResp> queryShop(@RequestBody BaseIdReq shopId);
}
