package com.sankuai.shangou.seashop.user.thrift.account.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/12/12 16:44
 */
@Data
public class UserBaseResp extends BaseThriftDto {


    /**
     * 商家头像
     */
    private String photo;

    /**
     * 商家账号
     */
    private String userName;

    /**
     * 账户安全级别
     */
    private Integer accountSafetyLevel;

    /**
     * 支付密码
     */
    private String payPwd;

    /**
     * 手机号
     */
    private String cellPhone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 是否绑定手机号，true绑定，false未绑定
     */
    private Boolean bindPhone;

    /**
     * 是否绑定邮箱，true绑定，false未绑定
     */
    private Boolean bindEmail;

    /**
     * 是否是供应商，true是，false不是
     */
    private Boolean supplier;
}
