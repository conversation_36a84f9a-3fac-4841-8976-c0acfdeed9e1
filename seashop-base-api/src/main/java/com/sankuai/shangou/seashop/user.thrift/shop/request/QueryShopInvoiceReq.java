package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/11/29 8:55
 */
@Data
public class QueryShopInvoiceReq extends BaseParamReq {

    /**
     * 店铺ID
     */
    private Long shopId;

    public void checkParameter(){
        if(shopId == null){
            throw new IllegalArgumentException("店铺shopId不能为空");
        }
    }
}
