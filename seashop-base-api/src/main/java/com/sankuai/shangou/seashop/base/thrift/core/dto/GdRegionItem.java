package com.sankuai.shangou.seashop.base.thrift.core.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.*;

import java.util.List;

@JsonSerialize
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@Getter
@Setter
public class GdRegionItem {
    private Long id;
//    private String citycode;
    private String adcode;
    private String name;
    private String center;
    private List<GdRegionItem> districts;

    private String parentAdcode;
    private Long parentId;
    private int regionLevel;
}
