package com.sankuai.shangou.seashop.base.thrift.core.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

@JsonSerialize
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@Data
public class BaseRegionRes extends BaseThriftDto {

    /**
     * 地址编号
     */
    private Long id;


    /**
     * 美团区域编号
     */
    private String code;


    /**
     * 区域名称
     */
    private String name;


    /**
     * 区域简称
     */
    private String shortName;


    /**
     * 状态 0：正常 9：删除
     */
    private int status;

    /**
     * 父id
     */
    private Long parentId;
    /**
     * 地址级别
     */
    private int regionLevel;

}
