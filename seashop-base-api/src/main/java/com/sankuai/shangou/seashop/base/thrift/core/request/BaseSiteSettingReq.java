package com.sankuai.shangou.seashop.base.thrift.core.request;

import org.apache.commons.lang3.StringUtils;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;

import lombok.Data;

@Data
public class BaseSiteSettingReq extends BaseParamReq {
    /**
     * 站点名称
     */
    @ExaminField(description = "站点名称")
    private String siteName;

    /**
     * 站点logo
     */
    @ExaminField(description = "站点logo")
    private String logo;

    /**
     * 卖家中心Logo
     */
    @FieldDoc(description = "卖家中心Logo", requiredness = Requiredness.REQUIRED)
    private String memberLogo;

    /**
     * 微信logo
     */
    @ExaminField(description = "微信logo")
    private String wxLogo;

    /**
     * 客服电话
     */
    @ExaminField(description = "客服电话")
    private String sitePhone;

    /**
     * 网店授权域名
     */
    @ExaminField(description = "网店授权域名")
    private String siteUrl;

    //第三方流量统计代码
    @ExaminField(description = "第三方流量统计代码")
    private String thirdPartyFlowCode;

    //    是否提供app下载
    @ExaminField(description = "是否提供app下载")
    private String isAppDownload;

    //    商城app版本号
    @ExaminField(description = "商城app版本号")
    private String appVersion;

    //    appleStore
    @ExaminField(description = "appleStore")
    private String appleStore;

    //    android
    @ExaminField(description = "android")
    private String android;

    //    供应商app更新说明
    @ExaminField(description = "供应商app更新说明")
    private String appUpdate;

    /**
     * 高德地图key
     */
    @ExaminField(description = "高德地图key")
    private String jDRegionAppKey;

    /**
     * 店铺id
     */
    @PrimaryField
    private long shopId;

    @ExaminField(description = "热门关键字")
    private String popularKeyWords;

    public void checkParameter() {
        if (StringUtils.isEmpty(this.siteName)) {
            throw new IllegalArgumentException("站点不能为空");
        }
//        if (StringUtils.isEmpty(this.logo)) {
//            throw new IllegalArgumentException("站点logo不能为空");
//        }
        if (StringUtils.isEmpty(this.sitePhone)) {
            throw new IllegalArgumentException("客服不能为空");
        }
    }
}
