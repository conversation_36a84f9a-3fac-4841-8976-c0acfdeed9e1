package com.sankuai.shangou.seashop.user.thrift.shop;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveShopInvoiceReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @description：发票管理
 * @author： liweisong
 * @create： 2023/11/29 8:50
 */
@FeignClient(name = "himall-base", contextId = "ShopInvoiceCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/shop/shopInvoice")
public interface ShopInvoiceCmdFeign {

    /**
     * 供应商后台-店铺-发票管理(保存)
     * @param saveShopInvoiceReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/saveShopInvoice", consumes = "application/json")
    ResultDto<BaseResp> saveShopInvoice(@RequestBody SaveShopInvoiceReq saveShopInvoiceReq) throws TException;
}
