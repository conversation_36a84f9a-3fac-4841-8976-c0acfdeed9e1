<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hishop.himall</groupId>
        <artifactId>himall-base</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>seashop-base-api</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-boot</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-common</artifactId>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>hishop-xxl-job-client-boot-starter</artifactId>
                    <groupId>com.hishop.starter</groupId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>
</project>
