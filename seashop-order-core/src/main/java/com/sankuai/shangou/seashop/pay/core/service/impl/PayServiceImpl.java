package com.sankuai.shangou.seashop.pay.core.service.impl;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ClassUtils;

import com.google.common.collect.Lists;
import com.huifu.adapay.core.exception.BaseAdaPayException;
import com.huifu.adapay.model.AdapayCommon;
import com.huifu.adapay.model.Bill;
import com.huifu.adapay.model.CorpMember;
import com.huifu.adapay.model.Member;
import com.huifu.adapay.model.Payment;
import com.huifu.adapay.model.PaymentConfirm;
import com.huifu.adapay.model.PaymentReverse;
import com.huifu.adapay.model.SettleAccount;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.leaf.LeafService;
import com.sankuai.shangou.seashop.base.s3plus.S3plusStorageService;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.pay.common.constant.AdaPayConstant;
import com.sankuai.shangou.seashop.pay.common.constant.LockConstant;
import com.sankuai.shangou.seashop.pay.common.enums.AdaPayEnums;
import com.sankuai.shangou.seashop.pay.common.enums.AdaPaymentTypeEnum;
import com.sankuai.shangou.seashop.pay.common.util.BeanUtilMap;
import com.sankuai.shangou.seashop.pay.core.config.AdaPayInitWithMerConfig;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPayBillDownloadDto;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPayBillDownloadResultDto;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPayCorpMemberDto;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPayPaymentDeviceInfoDto;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPayPaymentListQueryDto;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPayPaymentResultDto;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPayReverseCreateDto;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPayReverseCreateResultDto;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPaymentConfirmCreateDivMembersDto;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPaymentConfirmCreateDto;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPaymentConfirmCreateResultDto;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPaymentCreateDto;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPaymentResultDto;
import com.sankuai.shangou.seashop.pay.core.mq.model.OrderReverseExceptionMsg;
import com.sankuai.shangou.seashop.pay.core.mq.publisher.OrderReverseExceptionProducer;
import com.sankuai.shangou.seashop.pay.core.remote.OrderQueryRemoteService;
import com.sankuai.shangou.seashop.pay.core.service.ExchangeLogService;
import com.sankuai.shangou.seashop.pay.core.service.PayOrderService;
import com.sankuai.shangou.seashop.pay.core.service.PayService;
import com.sankuai.shangou.seashop.pay.dao.core.domain.OrderPay;
import com.sankuai.shangou.seashop.pay.dao.core.domain.ReverseOrder;
import com.sankuai.shangou.seashop.pay.dao.core.repository.OrderPayRepository;
import com.sankuai.shangou.seashop.pay.dao.core.repository.ReverseOrderRepository;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PayResultCodeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PayStateEnums;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentChannelEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentTypeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.ReverseStateEnums;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.ReverseTypeEnums;
import com.sankuai.shangou.seashop.pay.thrift.core.request.BillDownloadReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.ExchangeLogReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.OrderPayQueryReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPayBaseReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPayCorpMemberReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPayMemberAndAccountReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPayMemberReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPayPaymentCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPayReverseCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPaySettleAccountQryReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPaySettleAccountReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPaymentConfirmCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.dto.AdaPaymentConfirmDivMembersDto;
import com.sankuai.shangou.seashop.pay.thrift.core.response.BillDownloadResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.OrderPayResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.adapay.AdaPayPaymentCreateResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.adapay.AdaPayReverseCreateResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.adapay.AdaPaySettleAccountResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.adapay.AdaPaymentConfirmCreateResp;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.emoji.EmojiUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description:
 */
@Service
@Slf4j
public class PayServiceImpl implements PayService {

    @Resource
    private LeafService leafService;
    /**
     * 汇付回调地址
     */
    @Value("${adapay.callback.url}")
    private String adaPayCallbackUrl;
    /**
     * T打开，F不打开 【关联需求】线上止损方案支付开关：是否允许付款；
     */
    @Value("${create.payment.switch:'T'}")
    private String createPaymentSwitch;

    /**
     * T打开，F不打开 【关联需求】线上止损方案支付开关：是否允许退款 ；
     */
    @Value("${create.payment.reverse:'T'}")
    private String createPaymentReverseSwitch;

    /**
     * T打开，F不打开 【关联需求】线上止损方案分账开关：是否允许分账；
     */
    @Value("${create.payment.confirm:'T'}")
    private String createPaymentConfirmSwitch;
    @Resource
    private S3plusStorageService s3plusStorageService;
    @Resource
    private OrderPayRepository orderPayRepository;
    @Resource
    private ReverseOrderRepository reverseOrderRepository;
    @Resource
    private OrderReverseExceptionProducer orderReverseExceptionProducer;
    @Resource
    private ExchangeLogService exchangeLogService;
    @Resource
    private OrderQueryRemoteService orderQueryRemoteService;
    @Resource
    private PayOrderService payOrderService;
    @Override
    public void queryMember(AdaPayBaseReq request) {
        request.setAppId(AdaPayInitWithMerConfig.appId);
        Map<String, Object> adaPayMemberMap = BeanUtil.beanToMap(request, true, true);
        log.info("查询账号请求参数 adaPayMemberMap={}", JSONUtil.toJsonStr(adaPayMemberMap));
        try {
            Map<String, Object> returnMap = Member.query(adaPayMemberMap);
            log.info("查询账号返回参数 returnMap={}", JSONUtil.toJsonStr(returnMap));
            if (returnMap == null) {
                throw new BusinessException(PayResultCodeEnum.PAY_QUERY_ACCOUNT_ERROR.getCode(), PayResultCodeEnum.PAY_MEMBER_CREATE_ERROR.getMsg());
            }
            if (AdaPayEnums.SUCCEEDED.getStatus().equals(returnMap.get(AdaPayConstant.STATUS))){
                return;
            }
            throw new BusinessException(PayResultCodeEnum.PAY_QUERY_ACCOUNT_ERROR.getCode(), returnMap.get(AdaPayConstant.ERROR_MSG).toString());

        } catch (BaseAdaPayException e) {
            log.error("查询账号返回参数 code={}, message={}", e.getCode(), e.getMessage(), e);
            throw new BusinessException(PayResultCodeEnum.PAY_QUERY_ACCOUNT_ERROR.getCode(), e.getMessage());
        }
    }

    @Override
    public void createMember(AdaPayMemberReq request) {
        request.setAppId(AdaPayInitWithMerConfig.appId);
        Map<String, Object> adaPayMemberMap = BeanUtil.beanToMap(request, true, true);
        log.info("创建对私类型账号请求参数 adaPayMemberMap={}", JSONUtil.toJsonStr(adaPayMemberMap));
        try {
            Map<String, Object> returnMap = Member.create(adaPayMemberMap);
            log.info("创建对私类型账号返回参数 returnMap={}", JSONUtil.toJsonStr(returnMap));
            if (returnMap == null) {
                throw new BusinessException(PayResultCodeEnum.PAY_MEMBER_CREATE_ERROR.getCode(), PayResultCodeEnum.PAY_MEMBER_CREATE_ERROR.getMsg());
            }
            if (AdaPayEnums.SUCCEEDED.getStatus().equals(returnMap.get(AdaPayConstant.STATUS))) {
                return;
            }
            throw new BusinessException(PayResultCodeEnum.PAY_MEMBER_CREATE_ERROR.getCode(), returnMap.get(AdaPayConstant.ERROR_MSG).toString());

        } catch (BaseAdaPayException e) {
            log.error("创建对私类型账号异常 code={}, message={}", e.getCode(), e.getMessage(), e);
            throw new BusinessException(PayResultCodeEnum.PAY_MEMBER_CREATE_ERROR.getCode(), e.getMessage());
        }
    }

    @Override
    public AdaPaySettleAccountResp createSettleAccount(AdaPaySettleAccountReq request) {
        request.setAppId(AdaPayInitWithMerConfig.appId);
        Map<String, Object> adaPaySettleAccountMap = BeanUtil.beanToMap(request, true, true);
        log.info("创建结算账号对象请求参数 adaPaySettleAccountMap={}", JSONUtil.toJsonStr(adaPaySettleAccountMap));
        try {
            Map<String, Object> returnMap = SettleAccount.create(adaPaySettleAccountMap);
            log.info("创建结算账号对象返回参数 returnMap={}", JSONUtil.toJsonStr(returnMap));
            if (returnMap == null) {
                throw new BusinessException(PayResultCodeEnum.PAY_SETTLE_ACCOUNT_CREATE_ERROR.getCode(), PayResultCodeEnum.PAY_SETTLE_ACCOUNT_CREATE_ERROR.getMsg());
            }
            if (AdaPayEnums.SUCCEEDED.getStatus().equals(returnMap.get(AdaPayConstant.STATUS))) {
                return BeanUtil.fillBeanWithMap(returnMap, new AdaPaySettleAccountResp(), true, true);
            }
            throw new BusinessException(PayResultCodeEnum.PAY_SETTLE_ACCOUNT_CREATE_ERROR.getCode(), returnMap.get(AdaPayConstant.ERROR_MSG).toString());

        } catch (BaseAdaPayException e) {
            log.error("创建结算账号对象账号异常 code={}, message={}", e.getCode(), e.getMessage(), e);
            throw new BusinessException(PayResultCodeEnum.PAY_SETTLE_ACCOUNT_CREATE_ERROR.getCode(), e.getMessage());
        }
    }

    @Override
    public AdaPaySettleAccountResp createMemberAndSettleAccount(AdaPayMemberAndAccountReq request) {
        createMember(request.getPayMemberReq());
        return createSettleAccount(request.getPaySettleAccountReq());
    }

    @Override
    public void createCompanyMember(AdaPayCorpMemberReq request) {
        request.setAppId(AdaPayInitWithMerConfig.appId);
        request.setOrderNo(String.valueOf(leafService.generateNoBySnowFlake(AdaPayConstant.LEAF_ADAPAY_ORDER_NO_KEY)));
        request.setNotifyUrl(adaPayCallbackUrl);

        log.info("创建企业用户对象请求参数 request={}", JSONUtil.toJsonStr(request));
        AdaPayCorpMemberDto adaPayCorpMemberDTO = JsonUtil.copy(request, AdaPayCorpMemberDto.class);
        if(adaPayCorpMemberDTO.getSocialCreditCode().length() > 18){
            throw new BusinessException(PayResultCodeEnum.PAY_CORP_MEMBER_CREATE_ERROR.getCode(), "社会统一信用代码超长");
        }
        Map<String, Object> adaPayCorpMemberMap = BeanUtil.beanToMap(adaPayCorpMemberDTO, true, true);

        // 附件处理
        String attachFile = request.getAttachFile();
        File file = null;
        try (InputStream fileContent = s3plusStorageService.readExcelFromOnlineUrl(attachFile)) {
            StringBuilder str = new StringBuilder();
            str.append(ClassUtils.getDefaultClassLoader().getResource("").getPath());
            str.append(request.getName()).append("附件资料.zip");
            file = FileUtil.writeFromStream(fileContent, str.toString());
            Map<String, Object> returnMap = CorpMember.create(adaPayCorpMemberMap, file);
            if (returnMap == null) {
                throw new BusinessException(PayResultCodeEnum.PAY_CORP_MEMBER_CREATE_ERROR.getCode(), PayResultCodeEnum.PAY_CORP_MEMBER_CREATE_ERROR.getMsg());
            }
            log.info("创建企业用户对象返回参数 returnMap={}", JSONUtil.toJsonStr(returnMap));
            if ((AdaPayEnums.PENDING.getStatus().equals(returnMap.get(AdaPayConstant.STATUS))) || AdaPayEnums.SUCCEEDED.getStatus().equals(returnMap.get(AdaPayConstant.STATUS))) {
                return;
            }
            throw new BusinessException(PayResultCodeEnum.PAY_CORP_MEMBER_CREATE_ERROR.getCode(), returnMap.get(AdaPayConstant.ERROR_MSG).toString());
        } catch (IOException e) {
            log.error("创建企业用户对象账号IO异常", e);
            throw new BusinessException(PayResultCodeEnum.PAY_CORP_MEMBER_CREATE_ERROR.getCode(), e.getMessage());
        } catch (BaseAdaPayException e) {
            log.error("创建企业用户对象账号Ada异常 code={}, message={}", e.getCode(), e.getMessage(), e);
            throw new BusinessException(PayResultCodeEnum.PAY_CORP_MEMBER_CREATE_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("创建企业用户对象账号异常", e);
            throw new BusinessException(PayResultCodeEnum.PAY_CORP_MEMBER_CREATE_ERROR.getCode(), e.getMessage());
        } finally {
            if (null != file) {
                FileUtil.del(file);
            }
        }
    }

    @Override
    public boolean updateCompanyMember(AdaPayCorpMemberReq request) {
        request.setAppId(AdaPayInitWithMerConfig.appId);
        request.setOrderNo(String.valueOf(leafService.generateNoBySnowFlake(AdaPayConstant.LEAF_ADAPAY_ORDER_NO_KEY)));
        request.setNotifyUrl(adaPayCallbackUrl);

        log.info("更新企业用户对象请求参数 request={}", JSONUtil.toJsonStr(request));
        AdaPayCorpMemberDto adaPayCorpMemberDTO = JsonUtil.copy(request, AdaPayCorpMemberDto.class);
        Map<String, Object> adaPayCorpMemberMap = BeanUtil.beanToMap(adaPayCorpMemberDTO, true, true);
        adaPayCorpMemberMap.put("adapay_func_code", "corp_members.update");
        // 附件处理
        String attachFile = request.getAttachFile();
        File file = null;
        try (InputStream fileContent = s3plusStorageService.readExcelFromOnlineUrl(attachFile)) {
            StringBuilder str = new StringBuilder();
            str.append(ClassUtils.getDefaultClassLoader().getResource("").getPath());
            str.append(request.getName()).append("附件资料.zip");
            file = FileUtil.writeFromStream(fileContent, str.toString());
            Map<String, Object> returnMap = AdapayCommon.requestAdapayFile(adaPayCorpMemberMap, file);
            if (returnMap == null) {
                throw new BusinessException(PayResultCodeEnum.PAY_CORP_MEMBER_CREATE_ERROR.getCode(), PayResultCodeEnum.PAY_CORP_MEMBER_CREATE_ERROR.getMsg());
            }
            log.info("更新企业用户对象返回参数 returnMap={}", JSONUtil.toJsonStr(returnMap));
            if ((AdaPayEnums.PENDING.getStatus().equals(returnMap.get(AdaPayConstant.STATUS)))) {
                return false;
            }
            if (AdaPayEnums.SUCCEEDED.getStatus().equals(returnMap.get(AdaPayConstant.STATUS))){
                return true;
            }
            throw new BusinessException(PayResultCodeEnum.PAY_CORP_MEMBER_CREATE_ERROR.getCode(), returnMap.get(AdaPayConstant.ERROR_MSG).toString());
        } catch (IOException e) {
            log.error("更新企业用户对象账号IO异常", e);
            throw new BusinessException(PayResultCodeEnum.PAY_CORP_MEMBER_CREATE_ERROR.getCode(), e.getMessage());
        } catch (BaseAdaPayException e) {
            log.error("更新企业用户对象账号Ada异常 code={}, message={}", e.getCode(), e.getMessage(), e);
            throw new BusinessException(PayResultCodeEnum.PAY_CORP_MEMBER_CREATE_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("更新企业用户对象账号异常", e);
            throw new BusinessException(PayResultCodeEnum.PAY_CORP_MEMBER_CREATE_ERROR.getCode(), e.getMessage());
        } finally {
            if (null != file) {
                FileUtil.del(file);
            }
        }
    }

    @Override
    public Boolean hasSettleAccount(AdaPaySettleAccountQryReq request) {
        request.setAppId(AdaPayInitWithMerConfig.appId);
        log.info("查询结算账户对象 adaPaySettleAccountPO={}", JSONUtil.toJsonStr(request));
        Map<String, Object> adaPaySettleAccountQueryMap = BeanUtil.beanToMap(request, true, true);
        try {
            Map<String, Object> returnMap = SettleAccount.query(adaPaySettleAccountQueryMap);
            log.info("查询结算账户对象返回参数 returnMap={}", JSONUtil.toJsonStr(returnMap));
            if (returnMap == null) {
                throw new BusinessException(PayResultCodeEnum.PAY_SETTLE_ACCOUNT_QUERY_ERROR.getCode(), PayResultCodeEnum.PAY_SETTLE_ACCOUNT_QUERY_ERROR.getMsg());
            }
            if (AdaPayEnums.SUCCEEDED.getStatus().equals(returnMap.get(AdaPayConstant.STATUS))) {
                return Boolean.TRUE;
            }
            return Boolean.FALSE;
        } catch (BaseAdaPayException e) {
            log.error("查询结算账户对象异常 code={}, message={}", e.getCode(), e.getMessage(), e);
            throw new BusinessException(PayResultCodeEnum.PAY_SETTLE_ACCOUNT_QUERY_ERROR.getCode(), e.getMessage());
        }
    }

    @Override
    public AdaPayPaymentCreateResp createPayment(AdaPayPaymentCreateReq req) {
        AssertUtil.throwIfTrue("F".equals(createPaymentSwitch), "支付开关已关闭，无法发起付款");
        OrderPay orderPayCheck = orderPayRepository.getByOrderId(req.getOrderId());
        if (null != orderPayCheck) {
            throw new BusinessException(PayResultCodeEnum.ORDER_ID_EXIST.getCode(), PayResultCodeEnum.ORDER_ID_EXIST.getMsg());
        }

        // 保存订单支付信息
        String payId = AdaPayConstant.PAY_ID_PREFIX + leafService.generateNoBySnowFlake(AdaPayConstant.LEAF_ADAPAY_PAY_ID_KEY);
        OrderPay orderPay = new OrderPay();
        orderPay.setOrderId(req.getOrderId());
        orderPay.setPayId(payId);
        orderPay.setPayAmount(req.getPayAmount());
        orderPay.setPayState(PayStateEnums.UNPAID.getStatus());
        // 默认的支付渠道
        orderPay.setPaymentChannel(PaymentChannelEnum.ADAPAY.getCode());
        orderPay.setPaymentType(req.getPaymentType());
        orderPay.setBusinessType(req.getBusinessType());
        // 银行卡支付时，需要传入银行编码
        if (PaymentTypeEnum.COMPANY_BANK.getType().equals(req.getPaymentType())
                || PaymentTypeEnum.PERSON_BANK.getType().equals(req.getPaymentType())) {
            orderPay.setBankCode(req.getExpend().getAcctIssrId());
        }
        orderPayRepository.save(orderPay);

        AdaPaymentCreateDto adaPaymentCreateDto = AdaPaymentCreateDto.builder()
                .orderNo(payId)
                .appId(AdaPayInitWithMerConfig.appId)
                .payChannel(AdaPaymentTypeEnum.getEnumByType(req.getPaymentType()).getChannel())
                .payAmt(String.format("%.2f", req.getPayAmount()))
                .payMode(AdaPayConstant.PAY_MODE)
                .goodsTitle(EmojiUtil.removeAllEmojis(req.getGoodsTitle()))
                .goodsDesc(EmojiUtil.removeAllEmojis(req.getGoodsDesc()))
                .deviceInfo(AdaPayPaymentDeviceInfoDto.builder().deviceIp(req.getDeviceIp()).build())
                .expend(req.getExpend())
                .notifyUrl(adaPayCallbackUrl).build();
        log.info("创建支付对象请求参数 adaPaymentCreateDto={}", JsonUtil.toJsonString(adaPaymentCreateDto));
        Map<String, Object> adaPayPaymentCreateMap = BeanUtilMap.beanMap(adaPaymentCreateDto, true);
        log.info("创建支付对象请求参数 adaPayPaymentCreateMap={}", JsonUtil.toJsonString(adaPayPaymentCreateMap));
        try {
            Map<String, Object> returnMap = Payment.create(adaPayPaymentCreateMap);
            if (returnMap == null) {
                throw new BusinessException(PayResultCodeEnum.PAY_PAYMENT_CREATE_ERROR.getCode(), PayResultCodeEnum.PAY_PAYMENT_CREATE_ERROR.getMsg());
            }
            try {
                // 入参、返参插表记录
                ExchangeLogReq exchangeLogReq = new ExchangeLogReq();
                exchangeLogReq.setType(1);
                exchangeLogReq.setMethod("PayServiceImpl.createPayment_Payment.create");
                exchangeLogReq.setParam(JsonUtil.toJsonString(adaPayPaymentCreateMap));
                exchangeLogReq.setResult(JsonUtil.toJsonString(returnMap));
                exchangeLogService.insetExchangeLog(exchangeLogReq);
            } catch (Exception e){
                log.info("汇付交互表写数失败");
            }
            log.info("创建支付对象返回参数 returnMap={}", JsonUtil.toJsonString(returnMap));
            if (AdaPayEnums.SUCCEEDED.getStatus().equals(returnMap.get(AdaPayConstant.STATUS)) || AdaPayEnums.PENDING.getStatus().equals(returnMap.get(AdaPayConstant.STATUS))) {
                String channelPayId = returnMap.get(AdaPayConstant.PAY_CREATE_RESULT_KEY_ID).toString();
                Object expend = returnMap.get(AdaPayConstant.PAY_CREATE_RESULT_KEY_EXPEND);
                Map<String, String> expendMap = JsonUtil.beanToMap(expend);

                orderPay.setChannelPayId(channelPayId);
                orderPayRepository.updateById(orderPay);
                AdaPayPaymentCreateResp adaPayPaymentCreateResp = new AdaPayPaymentCreateResp();
                adaPayPaymentCreateResp.setExpend(expendMap);
                adaPayPaymentCreateResp.setChannelPayId(channelPayId);
                return adaPayPaymentCreateResp;
            }
            throw new BusinessException(PayResultCodeEnum.PAY_PAYMENT_CREATE_ERROR.getCode(), returnMap.get(AdaPayConstant.ERROR_MSG).toString());
        } catch (BaseAdaPayException e) {
            log.error("创建支付对象异常 code={}, message={}", e.getCode(), e.getMessage(), e);
            throw new BusinessException(PayResultCodeEnum.PAY_PAYMENT_CREATE_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 本退款方法只适用于未分账的退款
     *
     * @param req
     * @return
     */
    @Override
    public AdaPayReverseCreateResp createPaymentReverse(AdaPayReverseCreateReq req) {
        AssertUtil.throwIfTrue("F".equals(createPaymentReverseSwitch), "退款开关已关闭，无法发起退款");
        log.info("未分账前发起售后请求参数 req={}:", JsonUtil.toJsonString(req));
        OrderPay orderPay = orderPayRepository.getByOrderId(req.getOrderId());
        if (orderPay == null) {
            throw new BusinessException(PayResultCodeEnum.ORDER_PAY_NOT_EXIST.getCode(),
                    PayResultCodeEnum.ORDER_PAY_NOT_EXIST.getMsg());
        }
        ReverseOrder reverseOrderTemp = reverseOrderRepository.getByReverseId(req.getReverseId());
        if (reverseOrderTemp != null) {
            throw new BusinessException(PayResultCodeEnum.REVERSE_ID_EXIST.getCode(), PayResultCodeEnum.REVERSE_ID_EXIST.getMsg());
        }

        // 查询退款次数，单笔支付退款如果超过50次，不允许退款(查询退款中和退款成功的)
        Integer reverseCount = reverseOrderRepository.countByChannelPayIdAndStatus(orderPay.getChannelPayId(),
                Arrays.asList(ReverseStateEnums.REVERSE_ING.getStatus(), ReverseStateEnums.REVERSE_SUCCESS.getStatus()));
        if (reverseCount >= AdaPayConstant.MAX_REFUND_TIMES) {
            throw new BusinessException(PayResultCodeEnum.REVERSE_COUNT_OVER_LIMIT.getCode(), PayResultCodeEnum.REVERSE_COUNT_OVER_LIMIT.getMsg());
        }

        // 保存退款订单信息
        ReverseOrder reverseOrder = ReverseOrder.builder()
                .channelPayId(orderPay.getChannelPayId())
                .reverseAmount(req.getReverseAmount())
                .reverseId(req.getReverseId())
                .reverseState(ReverseStateEnums.REVERSE_ING.getStatus())
                .reverseType(ReverseTypeEnums.REVERSE_NO_SETTLEMENT.getType())
                .reverseTime(new Date())
                .businessType(orderPay.getBusinessType())
                .businessStatusType(req.getBusinessStatusType())
                .build();
        reverseOrderRepository.save(reverseOrder);

        AdaPayReverseCreateDto adaPayReverseCreateDto = AdaPayReverseCreateDto.builder()
                .paymentId(orderPay.getChannelPayId())
                .orderNo(req.getReverseId())
                .reverseAmt(String.format("%.2f", req.getReverseAmount()))
                .notifyUrl(adaPayCallbackUrl)
                .appId(AdaPayInitWithMerConfig.appId)
                .build();

        log.info("未分账前发起售后请求参数 adaPayReverseCreateDto={}:", JsonUtil.toJsonString(adaPayReverseCreateDto));
        Map<String, Object> adaPayPaymentReverseMap = BeanUtil.beanToMap(adaPayReverseCreateDto, true, true);
        try {
            Map<String, Object> returnMap = PaymentReverse.create(adaPayPaymentReverseMap);
            if (returnMap == null) {
                throw new BusinessException(PayResultCodeEnum.PAY_PAYMENT_REVERSE_CREATE_ERROR.getCode(), PayResultCodeEnum.PAY_PAYMENT_REVERSE_CREATE_ERROR.getMsg());
            }
            log.info("未分账前发起售后返回参数 returnMap={}:", JsonUtil.toJsonString(returnMap));
            if (AdaPayEnums.PENDING.getStatus().equals(returnMap.get(AdaPayConstant.STATUS)) || AdaPayEnums.SUCCEEDED.getStatus().equals(returnMap.get(AdaPayConstant.STATUS))) {
                AdaPayReverseCreateResultDto adaPayReverseCreateResultDto = BeanUtil.fillBeanWithMap(returnMap, new AdaPayReverseCreateResultDto(), true, true);
                reverseOrder.setChannelRefundId(adaPayReverseCreateResultDto.getId());
                reverseOrderRepository.updateById(reverseOrder);
                AdaPayReverseCreateResp adaPayPaymentReverseVO = new AdaPayReverseCreateResp();
                adaPayPaymentReverseVO.setChannelRefundId(adaPayReverseCreateResultDto.getId());
                return adaPayPaymentReverseVO;
            }
            throw new BusinessException(PayResultCodeEnum.PAY_PAYMENT_REVERSE_CREATE_ERROR.getCode(), returnMap.get(AdaPayConstant.ERROR_MSG).toString());
        } catch (BaseAdaPayException e) {
            log.error("未分账前发起售后异常[BaseAdaPayException] code={}, message={}", e.getCode(), e.getMessage(), e);
            reverseOrder.setReverseState(ReverseStateEnums.REVERSE_ERROR.getStatus());
            reverseOrder.setChannelRefundMsg(e.getMessage());
            reverseOrderRepository.updateById(reverseOrder);
            throw new BusinessException(PayResultCodeEnum.PAY_PAYMENT_REVERSE_CREATE_ERROR.getCode(), e.getMessage());
        } catch (BusinessException e) {
            log.error("未分账前发起售后异常[BusinessException] code={}, message={}", e.getCode(), e.getMessage(), e);
            reverseOrder.setReverseState(ReverseStateEnums.REVERSE_ERROR.getStatus());
            reverseOrder.setChannelRefundMsg(e.getMessage());
            reverseOrderRepository.updateById(reverseOrder);
            throw e;
        } catch (Exception e) {
            log.error("未分账前发起售后异常[Exception]", e);
            // 发送延迟消息处理（不确定调用汇付是否已经成功，这时返回了空对象回去，并且进行滞后查询处理）
            orderReverseExceptionProducer.sendMessage(
                    OrderReverseExceptionMsg.builder()
                            .channelPayId(orderPay.getChannelPayId())
                            .reverseId(req.getReverseId())
                            // 当前时间
                            .startTime(new Date()).build()
            );
            AdaPayReverseCreateResp adaPayPaymentReverseVO = new AdaPayReverseCreateResp();
            return adaPayPaymentReverseVO;
        }
    }

    @Override
    public AdaPaymentConfirmCreateResp createPaymentConfirm(AdaPaymentConfirmCreateReq req) {
        AssertUtil.throwIfTrue("F".equals(createPaymentConfirmSwitch), "分账开关已关闭，无法发起分账");
        OrderPay orderPay = orderPayRepository.getByOrderId(req.getSourceOrderId());
        if (null == orderPay) {
            throw new BusinessException(PayResultCodeEnum.ORDER_PAY_NOT_EXIST.getCode(),
                    PayResultCodeEnum.ORDER_PAY_NOT_EXIST.getMsg());
        }

        AdaPaymentConfirmCreateDto adaPaymentConfirmCreateDto = AdaPaymentConfirmCreateDto.builder()
                .paymentId(orderPay.getChannelPayId())
                .orderNo(req.getOrderNo())
                .confirmAmt(String.format("%.2f", req.getConfirmAmount()))
                .description(req.getDescription()).build();
        List<AdaPaymentConfirmDivMembersDto> divMemberDto = req.getDivMembers();
        if (CollUtil.isNotEmpty(divMemberDto)) {
            List<AdaPaymentConfirmCreateDivMembersDto> paymentConfirmCreateDivMembersDtoList = CollUtil.newArrayList();
            for (AdaPaymentConfirmDivMembersDto dto : divMemberDto) {
                AdaPaymentConfirmCreateDivMembersDto payDto = JsonUtil.copy(dto, AdaPaymentConfirmCreateDivMembersDto.class, "amount");
                payDto.setAmount(String.format("%.2f", dto.getAmount()));
                paymentConfirmCreateDivMembersDtoList.add(payDto);
            }
            adaPaymentConfirmCreateDto.setDivMembers(paymentConfirmCreateDivMembersDtoList);
        }

        log.info("创建支付确认对象请求参数 adaPaymentConfirmCreateDto={}:", JsonUtil.toJsonString(adaPaymentConfirmCreateDto));
        Map<String, Object> adaPaymentConfirmCreateMap = BeanUtilMap.beanMap(adaPaymentConfirmCreateDto, true);
        try {
            Map<String, Object> returnMap = PaymentConfirm.create(adaPaymentConfirmCreateMap);
            if (returnMap == null) {
                throw new BusinessException(PayResultCodeEnum.PAY_PAYMENT_CONFIRM_CREATE_ERROR.getCode(), PayResultCodeEnum.PAY_PAYMENT_CONFIRM_CREATE_ERROR.getMsg());
            }
            log.info("创建支付确认对象返回参数 returnMap={}:", JsonUtil.toJsonString(returnMap));
            if (AdaPayEnums.SUCCEEDED.getStatus().equals(returnMap.get(AdaPayConstant.STATUS))) {
                AdaPaymentConfirmCreateResultDto adaPaymentConfirmCreateResultDto = BeanUtil.fillBeanWithMap(returnMap, new AdaPaymentConfirmCreateResultDto(), true, true);
                orderPay.setChannelConfirmId(adaPaymentConfirmCreateResultDto.getId());
                orderPayRepository.updateById(orderPay);
                AdaPaymentConfirmCreateResp adaPaymentConfirmCreateResp = new AdaPaymentConfirmCreateResp();
                adaPaymentConfirmCreateResp.setId(adaPaymentConfirmCreateResultDto.getId());
                adaPaymentConfirmCreateResp.setFeeAmt(new BigDecimal(adaPaymentConfirmCreateResultDto.getFeeAmt()));
                return adaPaymentConfirmCreateResp;
            }
            // 修改创建支付确认对象异常
            Map<String, String> errorMap = new HashMap<>(2);
            errorMap.put(AdaPayConstant.ERROR_CODE, returnMap.get(AdaPayConstant.ERROR_CODE).toString());
            errorMap.put(AdaPayConstant.ERROR_MSG, returnMap.get(AdaPayConstant.ERROR_MSG).toString());
            throw new BusinessException(PayResultCodeEnum.PAY_PAYMENT_CONFIRM_CREATE_CHANNEL_ERROR.getCode(), JsonUtil.toJsonString(errorMap));
        } catch (BaseAdaPayException e) {
            log.error("创建支付确认对象异常 code={}, message={}", e.getCode(), e.getMessage(), e);
            throw new BusinessException(PayResultCodeEnum.PAY_PAYMENT_CONFIRM_CREATE_ERROR.getCode(), e.getMessage());
        }
    }

    @Override
    public BillDownloadResp billDownload(BillDownloadReq request) {
        Date billDate = request.getBillDate();
        String billDateStr = DateUtil.format(billDate, "yyyyMMdd");
        AdaPayBillDownloadDto adaPayBillDownloadDto = AdaPayBillDownloadDto.builder()
                .billDate(billDateStr)
                .build();
        log.info("对账单下载 adaPayBillDownloadDto={}:", JsonUtil.toJsonString(adaPayBillDownloadDto));
        Map<String, Object> adaPayBillDownloadMap = BeanUtilMap.beanMap(adaPayBillDownloadDto, true);
        try {
            Map<String, Object> returnMap = Bill.download(adaPayBillDownloadMap);
            if (returnMap == null) {
                throw new BusinessException(PayResultCodeEnum.BILL_DOWNLOAD_ERROR.getCode(), PayResultCodeEnum.BILL_DOWNLOAD_ERROR.getMsg());
            }
            log.info("对账单下载 returnMap={}:", JsonUtil.toJsonString(returnMap));
            if (AdaPayEnums.SUCCEEDED.getStatus().equals(returnMap.get(AdaPayConstant.STATUS))) {
                AdaPayBillDownloadResultDto adaPayBillDownloadResultDto = BeanUtil.fillBeanWithMap(returnMap, new AdaPayBillDownloadResultDto(), true, true);
                String billUrl = adaPayBillDownloadResultDto.getBillDownloadUrl();
                BillDownloadResp billDownloadResp = new BillDownloadResp();
                billDownloadResp.setBillUrl(billUrl);
                return billDownloadResp;
            }
            throw new BusinessException(PayResultCodeEnum.BILL_DOWNLOAD_ERROR.getCode(), returnMap.get(AdaPayConstant.ERROR_MSG).toString());
        } catch (BaseAdaPayException e) {
            log.error("对账单下载 code={}, message={}", e.getCode(), e.getMessage(), e);
            throw new BusinessException(PayResultCodeEnum.BILL_DOWNLOAD_ERROR.getCode(), e.getMessage());
        }
    }

    @Override
    public Map<String,OrderPayResp> queryCompletePay(List<String> batchNos) {
        Map<String,OrderPayResp> respMap = new HashMap<>();
        for(String batchNo : batchNos){
            OrderPay orderPay = orderPayRepository.queryListByNo(OrderPay.builder().orderId(batchNo).build());
            if (orderPay == null) {
                OrderPayResp resultDto = new OrderPayResp();
                resultDto.setOrderId(batchNo);
                resultDto.setPayState(PayStateEnums.PAY_FAILED.getStatus());
                respMap.put(batchNo, resultDto);
                continue;
            }
            // 如果支付成功了，说明回调了，组装返参
            if(PayStateEnums.PAID.getStatus() == orderPay.getPayState()){
                OrderPayResp resultDto = payOrderService.getOne(OrderPayQueryReq.builder().orderId(batchNo).queryChannel(true).build());
                respMap.put(batchNo, resultDto);
                continue;
            }
            List<OrderPay> unPaidList = Lists.newArrayList();
            unPaidList.add(orderPay);
            // 主动去拉一下汇付的数据,并且不发送mq
            this.opOrderPayStatus(unPaidList, false);
            // 组装结果返回
            OrderPayResp resultDto = payOrderService.getOne(OrderPayQueryReq.builder().orderId(batchNo).queryChannel(true).build());
            respMap.put(batchNo, resultDto);
        }
        return respMap;
    }

    /**
     *
     * @param unPaidList
     * @param flag 是否发送MQ
     */
    @Override
    public void opOrderPayStatus(List<OrderPay> unPaidList, boolean flag) {
        log.info("待支付查询列表 unPaidList={}", unPaidList);
        AdaPayPaymentListQueryDto adaPayPaymentListQueryDto = AdaPayPaymentListQueryDto.builder()
                .appId(AdaPayInitWithMerConfig.appId).build();

        for (OrderPay orderPay : unPaidList) {
            String lockKey = String.format(LockConstant.PAYMENT_CREATE_LOCK_KEY, orderPay.getOrderId());
            LockHelper.lock(lockKey, LockConstant.LOCK_TIME,
                    () -> {
                        adaPayPaymentListQueryDto.setPaymentId(orderPay.getChannelPayId());
                        Map<String, Object> adaPayReverseMap = BeanUtil.beanToMap(adaPayPaymentListQueryDto, true, true);
                        Map<String, Object> returnMap;
                        try {
                            returnMap = Payment.queryList(adaPayReverseMap);
                            log.info("待支付查询结果 returnMap={}:", returnMap);
                            if (returnMap != null) {
                                Object o = returnMap.get(AdaPayConstant.PAYMENTS);
                                List<AdaPayPaymentResultDto> resultDtoList = JSONUtil.toList(new JSONArray(o), AdaPayPaymentResultDto.class);
                                if (CollUtil.isNotEmpty(resultDtoList)) {
                                    AdaPayPaymentResultDto adaPayPaymentResultDto = resultDtoList.get(0);
                                    log.info("待支付查询结果 adaPayPaymentResultDto={}:", adaPayPaymentResultDto);

                                    AdaPaymentResultDto adaPaymentResultDto = new AdaPaymentResultDto();
                                    adaPaymentResultDto.setOrderId(orderPay.getOrderId());
                                    if (AdaPayEnums.PENDING.getStatus().equals(adaPayPaymentResultDto.getStatus())) {
                                        return;
                                    }
                                    BigDecimal bigDecimalValue = new BigDecimal(adaPayPaymentResultDto.getPayAmt());
                                    if(bigDecimalValue.compareTo(orderPay.getPayAmount()) != 0){
                                        // 说明金额被篡改了，这个订单不处理了
                                        return;
                                    }
                                    if (AdaPayEnums.SUCCEEDED.getStatus().equals(adaPayPaymentResultDto.getStatus())) {
                                        adaPaymentResultDto.setPayStatus(PayStateEnums.PAID.getStatus());
                                        String endTime = adaPayPaymentResultDto.getEndTime();
                                        if (StrUtil.isBlank(endTime)) {
                                            adaPaymentResultDto.setPayTime(new Date());
                                        } else {
                                            adaPaymentResultDto.setPayTime(DateUtil.parse(endTime, AdaPayConstant.TIME_FORMAT));
                                        }

                                    } else {
                                        adaPaymentResultDto.setPayStatus(PayStateEnums.PAY_FAILED.getStatus());
                                    }
                                    adaPaymentResultDto.setOutTransId(adaPayPaymentResultDto.getOutTransId());
                                    adaPaymentResultDto.setPayAmount(orderPay.getPayAmount());
                                    adaPaymentResultDto.setBankCode(orderPay.getBankCode());
                                    adaPaymentResultDto.setPayId(adaPayPaymentResultDto.getId());
                                    if (adaPayPaymentResultDto.getErrorMsg() != null) {
                                        adaPaymentResultDto.setErrorMsg(adaPayPaymentResultDto.getErrorMsg());
                                    }
                                    payOrderService.updateAndSendSyncPayment(adaPaymentResultDto, flag);
                                }
                            }
                        } catch (BaseAdaPayException e) {
                            log.error("支付结果查询异常", e);
                        }
                    });
        }
    }

    @Override
    public void deleteSettleAccount(AdaPaySettleAccountQryReq request) {
        request.setAppId(AdaPayInitWithMerConfig.appId);
//        先查询账号不存在直接返回
        if (!hasSettleAccount(request)){
            return;
        }
        log.info("删除结算账户对象 adaPaySettleAccountPO={}", JSONUtil.toJsonStr(request));
        Map<String, Object> adaPaySettleAccountQueryMap = BeanUtil.beanToMap(request, true, true);
        try {
            Map<String, Object> returnMap = SettleAccount.delete(adaPaySettleAccountQueryMap);
            log.info("删除结算账户对象返回参数 returnMap={}", JSONUtil.toJsonStr(returnMap));
            if (returnMap == null) {
                throw new BusinessException(PayResultCodeEnum.PAY_SETTLE_ACCOUNT_DELETE_ERROR.getCode(), PayResultCodeEnum.PAY_SETTLE_ACCOUNT_DELETE_ERROR.getMsg());
            }
            if (AdaPayEnums.SUCCEEDED.getStatus().equals(returnMap.get(AdaPayConstant.STATUS))) {
                return;
            }
            throw new BusinessException(PayResultCodeEnum.PAY_SETTLE_ACCOUNT_DELETE_ERROR.getCode(), returnMap.get(AdaPayConstant.ERROR_MSG).toString());
        } catch (BaseAdaPayException e) {
            log.error("删除结算账户对象异常 code={}, message={}", e.getCode(), e.getMessage(), e);
            throw new BusinessException(PayResultCodeEnum.PAY_SETTLE_ACCOUNT_DELETE_ERROR.getCode(), e.getMessage());
        }
    }

    @Override
    public Map<String, OrderPayResp> queryPayOrderPayMap(List<String> batchNos) {
        List<OrderPay> orderPays = orderPayRepository.queryPayOrderPayList(batchNos);
        if(CollectionUtil.isEmpty(orderPays)){
            return Collections.EMPTY_MAP;
        }
        List<OrderPayResp> orderPayResps = JsonUtil.copyList(orderPays, OrderPayResp.class);
        return orderPayResps.stream().collect(Collectors.toMap(OrderPayResp::getOrderId, Function.identity(), (v1, v2) -> v2));
    }
}
