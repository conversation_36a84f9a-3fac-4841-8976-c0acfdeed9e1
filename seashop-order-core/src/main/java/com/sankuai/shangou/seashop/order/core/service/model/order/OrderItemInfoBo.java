package com.sankuai.shangou.seashop.order.core.service.model.order;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class OrderItemInfoBo {

    private Long orderItemId;
    private String orderId;
    private Long productId;
    private String productName;
    private String mainImagePath;
    private String skuId;
    private String sku;
    /**
     * 拼接的规格信息
     */
    private String skuName;
    private Long quantity;
    private BigDecimal salePrice;
    private BigDecimal updatedAmount;
    private BigDecimal realTotalPrice;
    private List<String> attributeList;
    private BigDecimal discountAmount;
    private Long skuAutoId;

    /**
     * 是否显示【售后处理中】文案
     * 发货后的整单退，订单列表，明细显示文案
     */
    private Boolean showRefundingDesc;
    /**
     * 是否显示【退货退款】按钮
     * 前提：订单状态是待收货或者已完成；没有整单售后，明细没有发起售后，没有过售后维权期
     */
    private Boolean showRefundAndReturnBtn;
    /**
     * 当前明细是否有售后
     */
    private Boolean hasRefund;
    /**
     * 售后状态。
     */
    private Integer refundStatus;
    /**
     * 售后状态描述
     */
    private String refundStatusDesc;

    /**
     * sku颜色
     */
    private String color;
    /**
     * sku尺寸
     */
    private String size;
    /**
     * sku版本
     */
    private String version;
    /**
     * 售后ID
     */
    private Long refundId;
    /**
     * 是否允许7天无理由退货
     */
    private Boolean enableNoReasonReturn;
    /**
     * 是否显示保障(保证金)标志
     */
    private Boolean showGuaranteeFlag;
    /**
     * 是否显示闪电标识(是否立即发货)
     */
    private Boolean showThunderFlag;

    /**
     * 汇总的优惠金额(综合所有优惠的金额)
     */
    private BigDecimal realDiscountAmount;
    /**
     * 直接价格*数量的金额，realTotalPrice是计算了优惠的
     */
    private BigDecimal originTotalAmount;
    /**
     * 佣金
     */
    private BigDecimal commission;
    /**
     * 可退金额
     */
    private BigDecimal enabledRefundAmount;
    /**
     * 退款数量
     */
    private Long returnQuantity;

    /**
     * 商品类型（1:实物商品；2:虚拟商品）
     */
    private Integer productType;
    /**
     * 用户填写的预留信息
     */
    private String reservedInfo;
}
