package com.sankuai.shangou.seashop.order.core.service.model.order;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class QueryOrderCommonBo extends QueryOrderBaseBo {

    /**
     * 订单ID
     */
    private String orderId;
    /**
     * 商家手机号码
     */
    private String userPhone;
    /**
     * 收货人手机号
     */
    private String cellPhone;
    /**
     * 用户账号，来源EP，不可修改的
     */
    private String userName;
    /**
     * 订单来源（0:PC商城、1:牵牛花）
     */
    //private Integer orderSource;
    //private List<Integer> orderSourceList;
    /**
     * 付款方式（多选，支付宝扫码、微信小程序、企业网银、个人网银）
     */
    private List<Integer> paymentTypeList;
    private List<Integer> paymentList;
    /**
     * 订单类型列表（多选，0:正常购买; 1:组合购; 2:限时购; 3:虚拟商品订单; 4:实物自提订单;）
     */
    private List<Integer> orderTypeList;
    /**
     * 发票类型（全部、普通发票、电子普通发票、增值税发票）
     */
    private Integer invoiceType;
    /**
     * 是否有发票
     */
    private Boolean hasInvoice;
    /**
     * 是否评价
     */
    private Boolean hasComment;
    /**
     * 订单状态。-1:全部；1:待付款；2：待发货；3：待收货；4：已关闭；5：已完成；6：支付中
     */
    private Integer orderStatus;
    private List<Integer> orderStatusList;
    /**
     * 订单端口：来自哪个终端的订单。0：pc端，2：小程序
     */
    private List<Integer> platformList;

    private Long userId;
    /**
     * 店铺ID列表
     */
    private List<Long> shopIdList;
}
