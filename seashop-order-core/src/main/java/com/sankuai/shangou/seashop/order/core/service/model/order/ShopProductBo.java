package com.sankuai.shangou.seashop.order.core.service.model.order;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 购物车商品返回对象
 * <AUTHOR>
 */
@ToString
@Getter
@Setter
public class ShopProductBo {

    /**
     * 商品ID
     */
    private Long productId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 商品主图
     */
    private String mainImagePath;
    /**
     * 商品SKUID
     */
    private String skuId;
    /**
     * sku库存=sku.safeStock
     */
    private Long skuStock;
    /**
     * 原售价
     */
    private BigDecimal originSalePrice;
    /**
     * 优惠售价，基于专享价和阶梯价得到
     */
    private BigDecimal discountSalePrice;
    /**
     * 实际售价，不考虑任何营销活动，包括折扣的价格，目前只与专享价和阶梯价有关
     */
    private BigDecimal realSalePrice;
    /**
     * 最终售价，在实际售价的基础上，考虑折扣后的价格。如果没有折扣，则=realSalePrice，如果有折扣，则=discountSalePrice
     */
    private BigDecimal finalSalePrice;
    /**
     * 数量
     */
    private Long quantity;
    /**
     * 商品总金额
     */
    private BigDecimal totalAmount;
    /**
     * 规格：颜色，对应商品表的 spec1
     */
    private String color;
    /**
     * 规格：尺码，对应商品表的 spec2
     */
    private String size;
    /**
     * 规格：版本，对应商品表的 spec3
     */
    private String version;
    /**
     * sku自增主键ID
     */
    private Long skuAutoId;
    /**
     * 商品分类ID
     */
    private Long categoryId;
    private String sku;

    // 折扣活动ID，trade服务才会处理优惠，所以在这边处理分摊
    private Long discountActivityId;
    // 均摊后的满减金额
    private BigDecimal splitReductionAmount;
    // 均摊后的折扣金额
    private BigDecimal splitDiscountAmount;
    // 均摊后的优惠券金额
    private BigDecimal splitCouponAmount;
    // 如果该商品需要均摊优惠券，即商品在优惠券适用范围内，则会赋值，用于优惠券核销时判断
    private Long couponId;
    // 满减活动ID
    private Long reductionActivityId;

    /**
     * 商品类型（1:实物商品；2:虚拟商品）
     */
    private Integer productType;
    /**
     * 有效期类型（1:长期有效；2:天数内；3:时间段）
     */
    private Integer validityPeriodType;
    /**
     * 有效期天数
     */
    private Integer validityPeriodDays;
    /**
     * 有效期开始时间
     */
    private Date validityPeriodStartTime;
    /**
     * 有效期结束时间
     */
    private Date validityPeriodEndTime;
    /**
     * 是否支持多核销码
     */
    private Boolean whetherMultiVerificationCode;
    /**
     * 是否开启预留信息
     */
    private Boolean enableReservedInfo;
    /**
     * 预留信息表设置（json）
     */
    private String reservedInfoSetting;
    /**
     * 用户所填写的预留信息（json）
     */
    private String reservedInfo;
}
