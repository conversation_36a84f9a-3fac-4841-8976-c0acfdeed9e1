package com.sankuai.shangou.seashop.order.core.service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.order.core.mq.model.order.SellerRemarkBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.*;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderQueryFromEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.*;
import com.sankuai.shangou.seashop.order.thrift.core.response.*;
import com.sankuai.shangou.seashop.order.thrift.finance.request.ShopIdReq;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderService {

    /**
     * 创建订单
     * <p>订单服务的这个方法的前提是上游服务已经校验过数据的有效性了，订单服务只根据参数数据创建订单，不计算不校验</p>
     * 此方法下个版本会作废，替换为{@link com.sankuai.shangou.seashop.order.core.service.OrderCreatService#createOrder}
     *
     * @param createOrderBo 订单数据
     * <AUTHOR>
     */
    @Deprecated
    CreateOrderResp createOrder(CreateOrderBo createOrderBo);

    /**
     * 查询订单明细
     *
     * @param orderId 订单号
     * <AUTHOR>
     */
    OrderDetailBo getDetail(String orderId, OrderQueryFromEnum queryFrom);

    /**
     * 查询牵牛花订单明细 orderId sourceOrderId不能都为空
     *
     * @param orderId       订单id
     * @param sourceOrderId 牵牛花订单ID
     * @return 订单详情
     */
    ErpOrderDetailBo getErpOrderDetail(String orderId, String sourceOrderId);

    /**
     * 查询订单操作日志
     *
     * @param orderId 订单号
     */
    List<OrderOperationLogBo> getOrderLog(String orderId);

    /**
     * 取消支付
     *
     * @param userId  登录用户
     * @param orderId 订单号
     * <AUTHOR>
     */
    void cancelPay(Long userId, String orderId);

    /**
     * 取消订单
     *
     * @param userId  登录用户
     * @param orderId 订单号
     * <AUTHOR>
     */
    void cancelOrder(Long userId, String orderId);

    /**
     * 取消订单
     */
    void cancelOrder(CancelOrderBo cancelOrderBo);

    /**
     * 订单重新加购
     *
     * @param userId  登录用户
     * @param orderId 订单号
     * <AUTHOR>
     */
    void reBuy(Long userId, String orderId);

    /**
     * 商家修改自己的收货地址
     *
     * @param userUpdateReceiverBo 入参
     * <AUTHOR>
     */
    void updateReceiver(UserUpdateReceiverBo userUpdateReceiverBo);

    /**
     * 卖家修改商家的收货地址
     *
     * @param updateReceiverBo 入参
     * <AUTHOR>
     */
    void updateReceiverBySeller(SellerUpdateReceiverBo updateReceiverBo);

    /**
     * 商家改价
     *
     * @param updateItemAmountBo void
     * <AUTHOR>
     */
    void updateItemAmount(UpdateItemAmountBo updateItemAmountBo);

    /**
     * 商家修改运费
     *
     * @param updateFreightBo void
     * <AUTHOR>
     */
    void updateFreight(UpdateFreightBo updateFreightBo);

    /**
     * 买家延长收货
     *
     * @param delayReceiveBo 入参
     */
    void delayReceive(DelayReceiveBo delayReceiveBo);

    /**
     * 买家确认收货
     *
     * @param confirmReceiveBo 入参
     * <AUTHOR>
     */
    void confirmReceive(ConfirmReceiveBo confirmReceiveBo);

    /**
     * 确认收货 又返回订单
     *
     * @param confirmReceiveBo 入参
     * @return
     */
    ErpOrderDetailBo confirmErpOrderReceive(ConfirmReceiveBo confirmReceiveBo);

    /**
     * 获取订单收货地址信息
     *
     * @param orderIdList 订单号
     */
    OrderWayBillListBo getOrderWayBill(Long shopId, List<String> orderIdList);

    /**
     * 订单发货
     * <p>
     * 单个订单发货
     *
     * @param orderDeliveryBo 入参
     */
    void deliverOrder(DeliverOrderParamBo orderDeliveryBo);

    /**
     * 批量订单发货，单个发货复用
     *
     * @param orderDeliveryBo 入参
     */
    void batchDeliverOrder(BatchDeliverOrderParamBo orderDeliveryBo);

    /**
     * 修改运单号
     *
     * @param orderDeliveryBo 入参
     */
    void updateExpress(DeliverOrderParamBo orderDeliveryBo);

    /**
     * 新增运单信息
     *
     * @param orderDeliveryBo 入参
     */
    void addExpress(DeliverOrderParamBo orderDeliveryBo);

    /**
     * 订单统计查询
     *
     * @param request
     * @return
     */
    OrderStatisticsResp getOrderStatistics(OrderStatisticsReq request);

    OrderStatisticsResp getOrderStatisticsByMember(OrderStatisticsMemberReq request);

    /**
     * 订单统计查询
     *
     * @param request
     * @return
     */
    OrderStatisticsListResp getOrderStatisticsList(OrderStatisticsReq request);

    /**
     * 根据用户ID统计各订单状态的数量
     *
     * @param userId
     * @return
     */
//    EachStatusCountResp queryEachStatusCount(Long userId);

    /**
     * 根据用户ID查询最近一个订单信息
     *
     * @param userId
     * @return
     */
    OrderInfoDto queryLastOrderInfo(Long userId);

    /**
     * erp分页查询订单列表
     *
     * @param req 入参
     * @return 分页数据
     */
    BasePageResp<ErpOrderDetailBo> queryOrderPageForErp(QueryErpPageOrderReq req);

    /**
     * erp 批量查询订单
     *
     * @param req 入参
     * @return 订单列表
     */
    List<ErpOrderDetailBo> queryBatchOrderForErp(QueryBatchErpOrderReq req);

    /**
     * 供应商添加备注
     *
     * @param remarkBo 入参
     */
    void sellerRemark(SellerRemarkBo remarkBo);

    /**
     * 导出订单配货单
     *
     * @param queryReq
     * @return
     */
    List<OrderDistributionFormResp> exportOrderDistribution(QueryOrderDistributionReq queryReq);

    /**
     * 导出商品配货单
     *
     * @param queryReq
     * @return
     */
    List<OrderProductDistributionFormResp> exportOrderProductDistribution(QueryOrderDistributionReq queryReq);

    /**
     * 通过店铺ID统计已完成的订单数量和商品数量
     *
     * @param request
     * @return
     */
    CountFlashOrderAndProductResp countFlashOrderAndProduct(ShopIdReq request);

    /**
     * 取消订单
     */
    void sellerCancelOrder(CancelOrderBo cancelOrderBo);

    /**
     * 供应商将用户订单重新加入购物车
     */
    void reBuyBySeller(String orderId, Long shopId);

    /**
     * 获取订单的物流公司
     *
     * @param userId  用户ID
     * @param orderId 订单号
     */
    OrderWayBillBo getUserOrderWayBill(Long userId, String orderId);

    BasePageResp<OrderInfoDto> getSoldTrades(OrderGetSoldTradesReq param);

    BasePageResp<OrderInfoDto> getIncrementSoldTrades(OrderGetSoldTradesReq param);

    OrderInfoDto getTradeByOrderId(String orderId);

    /**
     * 创建指定数量的订单号
     *
     * @param size
     * @return
     */
    List<String> generateOrderNo(Integer size);

    /**
     * 确认收货
     * @param req
     */
    void confirmReceiptWx(ConfirmReceiptWxReq req);
}
