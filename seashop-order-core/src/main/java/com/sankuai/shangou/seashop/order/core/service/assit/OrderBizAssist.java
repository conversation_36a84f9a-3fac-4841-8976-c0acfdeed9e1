package com.sankuai.shangou.seashop.order.core.service.assit;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.DateUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.boot.utils.NumberUtil;
import com.sankuai.shangou.seashop.base.thrift.core.RegionQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.RegionIdsReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.AllPathRegionResp;
import com.sankuai.shangou.seashop.base.utils.SquirrelUtil;
import com.sankuai.shangou.seashop.order.common.constant.CacheConst;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.common.remote.SettingRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.base.TradeSiteSettingBo;
import com.sankuai.shangou.seashop.order.core.mq.model.order.OrderDataHolder;
import com.sankuai.shangou.seashop.order.core.service.assit.refund.RefundStatusHelper;
import com.sankuai.shangou.seashop.order.core.service.model.order.CreateOrderBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.CreateOrderUserBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ErpOrderAddressBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ErpOrderDetailBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.InvoiceBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderAdditionalBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderDetailBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderExpressBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderInfoBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderInvoiceBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderItemInfoBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderShopBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ProductPromotionBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.PromotionBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ShippingAddressBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ShopAndProductPromotionBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ShopProductBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ShopProductListBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderInvoice;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPromotionSnapshot;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefund;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderWayBill;
import com.sankuai.shangou.seashop.order.dao.core.po.UpdateReceiverPo;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderInvoiceRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderItemRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderOperationLogRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderPromotionSnapshotRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRefundRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderWayBillRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.*;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayMethodEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PaymentTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundAuditStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundModeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 订单辅助类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderBizAssist {

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private OrderInvoiceRepository orderInvoiceRepository;
    @Resource
    private OrderWayBillRepository orderWayBillRepository;
    @Resource
    private OrderPromotionSnapshotRepository orderPromotionSnapshotRepository;
    @Resource
    private OrderOperationLogRepository orderOperationLogRepository;
    @Resource
    private SquirrelUtil squirrelUtil;
    @Resource
    private ShopRemoteService shopRemoteService;
    @Resource
    private SettingRemoteService settingRemoteService;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private RegionQueryFeign regionQueryFeign;
    @Value("${wx.send: false}")
    private Boolean wxSend;

    /**
     * 校验订单创建的接口幂等性
     * <p>通过redis的setNx校验，如果已经设置了说明接口已经请求了</p>
     *
     * @param uniqueId void
     * <AUTHOR>
     */
    public void validateCreateOrderIdempotence(String uniqueId) {
        String key = CacheConst.KEY_ORDER_CREATE_IDEMPOTENCE + uniqueId;
        Boolean success = squirrelUtil.setnx(key, key, CacheConst.EXPIRE_ORDER_CREATE_IDEMPOTENCE);
        if (!Boolean.TRUE.equals(success)) {
            throw new BusinessException("订单正在创建中，请勿重复提交");
        }
    }


    /**
     * 根据订单号，待创建的订单数据，构建订单主表和订单明细表的数据
     *
     * @param orderIdList   订单ID
     * @param createOrderBo 需要保存的订单数据
     * <AUTHOR>
     */
    public OrderDataHolder buildOrderData(List<String> orderIdList, CreateOrderBo createOrderBo) {
        // 店铺商品列表，每个店铺一个订单
        List<ShopProductListBo> shopProductList = createOrderBo.getShopProductList();
        ShippingAddressBo shippingAddress = createOrderBo.getShippingAddress();
        // 获取一些计算用到的配置，比如佣金比例，结算手续费比例等
        // 将订单和订单明细全部构建出来，组装成 list，后面批量保存
        int size = shopProductList.size();
        List<Order> orderList = new ArrayList<>(size);
        List<OrderItem> orderItemList = new ArrayList<>(size * 10);
        List<OrderInvoice> invoiceList = new ArrayList<>(size);
        // 创建时间用交易服务提交订单入口时间，前后统一
        if (createOrderBo.getCreateTime() == null) {
            createOrderBo.setCreateTime(new Date());
        }
        Date now = createOrderBo.getCreateTime();
        String mainOrderId = orderIdList.get(0);
        // 获取店铺的类目佣金比例
        List<Long> shopIdList = shopProductList.stream()
                .map(ShopProductListBo::getShop)
                .map(OrderShopBo::getShopId)
                .distinct()
                .collect(Collectors.toList());
        List<BusinessCategoryResp> cateList = shopRemoteService.getCategoryByShopIdList(shopIdList);
        if (CollUtil.isEmpty(cateList)) {
            cateList = new ArrayList<>(0);
        }
        Map<String, BigDecimal> cateCommissionRate = cateList.stream()
                .collect(Collectors.toMap(cate -> cate.getShopId() + "-" + cate.getCategoryId(),
                        BusinessCategoryResp::getCommissionRate, (v1, v2) -> v1));
        List<String> needPayOrderIdList = new ArrayList<>(size);
        for (int i = 0; i < shopProductList.size(); i++) {
            String orderId = orderIdList.get(i);
            ShopProductListBo shopOrder = shopProductList.get(i);
            // 需要先设置，后续会用到
            shopOrder.setOrderId(orderId);
            // 构建订单明细信息，先构建明细，是为了先设置分佣比例，订单要设置总的
            List<OrderItem> orderItems = buildOrderItems(orderId, createOrderBo, shopOrder.getProductList(), shopOrder.getShop(), now, cateCommissionRate);
            // 构建订单信息
            Order order = buildOrder(shopOrder, shippingAddress, createOrderBo, orderItems, now);
            // 同一批次提交的订单，取一个作为批次标识
            order.setMainOrderId(mainOrderId);
            orderList.add(order);

            // 发票信息
            OrderInvoice invoice = buildInvoice(shopOrder);
            if (invoice != null) {
                invoice.setOrderId(orderId);
                invoice.setCreateTime(now);
                invoice.setUpdateTime(now);
                invoiceList.add(invoice);
            }

            if (BigDecimal.ZERO.compareTo(order.getTotalAmount()) != 0) {
                needPayOrderIdList.add(orderId);
            }

            orderItemList.addAll(orderItems);
        }

        return OrderDataHolder.builder()
                .orderList(orderList)
                .orderItemList(orderItemList)
                .flashSaleId(createOrderBo.getFlashSaleId())
                .orderIdList(orderIdList)
                .invoiceList(invoiceList)
                .needPayOrderIdList(needPayOrderIdList)
                .build();
    }


    /**
     * 保存订单和相关信息
     * <p>包括：订单主表、订单明细、删除购物车、营销快照</p>
     *
     * @param dataHolder    订单数据
     * @param createOrderBo 需要保存的订单数据
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrderAndRelated(OrderDataHolder dataHolder, CreateOrderBo createOrderBo) {
        log.debug("【订单保存】保存订单，订单数据为: {}", JsonUtil.toJsonString(dataHolder));
        // 保存订单主表和订单明细
        orderRepository.saveBatch(dataHolder.getOrderList());
        orderItemRepository.saveBatch(dataHolder.getOrderItemList());
        // 保存营销快照
        savePromotionSnapshot(createOrderBo);
        // 保存订单发票
        if (CollectionUtils.isNotEmpty(dataHolder.getInvoiceList())) {
            orderInvoiceRepository.saveBatch(dataHolder.getInvoiceList());
        }
    }

    public void validateOrder(Long userId, Order dbOrder) {
        if (dbOrder == null) {
            throw new BusinessException("订单不存在");
        }
        if (!dbOrder.getUserId().equals(userId)) {
            throw new BusinessException("只能操作自己的订单");
        }
    }

    public OrderDetailBo buildOrderDetail(String orderId, OrderQueryFromEnum queryFrom) {
        OrderDetailBo detail = new OrderDetailBo();
        detail.setWxSend(wxSend);
        // 设置订单信息
        appendCommonOrderInfo(orderId, detail, queryFrom);
        // 设置订单明细
        appendOrderItem(orderId, detail);
        // 设置订单售后状态
        appendOrderAndItemRefund(detail);
        // 设置发票信息
        appendOrderInvoice(orderId, detail);

        // 处理订单状态修改的时间
        appendStatusChangeTime(detail);
        detail.getOrderInfo().setWxSend(wxSend);
        return detail;
    }

    public ErpOrderDetailBo buildOrderDetailByOrderIdOrOrderSourceId(String orderId, String orderSourceId) {
        // 设置订单信息 包括收货人地址
        ErpOrderDetailBo detail = buildErpOrderInfo(orderId, orderSourceId);
        orderId = detail.getOrderId();
        // 设置订单明细
        detail.setItemList(buildOrderItems(orderId));
        // 设置物流信息
        detail.setExpressList(buildOrderExpress(orderId));
        //发票信息
        detail.setOrderInvoice(buildOrderInvoice(orderId));

        return detail;
    }

    /**
     * 添加操作日志
     *
     * <AUTHOR>
     */
    public void addOrderOperationLog(String orderId, String userName, String operationContent) {
        orderOperationLogRepository.save(orderId, userName, operationContent);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveUpdateReceiverData(String orderId, UpdateReceiverPo updateReceiverPo) {
        // 自定义sql只改部分字段，防止可能的并发导致其他字段被覆盖
        int cnt = orderRepository.updateOrderShipInfo(updateReceiverPo);
        if (cnt != 1) {
            throw new BusinessException("修改收货人信息失败");
        }
        // 保存操作日志
        this.addOrderOperationLog(orderId, updateReceiverPo.getUserName(), "供应商修改订单的收货信息");
    }


    //**************************************************************************


    private Order buildOrder(ShopProductListBo shopOrder, ShippingAddressBo shippingAddress, CreateOrderBo createOrderBo,
                             List<OrderItem> orderItems, Date now) {
        CreateOrderUserBo userInfo = createOrderBo.getUserInfo();
        // 订单附加信息，主要是每个订单用户可以自定义的信息
        // get方法判断了是否为空
        OrderAdditionalBo additional = shopOrder.getAdditional();

        OrderShopBo shop = shopOrder.getShop();
        Order order = new Order();
        order.setId(Long.parseLong(shopOrder.getOrderId()));
        order.setOrderId(shopOrder.getOrderId());
        order.setOrderDate(now);
        order.setShopId(shop.getShopId());
        order.setShopName(shop.getShopName());
        order.setUserId(userInfo.getUserId());
        order.setUserName(userInfo.getUserName());
        order.setUserRemark(additional.getRemark());
        order.setOrderRemarks(additional.getRemark());
        order.setSourceOrderId(additional.getSourceOrderId());
        // 如果没有收货地址，比如是外部同步的订单，就用用户信息里的
        if (shippingAddress != null) {
            order.setShipTo(shippingAddress.getShipTo());
            order.setCellPhone(shippingAddress.getPhone());
            // 解析顶级区域ID
            Integer topRegionId = shippingAddress.getProvinceId().intValue();
            order.setTopRegionId(topRegionId);
            order.setRegionId(shippingAddress.getRegionId());
            order.setRegionFullName(shippingAddress.getRegionFullName());
            order.setAddress(shippingAddress.getAddress() + " " + shippingAddress.getAddressDetail());
        }
        else {
            order.setShipTo(userInfo.getUserName());
            order.setCellPhone(userInfo.getUserPhone());
            order.setRegionFullName("");
            order.setAddress("");
            order.setTopRegionId(0);
            order.setRegionId(0);
        }
        if (additional.getCouponId() != null && additional.getCouponId() > 0) {
            order.setCouponId(additional.getCouponId());
            // 目前系统只有优惠券，所以这里写死
            order.setCouponType(OrderCouponTypeEnum.COUPON.getCode());
        }
        else {
            order.setCouponId(0L);
        }
        order.setActiveType(OrderActiveTypeEnum.NONE.getCode());
        order.setPaymentType(PaymentTypeEnum.ONLINE.getCode());
        if (additional.getDeliveryType() == null) {
            throw new InvalidParamException("配送方式不能为空");
        }
        // 当前系统只有快递配送
        order.setDeliveryType(createOrderBo.getDeliveryType() != null ? createOrderBo.getDeliveryType() : OrderDeliveryTypeEnum.EXPRESS.getCode());

        // 订单类型
        order.setOrderType(OrderTypeEnum.NORMAL.getCode());
        if (createOrderBo.getCollocationId() != null) {
            order.setOrderType(OrderTypeEnum.COLLOCATION.getCode());
        }
        else if (createOrderBo.getFlashSaleId() != null) {
            order.setOrderType(OrderTypeEnum.FLASH_SALE.getCode());
        }

        // 每个订单的总金额是商品相关的总金额+运费+税费
        // 商品相关的总金额是综合折扣、满减、优惠券计算过的
        BigDecimal selectedAmount = NumberUtil.nullToZero(shop.getSelectedTotalAmount());
        BigDecimal freightAmount = NumberUtil.nullToZero(additional.getFreightAmount());
        BigDecimal taxAmount = NumberUtil.nullToZero(additional.getTaxAmount());
        BigDecimal totalAmount = selectedAmount.add(freightAmount).add(taxAmount).setScale(2, RoundingMode.HALF_UP);
        order.setTotalAmount(totalAmount);
        order.setFreight(freightAmount);
        order.setTax(taxAmount);
        // 纯商品总金额
        order.setProductTotalAmount(NumberUtil.nullToZero(shop.getProductTotalAmount()));
        // 0元订单，直接待发货，默认支付时间为订单时间
        if (order.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
            // 正常订单
            order.setOrderStatus(OrderStatusEnum.UNDER_PAY.getCode());
        } else {
            // 0元订单
            order.setOrderStatus(OrderStatusEnum.UNDER_SEND.getCode());
            order.setPayDate(now);
        }
        // 实收金额初始设置为实付金额
        order.setActualPayAmount(totalAmount);
        order.setDiscountAmount(NumberUtil.nullToZero(additional.getDiscountAmount()));
        order.setCouponAmount(NumberUtil.nullToZero(additional.getCouponAmount()));
        order.setMoneyOffAmount(NumberUtil.nullToZero(additional.getReductionAmount()));
        order.setMoneyOffCondition(NumberUtil.nullToZero(additional.getReductionConditionAmount()));
        // 以下初始设置为0
        order.setRefundTotalAmount(BigDecimal.ZERO);


        order.setRefundCommissionAmount(BigDecimal.ZERO);
        order.setLastModifyTime(now);
        // 外部传入，底层创建订单通用
        if (createOrderBo.getPlatform() != null) {
            order.setPlatform(createOrderBo.getPlatform().getCode());
        }
        //if (createOrderBo.getOrderSource() != null) {
        //    order.setOrderSource(createOrderBo.getOrderSource().getCode());
        //}
        // 从配置获取结算手续费比例，需要区分微信支付，所以这里固定设置0，每次发起支付时根据支付方式重置这个手续费比例
        order.setSettlementCharge(BigDecimal.ZERO);
        // 佣金=订单中每个商品的实付金额*对应商品类目所设置的佣金比例 之和
        BigDecimal commissionAmount = orderItems.stream()
                .filter(item -> item.getCommisRate() != null)
                .map(item -> {
                    BigDecimal realTotalPrice = item.getRealTotalPrice();
                    BigDecimal commisRate = item.getCommisRate();
                    return NumberUtil.multiply(realTotalPrice, commisRate);
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        order.setCommissionTotalAmount(commissionAmount);
        order.setCreateTime(now);

        order.setPrinted(false);
        return order;
    }

    private List<OrderItem> buildOrderItems(String orderId, CreateOrderBo createOrderBo,
                                            List<ShopProductBo> productList, OrderShopBo shop, Date now, Map<String, BigDecimal> cateCommissionRate) {
        boolean isFlashSale = createOrderBo.getFlashSaleId() != null;
        Long flashSaleId = createOrderBo.getFlashSaleId();
        return productList.stream()
                .map(prod -> {
                    OrderItem item = new OrderItem();
                    item.setOrderId(orderId);
                    item.setShopId(shop.getShopId());
                    item.setProductId(prod.getProductId());
                    item.setSkuId(prod.getSkuId());
                    item.setQuantity(prod.getQuantity());
                    item.setCostPrice(NumberUtil.nullToZero(prod.getOriginSalePrice()));
                    // 非营销活动下的实际售价，即 优先级 ：限时购价格/组合购价格/专享价>阶梯价>原价
                    item.setSalePrice(prod.getRealSalePrice());
                    item.setSku(prod.getSku());

                    BigDecimal commissionRate = cateCommissionRate.get(shop.getShopId() + "-" + prod.getCategoryId());
                    item.setCommisRate(cn.hutool.core.util.NumberUtil.div(NumberUtil.nullToZero(commissionRate), CommonConst.HUNDRED, 5));
                    // 改价金额，默认不赋值
                    item.setDiscountAmount(BigDecimal.ZERO);
                    // 优惠券抵扣金额-均摊后
                    item.setCouponDiscount(NumberUtil.nullToZero(prod.getSplitCouponAmount()));
                    // 折扣抵扣金额-均摊后
                    item.setFullDiscount(NumberUtil.nullToZero(prod.getSplitDiscountAmount()));
                    // 满减抵扣金额-均摊后
                    item.setMoneyOff(NumberUtil.nullToZero(prod.getSplitReductionAmount()));

                    // item.getSalePrice() 前面已经赋值
                    // realTotalPrice 指的是商品的实付金额，所以 realTotalPrice = 价格*数量-优惠券分摊-满减均摊-折扣均摊
                    BigDecimal realTotalPrice = NumberUtil.multiply(item.getSalePrice(), item.getQuantity())
                            .subtract(item.getCouponDiscount())
                            .subtract(item.getFullDiscount())
                            .subtract(item.getMoneyOff());
                    log.info("【订单创建】计算商品实付金额=单价*数量-优惠券均摊-满减均摊-折扣抵扣, realTotalPrice({})=salePrice({}) * quantity=({}) - couponAmount=({}) - discountAmount=({}) - moneyOffAmount=({})",
                            realTotalPrice, item.getSalePrice(), item.getQuantity(), item.getCouponDiscount(), item.getFullDiscount(), item.getMoneyOff());
                    if (realTotalPrice.compareTo(BigDecimal.ZERO) < 0) {
                        realTotalPrice = BigDecimal.ZERO;
                    }
                    item.setRealTotalPrice(realTotalPrice);
                    // 初始的可退金额为商品金额
                    item.setEnabledRefundAmount(realTotalPrice);
                    item.setProductName(prod.getProductName());
                    item.setColor(prod.getColor());
                    item.setSize(prod.getSize());
                    item.setVersion(prod.getVersion());
                    item.setThumbnailsUrl(prod.getMainImagePath());
                    item.setLimitBuy(isFlashSale);

                    item.setFlashSaleId(flashSaleId);
                    item.setSkuAutoId(prod.getSkuAutoId());

                    // 以下初始为0
                    item.setReturnQuantity(0L);
                    item.setRefundPrice(BigDecimal.ZERO);

                    item.setCreateTime(now);
                    item.setUpdateTime(now);

                    return item;
                })
                .collect(Collectors.toList());
    }

    private void appendCommonOrderInfo(String orderId, OrderDetailBo detail, OrderQueryFromEnum queryFrom) {
        Order dbOrder = orderRepository.getByOrderId(orderId);
        if (dbOrder == null) {
            throw new BusinessException("订单不存在");
        }
        OrderInfoBo orderInfo = JsonUtil.copy(dbOrder, OrderInfoBo.class);
        orderInfo.setOrderStatusDesc(OrderStatusEnum.getDesc(orderInfo.getOrderStatus()));
        orderInfo.setPaymentTypeDesc(PaymentTypeEnum.getDesc(orderInfo.getPaymentType()));
        orderInfo.setPayment(dbOrder.getPayment());
        orderInfo.setPaymentDesc(PayMethodEnum.getDesc(dbOrder.getPayment()));

        // 运费修改情况
        appendFreightUpdated(orderInfo);

        TradeSiteSettingBo setting = settingRemoteService.getTradeSiteSetting();
        Date now = new Date();
        // 按需对收货人信息加解密
        appendEncryptShipInfoIfNecessary(dbOrder, orderInfo, setting, now, queryFrom);
        // 待付款、支付中设置剩余支付时间
        appendRemainPayTimeIfNecessary(dbOrder, orderInfo, setting, now);
        // 待收货状态下，显示预计完成时间
        appendEstimateCompleteTime(dbOrder, orderInfo, setting, now);

        // 设置物流信息
        appendOrderExpress(orderId, detail,dbOrder);
        detail.setOrderInfo(orderInfo);
    }

    private void appendItemSummary(OrderInfoBo orderInfo, List<OrderItemInfoBo> orderItemList) {
        long productQuantity = 0L;
        BigDecimal totalUpdatedAmount = BigDecimal.ZERO;
        for (OrderItemInfoBo item : orderItemList) {
            productQuantity = productQuantity + item.getQuantity();
            totalUpdatedAmount = totalUpdatedAmount.add(item.getDiscountAmount());
        }
        orderInfo.setTotalUpdatedAmount(totalUpdatedAmount);
        orderInfo.setProductQuantity(productQuantity);
    }

    private void appendFreightUpdated(OrderInfoBo orderInfo) {
        BigDecimal backupFreight = orderInfo.getBackupFreight();
        BigDecimal freight = NumberUtil.nullToZero(orderInfo.getFreight());
        if (backupFreight != null) {
            String changeDesc = freight.compareTo(backupFreight) > 0 ? CommonConst.DESC_INCREASE : CommonConst.DESC_DECREASE;
            String desc = String.format(CommonConst.FORMAT_PATTERN_ORDER_FREIGHT_UPDATED_DESC,
                    backupFreight.stripTrailingZeros(), changeDesc, freight.subtract(backupFreight).toPlainString());
            orderInfo.setFreightUpdateDesc(desc);
        }
    }

    public void appendEncryptShipInfoIfNecessary(Order dbOrder, OrderInfoBo orderInfo, TradeSiteSettingBo setting, Date now, OrderQueryFromEnum queryFrom) {
        // 设置是否已过售后维权期，默认值然后重置
        // 查询交易设置，获取售后维权期
        int refundCloseDays = getRefundCloseConfigDays(setting);

        Date lastFinishDate = cn.hutool.core.date.DateUtil.offsetDay(now, -refundCloseDays);
        boolean hasOverAfterSales = false;
        if (dbOrder.getFinishDate() != null && refundCloseDays != 0) {
            hasOverAfterSales = cn.hutool.core.date.DateUtil.compare(dbOrder.getFinishDate(), lastFinishDate) < 0;
        }
        orderInfo.setHasOverAfterSales(hasOverAfterSales);
        // 平台端查询，不管状态，都要加密
        if (OrderQueryFromEnum.PLATFORM_PC.equals(queryFrom)) {
            orderInfo.setShipTo(DesensitizedUtil.chineseName(orderInfo.getShipTo()));
            orderInfo.setCellPhone(DesensitizedUtil.mobilePhone(orderInfo.getCellPhone()));
            orderInfo.setUserPhone(DesensitizedUtil.mobilePhone(orderInfo.getUserPhone()));
        } else {
            // 已关闭或者已完成且已过售后维权期，收货人姓名和手机号码脱敏处理，商家、供应商、平台都是一样的处理
            boolean closed = OrderStatusEnum.CLOSED.getCode().equals(dbOrder.getOrderStatus());
            boolean finishAndOverAfterSales = OrderStatusEnum.FINISHED.getCode().equals(dbOrder.getOrderStatus()) && hasOverAfterSales;
            if (closed || finishAndOverAfterSales) {
                orderInfo.setShipTo(DesensitizedUtil.chineseName(orderInfo.getShipTo()));
                orderInfo.setCellPhone(DesensitizedUtil.mobilePhone(orderInfo.getCellPhone()));
                orderInfo.setUserPhone(DesensitizedUtil.mobilePhone(orderInfo.getUserPhone()));
            }
        }
    }

    public void appendRemainPayTimeIfNecessary(Order dbOrder, OrderInfoBo orderInfo, TradeSiteSettingBo setting, Date now) {
        // 待付款、支付中设置剩余支付时间
        if (OrderStatusEnum.UNDER_PAY.getCode().equals(dbOrder.getOrderStatus()) ||
                OrderStatusEnum.PAYING.getCode().equals(dbOrder.getOrderStatus())) {
            // 交易设置的未支付超时时间
            long unpaidTimeoutMills = getUnpaidTimeoutMills(setting);
            long remainPayTime = dbOrder.getCreateTime().getTime() + unpaidTimeoutMills - now.getTime();
            if (remainPayTime > 0) {
                orderInfo.setRemainPayTime(remainPayTime);
                orderInfo.setRemainPayTimeDesc(cn.hutool.core.date.DateUtil.formatBetween(remainPayTime, BetweenFormatter.Level.SECOND));
            }
        }
    }

    private void appendEstimateCompleteTime(Order dbOrder, OrderInfoBo orderInfo, TradeSiteSettingBo setting, Date now) {
        // 待收货状态下，显示预计完成时间
        if (OrderStatusEnum.UNDER_RECEIVE.getCode().equals(dbOrder.getOrderStatus()) && dbOrder.getShippingDate() != null) {
            int autoReceiveDays = getAutoReceiveDays(setting);
            int delayDays = cn.hutool.core.util.NumberUtil.nullToZero(dbOrder.getReceiveDelay());
            autoReceiveDays = autoReceiveDays + delayDays;
            Date finishDate = cn.hutool.core.date.DateUtil.offsetDay(dbOrder.getShippingDate(), autoReceiveDays);
            if (cn.hutool.core.date.DateUtil.compare(finishDate, now) > 0) {
                orderInfo.setEstimateCompleteTime(DateUtil.format(finishDate, CommonConst.TIME_PATTERN_ORDER_ESTIMATED_FINISH));
            }
        }
    }

    private ErpOrderDetailBo buildErpOrderInfo(String orderId, String sourceOrderId) {
        Order dbOrder = null;
        if (StringUtils.isNotBlank(orderId)) {
            dbOrder = orderRepository.getByOrderId(orderId);
        }
        else if (StringUtils.isNotBlank(sourceOrderId)) {
            List<Order> dbOrders = orderRepository.getBySourceOrderId(sourceOrderId);
            dbOrder = (CollectionUtils.isEmpty(dbOrders) ? null : dbOrders.get(0));
        }
        if (dbOrder == null) {
            throw new BusinessException("订单不存在");
        }
        return convertErpOrder(dbOrder);
    }

    public ErpOrderDetailBo convertErpOrder(Order dbOrder) {
        Integer orderStatus = dbOrder.getOrderStatus();
        dbOrder.setOrderStatus(null);
        ErpOrderDetailBo orderInfo = JsonUtil.copy(dbOrder, ErpOrderDetailBo.class);
        orderInfo.setTaxAmount(dbOrder.getTax());
        orderInfo.setFreightAmount(dbOrder.getFreight());
        orderInfo.setOrderStatus(OrderStatusEnum.getByCode(orderStatus));
        if (StringUtils.isNotBlank(dbOrder.getShipTo())) {
            ErpOrderAddressBo addressBo = new ErpOrderAddressBo();
            addressBo.setShipTo(dbOrder.getShipTo());
            addressBo.setCellPhone(dbOrder.getCellPhone());
            addressBo.setRegionId(dbOrder.getRegionId());
            addressBo.setTopRegionId(dbOrder.getTopRegionId());
            addressBo.setAddress(dbOrder.getAddress());
            addressBo.setRegionFullName(dbOrder.getRegionFullName());
            addressBo.setReceiveLatitude(dbOrder.getReceiveLatitude());
            addressBo.setReceiveLongitude(dbOrder.getReceiveLongitude());
            orderInfo.setOrderAddress(addressBo);
        }
        BigDecimal backupFreight = orderInfo.getBackupFreight();
        BigDecimal freight = NumberUtil.nullToZero(orderInfo.getFreightAmount());
        if (backupFreight != null) {
            String changeDesc = freight.compareTo(backupFreight) > 0 ? CommonConst.DESC_INCREASE : CommonConst.DESC_DECREASE;
            String desc = String.format(CommonConst.FORMAT_PATTERN_ORDER_FREIGHT_UPDATED_DESC,
                    backupFreight.stripTrailingZeros(), changeDesc, freight.subtract(backupFreight).toPlainString());
            orderInfo.setFreightUpdateDesc(desc);
        }
        return orderInfo;
    }

    private List<OrderItemInfoBo> buildOrderItems(String orderId) {
        List<OrderItem> dbItemList = orderItemRepository.getByOrderIdList(Collections.singletonList(orderId));
        return Optional.ofNullable(dbItemList)
                .orElse(Collections.emptyList())
                .stream().map(orderItem -> {
                    OrderItemInfoBo bo = JsonUtil.copy(orderItem, OrderItemInfoBo.class);
                    bo.setOrderItemId(orderItem.getId());
                    bo.setMainImagePath(orderItem.getThumbnailsUrl());
                    BigDecimal updatedPrice = orderItem.getDiscountAmount();
                    if (updatedPrice != null) {
                        bo.setUpdatedAmount(updatedPrice);
                    }
                    bo.setOriginTotalAmount(NumberUtil.multiply(orderItem.getSalePrice(), orderItem.getQuantity()));
                    bo.setCommission(NumberUtil.multiply(orderItem.getCommisRate(), orderItem.getRealTotalPrice()));
                    //color;size;version 非空拼接
                    String skuName = Stream.of(orderItem.getColor(), orderItem.getSize(), orderItem.getVersion())
                            .filter(StringUtils::isNotBlank).collect(Collectors.joining(StringPool.SEMICOLON));
                    bo.setSkuName(skuName);
                    return bo;
                }).collect(Collectors.toList());
    }

    private void appendOrderItem(String orderId, OrderDetailBo detail) {
        List<OrderItemInfoBo> orderItemList = buildOrderItems(orderId);

        OrderInfoBo orderInfo = detail.getOrderInfo();
        // 设置明细汇总
        appendItemSummary(orderInfo, orderItemList);

        orderInfo.setItemList(orderItemList);
    }

    private void appendOrderAndItemRefund(OrderDetailBo detail) {
        OrderInfoBo orderInfo = detail.getOrderInfo();
        List<OrderItemInfoBo> itemList = orderInfo.getItemList();

        List<OrderRefund> refundList = orderRefundRepository.getByOrderId(orderInfo.getOrderId());
        boolean continueItem = this.appendOrderRefundInfoAndReturnContinueItem(orderInfo, refundList);
        Map<Long, List<OrderRefund>> itemRefundMap = getItemRefund(refundList);
        // 如果没有订单级别的售后才需要进一步处理明细级别的
        itemList.forEach(item -> appendOrderItemRefundInfo(orderInfo, item, itemRefundMap.get(item.getOrderItemId()), continueItem));
    }

    private OrderInvoiceBo buildOrderInvoice(String orderId) {
        List<OrderInvoice> invoiceList = orderInvoiceRepository.getByOrderIdList(Collections.singletonList(orderId));
        if (CollectionUtils.isEmpty(invoiceList)) {
            return null;
        }
        OrderInvoiceBo invoice = JsonUtil.copy(invoiceList.get(0), OrderInvoiceBo.class);
        return invoice;
    }

    private void appendOrderInvoice(String orderId, OrderDetailBo detail) {
        List<OrderInvoice> invoiceList = orderInvoiceRepository.getByOrderIdList(Collections.singletonList(orderId));
        if (CollectionUtils.isEmpty(invoiceList)) {
            return;
        }
        OrderInvoiceBo invoice = JsonUtil.copy(invoiceList.get(0), OrderInvoiceBo.class);
        if (invoice.getRegionId() != null) {
            RegionIdsReq req = new RegionIdsReq();
            req.setRegionIds(Collections.singletonList(invoice.getRegionId()));
            Map<String, AllPathRegionResp> regionMap = ThriftResponseHelper.executeThriftCall(()->regionQueryFeign.getAllPathRegions(req));
            if (MapUtils.isNotEmpty(regionMap) && regionMap.containsKey(invoice.getRegionId().toString())) {
                AllPathRegionResp region = regionMap.get(invoice.getRegionId().toString());
                invoice.setProvinceId(region.getProvinceId());
                invoice.setProvinceName(region.getProvinceName());
                invoice.setCityId(region.getCityId());
                invoice.setCityName(region.getCityName());
                invoice.setCountyId(region.getCountyId());
                invoice.setCountyName(region.getCountyName());
                invoice.setTownIds(region.getTownIds());
                invoice.setTownsNames(region.getTownsNames());
            }
        }
        detail.setOrderInvoice(invoice);
    }

    private void appendOrderExpress(String orderId, OrderDetailBo detail,Order dbOrder) {
        List<OrderWayBill> wayBillList = orderWayBillRepository.getByOrderIdList(Collections.singletonList(orderId));
        if (CollectionUtils.isEmpty(wayBillList)) {
            return;
        }

        List<OrderExpressBo> expressList = JsonUtil.copyList(wayBillList, OrderExpressBo.class);
        expressList.forEach(t->t.setCellPhone(dbOrder.getCellPhone()));
        detail.setExpressList(expressList);
    }

    private List<OrderExpressBo> buildOrderExpress(String orderId) {
        List<OrderWayBill> wayBillList = orderWayBillRepository.getByOrderIdList(Collections.singletonList(orderId));
        if (CollectionUtils.isEmpty(wayBillList)) {
            return null;
        }
        return JsonUtil.copyList(wayBillList, OrderExpressBo.class);
    }

    private void appendStatusChangeTime(OrderDetailBo detail) {
        OrderInfoBo order = detail.getOrderInfo();
        List<String> timeList = new ArrayList<>(4);
        if (order.getOrderDate() != null) {
            timeList.add(DateUtil.format(order.getOrderDate()));
        }
        // 支付时间、发货时间、收货时间
        if (order.getPayDate() != null) {
            timeList.add(DateUtil.format(order.getPayDate()));
        }
        if (order.getShippingDate() != null) {
            timeList.add(DateUtil.format(order.getShippingDate()));
        }
        if (order.getFinishDate() != null) {
            timeList.add(DateUtil.format(order.getFinishDate()));
        }
        detail.setStatusChangeTimeList(timeList);
    }


    private void savePromotionSnapshot(CreateOrderBo createOrderBo) {
        List<OrderPromotionSnapshot> snapshotList = new ArrayList<>(10);
        for (ShopProductListBo sp : createOrderBo.getShopProductList()) {
            ShopAndProductPromotionBo shopAndProductPromotion = sp.getShop().getShopAndProductPromotion();
            if (shopAndProductPromotion == null) {
                continue;
            }
            // 订单级别的营销
            List<PromotionBo> shopPromotionList = shopAndProductPromotion.getShopPromotionList();
            if (CollectionUtils.isNotEmpty(shopPromotionList)) {
                List<OrderPromotionSnapshot> orderPromotionList = shopPromotionList.stream()
                        .map(promotion -> {
                            OrderPromotionSnapshot snapshot = new OrderPromotionSnapshot();
                            snapshot.setOrderId(sp.getOrderId());
                            snapshot.setBizType(OrderPromotionSnapshotTypeEnum.ORDER.getCode());
                            snapshot.setPromotionContent(JsonUtil.toJsonString(promotion));
                            snapshot.setCreateTime(new Date());
                            return snapshot;
                        })
                        .collect(Collectors.toList());
                snapshotList.addAll(orderPromotionList);
            }
            // 商品级别的营销
            Map<String/*skuId*/, ProductPromotionBo> productPromotionMap = shopAndProductPromotion.getProductPromotionMap();
            if (MapUtils.isNotEmpty(productPromotionMap)) {
                List<OrderPromotionSnapshot> orderProductPromotionList = productPromotionMap.values().stream()
                        .map(product -> {
                            List<PromotionBo> productPromotionList = product.getPromotionList();
                            if (CollectionUtils.isEmpty(productPromotionList)) {
                                return null;
                            }
                            return productPromotionList.stream()
                                    .map(promotion -> {
                                        OrderPromotionSnapshot snapshot = new OrderPromotionSnapshot();
                                        snapshot.setOrderId(sp.getOrderId());
                                        snapshot.setBizType(OrderPromotionSnapshotTypeEnum.PRODUCT.getCode());
                                        snapshot.setProductId(product.getProductId());
                                        snapshot.setSkuId(product.getSkuId());
                                        snapshot.setPromotionContent(JsonUtil.toJsonString(promotion));
                                        snapshot.setCreateTime(new Date());
                                        return snapshot;
                                    })
                                    .collect(Collectors.toList());
                        })
                        .filter(CollectionUtils::isNotEmpty)
                        .flatMap(List::stream)
                        .collect(Collectors.toList());
                snapshotList.addAll(orderProductPromotionList);
            }
        }
        if (CollectionUtils.isNotEmpty(snapshotList)) {
            orderPromotionSnapshotRepository.saveBatch(snapshotList);
        }
    }

    private OrderInvoice buildInvoice(ShopProductListBo shopOrder) {
        if (shopOrder.getAdditional() == null || shopOrder.getAdditional().getInvoice() == null) {
            return null;
        }
        InvoiceBo invoice = shopOrder.getAdditional().getInvoice();
        if (StrUtil.isBlank(invoice.getInvoiceTitle())) {
            return null;
        }
        return JsonUtil.copy(invoice, OrderInvoice.class);
    }

    private int getRefundCloseConfigDays(TradeSiteSettingBo setting) {
        if (setting == null) {
            return 0;
        }
        String refundCloseConfigDays = setting.getSalesReturnTimeout();
        log.info("【订单】获取售后维权期配置为: {}", refundCloseConfigDays);
        refundCloseConfigDays = StrUtil.nullToDefault(refundCloseConfigDays, "0");
        return Integer.parseInt(refundCloseConfigDays);
    }

    private long getUnpaidTimeoutMills(TradeSiteSettingBo setting) {
        if (setting == null) {
            return CommonConst.DEFAULT_PAY_TIMEOUT_HOUR * CommonConst.HOUR_MILLIS;
        }
        String unpaidTimeout = setting.getUnpaidTimeout();
        log.info("【订单】获取未支付订单超时时间配置为: {}", unpaidTimeout);
        unpaidTimeout = StrUtil.nullToDefault(unpaidTimeout, CommonConst.DEFAULT_PAY_TIMEOUT_HOUR_STR);
        return Long.parseLong(unpaidTimeout) * CommonConst.HOUR_MILLIS;
    }

    private int getAutoReceiveDays(TradeSiteSettingBo setting) {
        if (setting == null) {
            return CommonConst.DEFAULT_AUTO_FINISH_DAYS;
        }
        String autoReceiveDays = setting.getNoReceivingTimeout();
        log.info("【订单】获取自动确认收货时间配置为: {}", autoReceiveDays);
        autoReceiveDays = StrUtil.nullToDefault(autoReceiveDays, CommonConst.DEFAULT_AUTO_FINISH_DAYS_STR);
        return Integer.parseInt(autoReceiveDays);
    }


    public boolean appendOrderRefundInfoAndReturnContinueItem(OrderInfoBo orderInfo, List<OrderRefund> orderRefundList) {
        boolean orderPaid = OrderStatusEnum.isPaid(orderInfo.getOrderStatus());
        // 如果订单未付款，则完全不需要进入售后逻辑，明细也不需要，所以返回false
        if (!orderPaid) {
            return false;
        }

        // 先设置默认值
        orderInfo.setHasRefund(false);
        orderInfo.setShowRefundingDesc(false);
        orderInfo.setShowCanceledRefundBtn(false);
        orderInfo.setShowRefundingAndReturnDesc(false);
        orderInfo.setShowCancelRefundBtn(false);
        orderInfo.setShowRefundingAndReturnRejectBtn(false);
        orderInfo.setShowRefundingRejectBtn(false);
        orderInfo.setHasAuditingRefund(false);
        orderInfo.setWhetherOrderLevelRefund(false);

        // 没有订单售后记录，可以发起明细维度的售后，需要进一步处理明细
        if (CollUtil.isEmpty(orderRefundList)) {
            return true;
        }
        // 判断是否存在审核中的售后，这里的判断包括订单维度的或者明细维度的，供应商列表用于判断是否显示【发货】按钮
        boolean anyAuditing = anyAuditing(orderRefundList);
        orderInfo.setHasAuditingRefund(anyAuditing);
        // 售后记录根据申请时间倒序
        orderRefundList.sort(Comparator.comparing(OrderRefund::getApplyDate).reversed());
        // 判断订单售后列表是否存在非取消，非审核拒绝中的数据，如果有就代表存在有效的售后
        boolean hasRefund = orderRefundList.stream()
                .anyMatch(or -> RefundStatusHelper.refundValid(or.getHasCancel(), or.getSellerAuditStatus(), or.getManagerConfirmStatus()));
        orderInfo.setHasRefund(hasRefund);
        // 此时，如果有订单级别的售后，订单级别的一定只有一条记录，所以取最新的一条
        OrderRefund orderRefund = orderRefundList.get(0);
        // 第一条数据是否是订单级别的
        boolean whetherOrderFlag = RefundModeEnum.ORDER_REFUND.getCode().equals(orderRefund.getRefundMode()) || Boolean.TRUE.equals(orderRefund.getHasAllReturn());
        // 如果不是订单级别的，则设置为null，如果是，代表是订单级别的，继续往下走
        if (!whetherOrderFlag) {
            orderRefund = null;
        }
        if (orderRefund == null) {
            // 没有整单售后，可以继续发起单品的，所以 continueItem 设置为true，否则有整单有效售后的情况下，不能操作单品的售后
            return true;
        }
        // 已取消或者被拒绝，则返回当前没有售后
        if (RefundStatusHelper.refundInvalid(orderRefund.getHasCancel(), orderRefund.getSellerAuditStatus(), orderRefund.getManagerConfirmStatus())) {
            orderInfo.setHasRefund(false);
        }
        orderInfo.setRefundId(orderRefund.getId());

        // 【取消售后】用户取消了售后
        if (orderRefund.getHasCancel()) {
            orderInfo.setShowCanceledRefundBtn(true);
            // 整单被取消的情况下，可以继续发起单品的，所以 continueItem 设置为true，否则有整单有效售后的情况下，不能操作单品的售后
            return true;
        }

        orderInfo.setRefundStatus(orderRefund.getStatus());
        orderInfo.setRefundStatusDesc(RefundStatusEnum.getDesc(orderRefund.getStatus()));

        boolean continueItem = false;

        orderInfo.setWhetherOrderLevelRefund(true);

        // 到这里是订单级别的售后
        boolean auditing = RefundAuditStatusEnum.underAudit(orderRefund.getSellerAuditStatus(), orderRefund.getManagerConfirmStatus());
        // 【退款中】订单退款，审核中时显示
        // 【退货/退款中】整单退，审核中时显示
        if (RefundModeEnum.ORDER_REFUND.getCode().equals(orderRefund.getRefundMode()) && auditing) {
            orderInfo.setShowRefundingDesc(true);
        } else if (auditing) {
            orderInfo.setShowRefundingAndReturnDesc(true);
        }
        // 【取消售后】订单退款，或仅退款，且是待供应商审核或待平台审核时，显示
        boolean onlyRefund = RefundModeEnum.ORDER_REFUND.getCode().equals(orderRefund.getRefundMode()) ||
                RefundModeEnum.GOODS_REFUND.getCode().equals(orderRefund.getRefundMode());
        if (onlyRefund && auditing) {
            orderInfo.setShowCancelRefundBtn(true);
        }
        // 【取消售后】退货退款时，待供应商审核或待买家寄货时，显示
        boolean sellerAuditOrBuyerSend = RefundAuditStatusEnum.WAIT_SUPPLIER_AUDIT.getCode().equals(orderRefund.getSellerAuditStatus())
                || RefundAuditStatusEnum.WAIT_BUYER_SEND.getCode().equals(orderRefund.getSellerAuditStatus());
        if (RefundModeEnum.RETURN_AND_REFUND.getCode().equals(orderRefund.getRefundMode()) && sellerAuditOrBuyerSend) {
            orderInfo.setShowCancelRefundBtn(true);
        }
        // 【退款被拒】订单退款，供应商拒绝、平台驳回
        // 【退货/退款被拒】整单退，供应商拒绝、平台驳回
        boolean rejected = RefundStatusHelper.refundRejected(orderRefund.getSellerAuditStatus(), orderRefund.getManagerConfirmStatus());
        // 被拒的情况下，可以继续发起单品的，所以 continueItem 设置为true，否则有整单有效售后的情况下，不能操作单品的售后
        if (rejected) {
            continueItem = true;
            if (RefundModeEnum.ORDER_REFUND.getCode().equals(orderRefund.getRefundMode())) {
                orderInfo.setShowRefundingRejectBtn(true);
            } else if (Boolean.TRUE.equals(orderRefund.getHasAllReturn())) {
                orderInfo.setShowRefundingAndReturnRejectBtn(true);
            }
            orderInfo.setWhetherOrderLevelRefund(false);
            return continueItem;
        }
        // 售后平台已确认退款或者已完成，也不能操作单品售后
        if (RefundStatusHelper.refundCompleted(orderRefund.getStatus())) {
            orderInfo.setWhetherOrderLevelRefund(false);
        }
        return continueItem;
    }

    /**
     * 填充订单明细的售后信息，是否能发起售后等，需要没有整单级别的售后才需要处理
     */
    public void appendOrderItemRefundInfo(OrderInfoBo orderInfo, OrderItemInfoBo orderItem, List<OrderRefund> itemRefundList, boolean continueItem) {
        orderItem.setShowRefundingDesc(false);
        // 如果是发货后的整单有效售后，则明细显示【售后处理中】
        // 因为要设置到明细上，所以在这里判断的，满足条件时说明有整单售后，所以后续的明细不需要处理了
        if (!continueItem) {
            boolean showRefundingDesc = Boolean.TRUE.equals(orderInfo.getWhetherOrderLevelRefund());
            orderItem.setShowRefundingDesc(showRefundingDesc);
            return;
        }
        // 待收货/已完成且没过售后维权期时，可以发起单品售后，这里取反，不是这两个情况则直接返回
        if (!(OrderStatusEnum.UNDER_RECEIVE.getCode().equals(orderInfo.getOrderStatus()) ||
                (OrderStatusEnum.FINISHED.getCode().equals(orderInfo.getOrderStatus()) && Boolean.FALSE.equals(orderInfo.getHasOverAfterSales())))) {
            orderItem.setHasRefund(false);
            orderItem.setShowRefundAndReturnBtn(false);
            return;
        }

        if (CollUtil.isEmpty(itemRefundList)) {
            orderItem.setHasRefund(false);
            orderItem.setShowRefundAndReturnBtn(true);
            return;
        }

        // 根据申请时间倒序
        itemRefundList.sort(Comparator.comparing(OrderRefund::getApplyDate).reversed());
        // 过滤掉用户取消的，然后取第一条
        OrderRefund latestRefund = itemRefundList.stream()
                .filter(item -> !Boolean.TRUE.equals(item.getHasCancel()))
                .findFirst()
                .orElse(null);
        if (latestRefund == null) {
            orderItem.setHasRefund(false);
            orderItem.setShowRefundAndReturnBtn(true);
            return;
        }

        orderItem.setHasRefund(true);
        orderItem.setRefundId(latestRefund.getId());

        // 取最新的一条的售后显示售后状态，状态的判断暂时只能写死，.net数据一开始就设置了两个状态
        if (latestRefund.getSellerAuditStatus() < RefundAuditStatusEnum.SUPPLIER_PASS.getCode()) {
            RefundAuditStatusEnum status = RefundAuditStatusEnum.valueOf(latestRefund.getSellerAuditStatus());
            orderItem.setRefundStatus(status.getCode());
            orderItem.setRefundStatusDesc(status.getDesc());
        } else {
            RefundAuditStatusEnum status = RefundAuditStatusEnum.valueOf(latestRefund.getManagerConfirmStatus());
            orderItem.setRefundStatus(status.getCode());
            orderItem.setRefundStatusDesc(status.getDesc());
        }

        long totalRefundQuantity = 0L;
        BigDecimal totalRefundAmount = BigDecimal.ZERO;
        // 过滤掉用户取消的和供应商审核拒绝的
        List<OrderRefund> validList = itemRefundList.stream()
                .filter(item -> RefundStatusHelper.refundValid(item.getHasCancel(), item.getSellerAuditStatus(), item.getManagerConfirmStatus()))
                .collect(Collectors.toList());
        for (OrderRefund orderRefund : validList) {
            // 供应商审核同意弃货时，退货数量为0，此时取申请数量；供应商确认收货时也可以修改returnQuantity，但不会改为0
            totalRefundQuantity += orderRefund.getReturnQuantity() == 0 ? orderRefund.getApplyQuantity() : orderRefund.getReturnQuantity();
            totalRefundAmount = totalRefundAmount.add(orderRefund.getAmount());
        }
        // 如果订单明细还没有退完，则可以继续退
        // 考虑0元的情况
        boolean priceCanRefund = false;
        if (orderItem.getRealTotalPrice().compareTo(BigDecimal.ZERO) == 0) {
            priceCanRefund = true;
        } else {
            priceCanRefund = orderItem.getRealTotalPrice().compareTo(totalRefundAmount) > 0;
        }
        boolean itemCanRefund = priceCanRefund && orderItem.getQuantity() > totalRefundQuantity;
        // 能继续退，按钮显示
        orderItem.setShowRefundAndReturnBtn(itemCanRefund);
    }

    /**
     * 一个明细可以发起多次售后
     * <AUTHOR>
     * @param orderRefundList 订单售后列表
     */
    public Map<Long, List<OrderRefund>> getItemRefund(List<OrderRefund> orderRefundList) {
        if (CollUtil.isEmpty(orderRefundList)) {
            return Collections.emptyMap();
        }
        return orderRefundList.stream()
                // 根据时间升序，map取最后一条即代表申请时间最大
                .sorted(Comparator.comparing(OrderRefund::getApplyDate))
                .collect(Collectors.groupingBy(OrderRefund::getOrderItemId));
    }


    private boolean anyAuditing(List<OrderRefund> orderRefundList) {
        return orderRefundList.stream()
                // 过滤出未被取消的
                .filter(or -> Boolean.FALSE.equals(or.getHasCancel()))
                .anyMatch(or -> {
                    if (RefundAuditStatusEnum.WAIT_SUPPLIER_AUDIT.getCode().equals(or.getSellerAuditStatus())) {
                        return true;
                    }
                    // 平台的状态初始就设置了，所以需要综合供应商审核通过
                    boolean sellerApprove = RefundAuditStatusEnum.SUPPLIER_PASS.getCode().equals(or.getSellerAuditStatus());
                    boolean platformConfirm = RefundAuditStatusEnum.WAIT_PLATFORM_CONFIRM.getCode().equals(or.getManagerConfirmStatus());
                    return sellerApprove && platformConfirm;
                });
    }
}
