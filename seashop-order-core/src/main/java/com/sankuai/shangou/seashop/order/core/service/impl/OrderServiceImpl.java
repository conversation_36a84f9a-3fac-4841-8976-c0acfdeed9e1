package com.sankuai.shangou.seashop.order.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.statemachine.StateMachine;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.sankuai.shangou.seashop.base.boot.dto.ChangeFiled;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminProcess;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.boot.utils.NumberUtil;
import com.sankuai.shangou.seashop.base.thrift.core.RegionQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.RegionIdsReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.AllPathRegionResp;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.common.constant.FinanceLockConst;
import com.sankuai.shangou.seashop.order.common.constant.OrderConst;
import com.sankuai.shangou.seashop.order.core.mq.model.order.OrderDataHolder;
import com.sankuai.shangou.seashop.order.core.mq.model.order.SellerRemarkBo;
import com.sankuai.shangou.seashop.order.core.mq.publisher.OrderMessagePublisher;
import com.sankuai.shangou.seashop.order.core.mq.publisher.RemoveShoppingCartPublisher;
import com.sankuai.shangou.seashop.order.core.service.OrderService;
import com.sankuai.shangou.seashop.order.core.service.assit.BizNoGenerator;
import com.sankuai.shangou.seashop.order.core.service.assit.OrderBizAssist;
import com.sankuai.shangou.seashop.order.core.service.assit.OrderFinishForFinanceBizAssist;
import com.sankuai.shangou.seashop.order.core.service.assit.OrderRelateBizDataConsumeAssist;
import com.sankuai.shangou.seashop.order.core.service.assit.opLog.ChangeFieldDesc;
import com.sankuai.shangou.seashop.order.core.service.assit.opLog.OperationLogAssist;
import com.sankuai.shangou.seashop.order.core.service.model.order.BatchDeliverOrderParamBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.CancelOrderBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ConfirmReceiveBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.CreateOrderBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.DelayReceiveBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.DeliverOrderParamBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ErpOrderAddressBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ErpOrderDetailBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderDeliveryBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderDetailBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderExpressBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderInvoiceBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderItemInfoBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderOperationLogBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderReceiverBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderWayBillBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderWayBillListBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.SellerUpdateReceiverBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ShopProductBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.UpdateFreightBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.UpdateItemAmountBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.UserUpdateReceiverBo;
import com.sankuai.shangou.seashop.order.core.statemachine.OrderContext;
import com.sankuai.shangou.seashop.order.core.statemachine.OrderEvent;
import com.sankuai.shangou.seashop.order.core.statemachine.context.OrderDeliveryContext;
import com.sankuai.shangou.seashop.order.dao.core.domain.*;
import com.sankuai.shangou.seashop.order.dao.core.model.CountFlashOrderAndProductModel;
import com.sankuai.shangou.seashop.order.dao.core.model.OrderStatisticsModel;
import com.sankuai.shangou.seashop.order.dao.core.po.CommonPayRecordQueryParamBo;
import com.sankuai.shangou.seashop.order.dao.core.po.OrderStatisticsPo;
import com.sankuai.shangou.seashop.order.dao.core.po.UpdateReceiverPo;
import com.sankuai.shangou.seashop.order.dao.core.repository.*;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.SearchTimeTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderQueryFromEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.*;
import com.sankuai.shangou.seashop.order.thrift.core.response.CountFlashOrderAndProductResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.CreateOrderResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderDistributionFormResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderProductDistributionFormResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderStatisticsDataResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderStatisticsListResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderStatisticsResp;
import com.sankuai.shangou.seashop.order.thrift.finance.request.ShopIdReq;
import com.sankuai.shangou.seashop.product.thrift.core.ProductQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductSkuQueryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductSkuMergeQueryResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductSkuMergeDto;
import com.sankuai.shangou.seashop.trade.thrift.core.ShoppingCartCmdFeign;
import com.sankuai.shangou.seashop.trade.thrift.core.request.AddShoppingCartBatchReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.AddShoppingCartReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderServiceImpl implements OrderService {

    @Resource
    private BizNoGenerator bizNoGenerator;
    @Resource
    private OrderBizAssist orderBizAssist;
    @Resource
    private OrderMessagePublisher orderMessagePublisher;
    @Resource
    private OrderRelateBizDataConsumeAssist orderRelateBizDataConsumeAssist;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private StateMachine<OrderStatusEnum, OrderEvent, OrderContext> orderStateMachine;
    @Resource
    private RemoveShoppingCartPublisher removeShoppingCartPublisher;
    @Resource
    private OrderOperationLogRepository orderOperationLogRepository;
    @Resource
    private OrderWayBillRepository orderWayBillRepository;
    @Resource
    private RegionQueryFeign regionQueryFeign;
    @Resource
    private OrderInvoiceRepository orderInvoiceRepository;
    @Resource
    private OperationLogAssist operationLogAssist;
    @Resource
    private ProductQueryFeign productQueryFeign;
    @Resource
    private ShoppingCartCmdFeign shoppingCartCmdFeign;
    @Resource
    private OrderFinishForFinanceBizAssist orderFinishForFinanceBizAssist;
    @Resource
    private OrderPayRecordRepository orderPayRecordRepository;

    /**
     * 创建订单
     * <pre>
     *     1. 创建订单时，在交易服务已经校验过数据的有效性了，订单服务只根据参数数据创建订单，不计算不校验
     *     2. 创建订单时，首先发送一条延时消息，用于最终校验订单是否创建成功，下单逻辑当前采用的是保证数据的最终一致性
     *     3. 扣减库存和核销营销活动时，如果处理失败，也都会发送失效的消息，延时消息和失败的消息都是用于后续回滚相关数据的
     *     4. 保存数据时，涉及到多个表的数据(订单主表、订单明细、购物车删除、营销快照)，采用的是本地事务，保证数据的一致性，这个事务粒度不大
     * </pre>
     *
     * <AUTHOR>
     */
    @Override
    public CreateOrderResp createOrder(CreateOrderBo createOrderBo) {
        Long userId = createOrderBo.getUserInfo().getUserId();
        // 接口幂等性校验
        orderBizAssist.validateCreateOrderIdempotence(createOrderBo.getUniqueId());
        // 批量生成订单号
        int orderCount = createOrderBo.getShopProductList().size();
        List<String> orderIdList = bizNoGenerator.generateOrderNo(orderCount);
        // 发送订单检查 延时 消息，用于最后检查订单是否创建成功
        orderMessagePublisher.sendOrderCheckMessage(userId, orderIdList);
        OrderDataHolder orderDataHolder = orderBizAssist.buildOrderData(orderIdList, createOrderBo);
        try {
            // 消费处理订单相关的业务数据，比如扣减库存、核销优惠券等等
            orderRelateBizDataConsumeAssist.consumeOrderRelateBizData(orderDataHolder, createOrderBo);
            // 保存数据
            orderBizAssist.saveOrderAndRelated(orderDataHolder, createOrderBo);
        } catch (BusinessException be) {
            log.error("创建订单失败，订单号：{}", orderIdList, be);
            // 发送处理失效的消息，回滚相关数据
            orderMessagePublisher.sendOrderFailureMessage(userId, orderIdList);
            throw be;
        } catch (Exception e) {
            log.error("创建订单失败，订单号：{}", orderIdList, e);
            // 发送处理失效的消息，回滚相关数据
            orderMessagePublisher.sendOrderFailureMessage(userId, orderIdList);
            throw new BusinessException("系统异常");
        }
        // 异步删除购物车，BCP处理最终一致，放到事务外不影响事务执行
        // 异步消息失败，不影响用户下单，小概率事件，用户可以手动删除购物车
        try {
            // 下单来源为购物车才需要删除购物车
            if (!createOrderBo.isNotFromCart()) {
                List<String> skuIdList = createOrderBo.getShopProductList().stream()
                        .flatMap(sp -> sp.getProductList().stream().map(ShopProductBo::getSkuId))
                        .collect(Collectors.toList());
                removeShoppingCartPublisher.sendRemoveShoppingCartMessage(createOrderBo.getUserInfo().getUserId(), skuIdList);
            }
            for (String orderId : orderIdList) {
                // 发送通知，比如需要更新订单ES
                orderMessagePublisher.sendOrderChangeMessage(orderId, OrderMessageEventEnum.CREATE_ORDER);
            }
        } catch (Exception e) {
            log.warn("【订单保存】删除购物车消息发送失败，userId: {}", createOrderBo.getUserInfo().getUserId());
        }

        CreateOrderResp resp = new CreateOrderResp();
        resp.setOrderIdList(orderDataHolder.getNeedPayOrderIdList());
        resp.setAllOrderIdList(orderIdList);
        return resp;
    }

    @Override
    public OrderDetailBo getDetail(String orderId, OrderQueryFromEnum queryFrom) {
        return orderBizAssist.buildOrderDetail(orderId, queryFrom);
    }

    @Override
    public ErpOrderDetailBo getErpOrderDetail(String orderId, String sourceOrderId) {
        return orderBizAssist.buildOrderDetailByOrderIdOrOrderSourceId(orderId, sourceOrderId);
    }

    @Override
    public List<OrderOperationLogBo> getOrderLog(String orderId) {
        List<OrderOperationLog> dbList = orderOperationLogRepository.getByOrderId(orderId);
        return JsonUtil.copyList(dbList, OrderOperationLogBo.class);
    }

    @Override
    public void cancelPay(Long userId, String orderId) {
        // 触发状态流转
        OrderContext orderContext = OrderContext.builder()
                .userId(userId)
                .orderId(orderId)
                .build();
        orderStateMachine.fireEvent(OrderStatusEnum.PAYING, OrderEvent.CANCEL_PAY, orderContext);
    }

    @Override
    public void cancelOrder(Long userId, String orderId) {
        CancelOrderBo cancelOrderBo = CancelOrderBo.builder()
                .userId(userId)
                .userName("")
                .orderId(orderId)
                .build();
        // 触发状态流转
        cancelOrder(cancelOrderBo);
    }

    @Override
    public void cancelOrder(CancelOrderBo cancelOrderBo) {
        // 触发状态流转
        OrderContext orderContext = OrderContext.builder()
                .userId(cancelOrderBo.getUserId())
                .orderId(cancelOrderBo.getOrderId())
                .userName(cancelOrderBo.getUserName())
                .cancelReason(cancelOrderBo.getCancelReason())
                .build();
        orderStateMachine.fireEvent(OrderStatusEnum.UNDER_PAY, OrderEvent.CANCEL_ORDER, orderContext);
    }

    @Override
    public void reBuy(Long userId, String orderId) {
        Order dbOrder = orderRepository.getByOrderId(orderId);
        orderBizAssist.validateOrder(userId, dbOrder);
        reBuyOrderItem(dbOrder.getUserId(), orderId);
    }

    @Override
    public void updateReceiver(UserUpdateReceiverBo userUpdateReceiverBo) {
        Order dbOrder = orderRepository.getByOrderId(userUpdateReceiverBo.getOrderId());
        orderBizAssist.validateOrder(userUpdateReceiverBo.getUserId(), dbOrder);
        UpdateReceiverPo updateReceiverPo = buildUpdateReceiverPo(dbOrder, userUpdateReceiverBo);
        // 自定义sql只改部分字段，防止可能的并发导致其他字段被覆盖
        int cnt = orderRepository.updateOrderShipInfo(updateReceiverPo);
        if (cnt != 1) {
            throw new BusinessException("修改收货人信息失败");
        }
    }

    @ExaminProcess(actionName = "供应商修改订单收货信息", processModel = ExaminModelEnum.ORDER,
            processType = ExaProEnum.MODIFY, serviceMethod = "updateReceiverBySeller",
            dto = SellerUpdateReceiverBo.class, entity = Order.class)
    @Override
    public void updateReceiverBySeller(SellerUpdateReceiverBo updateReceiverBo) {
        Order dbOrder = orderRepository.getByOrderId(updateReceiverBo.getOrderId());
        AssertUtil.throwIfNull(dbOrder, "订单不存在");
        AssertUtil.throwIfTrue(!dbOrder.getShopId().equals(updateReceiverBo.getShopId()), "店铺不匹配");

        UpdateReceiverPo updateReceiverPo = buildUpdateReceiverPo(dbOrder, updateReceiverBo);
        updateReceiverPo.setUserName(updateReceiverBo.getUserName());
        // 保存修改数据和日志
        orderBizAssist.saveUpdateReceiverData(dbOrder.getOrderId(), updateReceiverPo);
    }


    /**
     * <pre>
     *     如果之前没改过价，则差异用前后的实付金额计算，否则用前后的变化计算。
     *     比如，没改价之前，realTotalPrice=0.15，discountAmount=0；
     *     第一次改价，newRealTotalPrice=0.03，根据逻辑，discountAmount=0.12
     *     第二次改价，newRealTotalPrice=0.06，则 discountAmount应该等于0.09，代表的是从 0.15 改成 0.06 经过的变化
     * </pre>
     *
     * @param updateItemAmountBo void
     * <AUTHOR>
     */
    @Override
    public void updateItemAmount(UpdateItemAmountBo updateItemAmountBo) {
        OrderItem orderItem = orderItemRepository.getById(updateItemAmountBo.getItemId());
        AssertUtil.throwIfNull(orderItem, "订单明细不存在");

        Order dbOrder = orderRepository.getByOrderIdForceMaster(orderItem.getOrderId());
        AssertUtil.throwIfNull(dbOrder, "订单不存在");
        AssertUtil.throwIfTrue(!dbOrder.getShopId().equals(updateItemAmountBo.getShopId()), "店铺不匹配");
        AssertUtil.throwIfTrue(!OrderStatusEnum.UNDER_PAY.getCode().equals(dbOrder.getOrderStatus()), "只有待付款订单才能改价");

        // 修改订单明细数据：设置部分字段实现部分更新
        OrderItem updateOrderItem = new OrderItem();
        updateOrderItem.setId(orderItem.getId());
        BigDecimal newRealTotalPrice = updateItemAmountBo.getUpdatedAmount();
        BigDecimal originRealTotalPrice = orderItem.getRealTotalPrice();
        // .net之前是增减数量，比如原价 0.1，改价增加 3元，则改价后，realTotalPrice=3.1元，discountAmount=-3元
        // 考虑历史数据的兼容性，这里对数据的存储也这么做
        BigDecimal discountAmount = BigDecimal.ZERO;
        // 如果之前没改过价，则差异用前后的实付金额计算，否则用前后的变化计算。
        if (orderItem.getDiscountAmount() == null || orderItem.getDiscountAmount().compareTo(BigDecimal.ZERO) == 0) {
            discountAmount = NumberUtil.multiply(newRealTotalPrice.subtract(originRealTotalPrice), -1);
        } else {
            discountAmount = originRealTotalPrice.subtract(newRealTotalPrice).add(orderItem.getDiscountAmount());
        }
        // 改价是输入的金额作为目标的最终金额
        updateOrderItem.setRealTotalPrice(newRealTotalPrice);
        updateOrderItem.setEnabledRefundAmount(newRealTotalPrice);
        updateOrderItem.setDiscountAmount(discountAmount);
        log.info("【供应商改价】明细调整, itemId={}, 改价前金额={}, 改价后金额={}, 前后差异金额={}",
                orderItem.getId(), originRealTotalPrice, newRealTotalPrice, discountAmount);

        // 修改订单上的金额和佣金
        List<OrderItem> orderitemList = orderItemRepository.getByOrderId(dbOrder.getOrderId());
        // 需要重新计算平台佣金
        BigDecimal commissionTotalAmount = BigDecimal.ZERO;
        for (OrderItem item : orderitemList) {
            // 当前修改的，用改价后的金额，其他的用原价
            BigDecimal productAmount = item.getId().equals(orderItem.getId()) ? item.getRealTotalPrice() : orderItem.getRealTotalPrice();
            BigDecimal amount = NumberUtil.multiply(productAmount, item.getCommisRate()).setScale(2, RoundingMode.HALF_UP);
            commissionTotalAmount = commissionTotalAmount.add(amount);
        }
        // 修改订单数据：设置部分字段实现部分更新
        Order updateOrder = new Order();
        // 新的订单总金额，实收金额也要重置
        BigDecimal newOrderTotalAmount = dbOrder.getTotalAmount().subtract(originRealTotalPrice).add(newRealTotalPrice);
        updateOrder.setTotalAmount(newOrderTotalAmount);
        updateOrder.setActualPayAmount(newOrderTotalAmount);
        updateOrder.setLastModifyTime(new Date());
        updateOrder.setCommissionTotalAmount(commissionTotalAmount);
        log.info("【供应商改价】订单调整, orderId={}, 原totalAmount={}, 新totalAmount={}, 原actualPayAmount={}, 新actualPayAmount={}, 原commissionTotalAmount={}, 新commissionTotalAmount={}",
                dbOrder.getOrderId(), dbOrder.getTotalAmount(), updateOrder.getTotalAmount(),
                dbOrder.getActualPayAmount(), updateOrder.getActualPayAmount(), dbOrder.getCommissionTotalAmount(), updateOrder.getCommissionTotalAmount());

        TransactionHelper.doInTransaction(() -> {
            orderRepository.updateByOrderId(dbOrder.getOrderId(), updateOrder);
            orderItemRepository.updateById(orderItem.getId(), updateOrderItem);
            String logContent = String.format(CommonConst.LOG_ORDER_UPDATE_ITEM_AMOUNT_PATTERN, originRealTotalPrice, newRealTotalPrice);
            orderBizAssist.addOrderOperationLog(dbOrder.getOrderId(), updateItemAmountBo.getUserName(), logContent);
        });
        // 记录接口操作日志，这个目前的aop日志切面不支持
        try {
            updateOrder.setOrderId(dbOrder.getOrderId());
            List<ChangeFiled> orderChanged = operationLogAssist.buildChangeList(dbOrder, updateOrder, ChangeFieldDesc.ORDER_UPDATE_ITEM_AMOUNT_ORDER);
            List<ChangeFiled> itemChanged = operationLogAssist.buildChangeList(orderItem, updateOrderItem, ChangeFieldDesc.ORDER_UPDATE_ITEM_AMOUNT_ITEM);
            // 理论上都不为空
            orderChanged.addAll(itemChanged);
            operationLogAssist.log(ExaminModelEnum.ORDER, ExaProEnum.MODIFY, "供应商改价", JsonUtil.toJsonString(orderChanged), updateItemAmountBo, updateItemAmountBo.getUserName());
        } catch (Exception e) {
            log.error("【供应商改价】记录接口操作日志失败", e);
        }

        // 发送订单改价事件
        try {
            orderMessagePublisher.sendOrderChangeMessage(dbOrder.getOrderId(), OrderMessageEventEnum.UPDATE_ORDER_PRICE);
        } catch (Exception e) {
            log.error("发送订单改价事件失败, orderId: {}", dbOrder.getOrderId(), e);
        }
    }

    @Override
    public void updateFreight(UpdateFreightBo updateFreightBo) {
        Order dbOrder = orderRepository.getByOrderIdForceMaster(updateFreightBo.getOrderId());
        AssertUtil.throwIfNull(dbOrder, "订单不存在");
        AssertUtil.throwIfTrue(!dbOrder.getShopId().equals(updateFreightBo.getShopId()), "店铺不匹配");
        AssertUtil.throwIfTrue(!OrderStatusEnum.UNDER_PAY.getCode().equals(dbOrder.getOrderStatus()), "只有待付款订单才能修改运费");

        Order updateOrder = getUpdatedFreightOrder(updateFreightBo, dbOrder);
        boolean updated = orderRepository.updateByOrderId(dbOrder.getOrderId(), updateOrder);
        AssertUtil.throwIfTrue(!updated, "修改运费失败");

        // 保存订单操作日志
        orderBizAssist.addOrderOperationLog(dbOrder.getOrderId(), updateFreightBo.getUserName(), "供应商修改订单运费金额：" + updateFreightBo.getUpdatedFreight() + "元");

        // 记录接口操作日志，这个目前的aop日志切面不支持
        try {
            updateOrder.setOrderId(dbOrder.getOrderId());
            List<ChangeFiled> orderChanged = operationLogAssist.buildChangeList(dbOrder, updateOrder, ChangeFieldDesc.ORDER_UPDATE_FREIGHT_ORDER);
            operationLogAssist.log(ExaminModelEnum.ORDER, ExaProEnum.MODIFY, "供应商修改运费",
                    JsonUtil.toJsonString(orderChanged), updateFreightBo, updateFreightBo.getUserName());
        } catch (Exception e) {
            log.error("【供应商改价】记录接口操作日志失败", e);
        }

        // 发送订单改价事件
        try {
            orderMessagePublisher.sendOrderChangeMessage(dbOrder.getOrderId(), OrderMessageEventEnum.UPDATE_ORDER_PRICE);
        } catch (Exception e) {
            log.error("发送订单改价事件失败, orderId: {}", dbOrder.getOrderId(), e);
        }
    }

    @NotNull
    private static Order getUpdatedFreightOrder(UpdateFreightBo updateFreightBo, Order dbOrder) {
        BigDecimal originFreight = NumberUtil.nullToZero(dbOrder.getFreight());
        // 备份原运费，用于计算运费变更
        BigDecimal backupFreight = dbOrder.getBackupFreight() == null ? dbOrder.getFreight() : dbOrder.getBackupFreight();
        // 重新计算总金额
        BigDecimal newTotalAmount = dbOrder.getTotalAmount().subtract(originFreight).add(updateFreightBo.getUpdatedFreight());
        // 修改订单数据：只改部分字段, 防止可能的并发导致其他字段被覆盖
        Order updateOrder = new Order();
        updateOrder.setFreight(updateFreightBo.getUpdatedFreight());
        updateOrder.setBackupFreight(backupFreight);
        updateOrder.setTotalAmount(newTotalAmount);
        updateOrder.setActualPayAmount(newTotalAmount);
        return updateOrder;
    }

    /**
     * 延迟收货可以认为是一种内部状态流转，且能复用状态机的一些逻辑
     *
     * @param delayReceiveBo 入参
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delayReceive(DelayReceiveBo delayReceiveBo) {
        // 触发状态流转
        OrderContext orderContext = OrderContext.builder()
                .userId(delayReceiveBo.getUser().getUserId())
                .userName(delayReceiveBo.getUser().getUserName())
                .orderId(delayReceiveBo.getOrderId())
                .build();
        orderStateMachine.fireEvent(OrderStatusEnum.UNDER_RECEIVE, OrderEvent.DELAY_RECEIVE, orderContext);
    }

    @Override
    public void confirmReceive(ConfirmReceiveBo confirmReceiveBo) {
        // 触发状态流转
        OrderContext orderContext = OrderContext.builder()
                .userId(confirmReceiveBo.getUser().getUserId())
                .userName(confirmReceiveBo.getUser().getUserName())
                .orderId(confirmReceiveBo.getOrderId())
                .build();
        orderStateMachine.fireEvent(OrderStatusEnum.UNDER_RECEIVE, OrderEvent.CONFIRM_RECEIVE, orderContext);
    }

    @Override
    public ErpOrderDetailBo confirmErpOrderReceive(ConfirmReceiveBo confirmReceiveBo) {
        // 触发状态流转
        OrderContext orderContext = OrderContext.builder()
                .userId(confirmReceiveBo.getUser().getUserId())
                .userName(confirmReceiveBo.getUser().getUserName())
                .orderId(confirmReceiveBo.getOrderId())
                .build();
        orderStateMachine.fireEvent(OrderStatusEnum.UNDER_RECEIVE, OrderEvent.CONFIRM_RECEIVE, orderContext);
        return orderBizAssist.convertErpOrder(orderContext.getOrder());
    }

    @Override
    public OrderWayBillListBo getOrderWayBill(Long shopId, List<String> orderIdList) {
        List<Order> orderList = orderRepository.getByOrderIdList(orderIdList);
        if (CollUtil.isEmpty(orderList)) {
            throw new BusinessException("订单不存在");
        }
        boolean anyNotShop = orderList.stream().anyMatch(order -> !order.getShopId().equals(shopId));
        if (anyNotShop) {
            throw new BusinessException("存在订单不属于当前店铺");
        }

        List<OrderWayBill> billList = orderWayBillRepository.getByOrderIdList(orderIdList);
        if (billList == null) {
            billList = Collections.emptyList();
        }
        Map<String, List<OrderWayBill>> billMap = billList.stream()
                .collect(Collectors.groupingBy(OrderWayBill::getOrderId));

        OrderWayBillListBo wayBillListBo = new OrderWayBillListBo();
        boolean anyHasExpress = false;
        List<OrderWayBillBo> orderWayBillList = new ArrayList<>(orderList.size());
        OrderWayBillBo orderWayBillBo = null;
        for (Order order : orderList) {
            orderWayBillBo = new OrderWayBillBo();
            // 设置相关参数
            anyHasExpress = buildOrderWayBill(orderWayBillBo, order, billMap.get(order.getOrderId()));
            orderWayBillList.add(orderWayBillBo);
        }

        wayBillListBo.setNeedExpress(anyHasExpress);
        wayBillListBo.setOrderWayBillList(orderWayBillList);
        return wayBillListBo;
    }

    @Override
    public void deliverOrder(DeliverOrderParamBo delivery) {
        // 触发状态流转
        OrderDeliveryContext orderContext = OrderDeliveryContext.builder()
                .shopId(delivery.getShop().getShopId())
                .orderId(delivery.getDelivery().getOrderId())
                .needExpress(delivery.getNeedExpress())
                .expressList(delivery.getDelivery().getExpressList())
                .userName(delivery.getUser().getUserName())
                .userId(delivery.getUser().getUserId())
                .build();
        orderStateMachine.fireEvent(OrderStatusEnum.UNDER_SEND, OrderEvent.DELIVERY, orderContext);
    }

    /**
     * 这里先复用状态机进行处理，最外层加事务保证多条数据一起成功失败
     *
     * @param orderDeliveryBo void
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchDeliverOrder(BatchDeliverOrderParamBo orderDeliveryBo) {
        // 批量发货前置检测
        this.beforeBatchDeliverOrderValidate(orderDeliveryBo);
        orderDeliveryBo.getOrderWayBillList().forEach(delivery -> {
            // 触发状态流转
            OrderDeliveryContext orderContext = OrderDeliveryContext.builder()
                    .shopId(orderDeliveryBo.getOperationShopId())
                    .orderId(delivery.getOrderId())
                    .userId(orderDeliveryBo.getOperationUserId())
                    .userName(orderDeliveryBo.getUser().getUserName())
                    .needExpress(orderDeliveryBo.getNeedExpress())
                    .expressList(delivery.getExpressList())
                    .build();
            orderStateMachine.fireEvent(OrderStatusEnum.UNDER_SEND, OrderEvent.DELIVERY, orderContext);
        });
    }

    @Override
    public void updateExpress(DeliverOrderParamBo orderDeliveryBo) {
        OrderDeliveryBo delivery = orderDeliveryBo.getDelivery();
        List<OrderWayBill> beforeList = orderWayBillRepository.getByOrderId(delivery.getOrderId());
        int cnt = orderWayBillRepository.removeByOrderId(delivery.getOrderId());
        log.info("【修改物流】删除原有物流信息，orderId: {}, cnt: {}", delivery.getOrderId(), cnt);
        List<OrderWayBill> wayBillList = null;
        if (Boolean.TRUE.equals(orderDeliveryBo.getNeedExpress())) {
            Date now = new Date();
            wayBillList = delivery.getExpressList().stream()
                    .map(express -> {
                        OrderWayBill wayBill = new OrderWayBill();
                        wayBill.setOrderId(delivery.getOrderId());
                        wayBill.setExpressCompanyName(express.getExpressCompanyName());
                        wayBill.setShipOrderNumber(express.getShipOrderNumber());
                        wayBill.setExpressCompanyCode(express.getExpressCompanyCode());
                        wayBill.setCreateTime(now);
                        wayBill.setUpdateTime(now);
                        return wayBill;
                    })
                    .collect(Collectors.toList());
            orderWayBillRepository.saveBatch(wayBillList);
        }
        // 记录接口操作日志，这个目前的aop日志切面不支持
        try {
            List<ChangeFiled> orderChanged = operationLogAssist.buildChangeListForUpdateExpress(beforeList, wayBillList,
                    ChangeFieldDesc.ORDER_SELLER_UPDATE_WAYBILL_BILL, ChangeFieldDesc.ORDER_SELLER_UPDATE_WAYBILL_ORDER, delivery.getOrderId());
            operationLogAssist.log(ExaminModelEnum.ORDER, ExaProEnum.MODIFY, "供应商修改运单号",
                    JsonUtil.toJsonString(orderChanged), orderDeliveryBo.getOperationUserId(), orderDeliveryBo.getOperationShopId(), "");
        } catch (Exception e) {
            log.error("【供应商改价】记录接口操作日志失败", e);
        }
    }

    @Override
    public void addExpress(DeliverOrderParamBo orderDeliveryBo) {
        OrderDeliveryBo delivery = orderDeliveryBo.getDelivery();
        if (delivery == null) {
            return;
        }
        if (!Boolean.TRUE.equals(orderDeliveryBo.getNeedExpress())) {
            log.info("【新增物流】orderId: {}, 不需要运单信息", delivery.getOrderId());
            return;
        }
        String orderId = delivery.getOrderId();
        //先查询当前orderId的所有物流信息
        List<String> existExpressNos = Optional.ofNullable(orderWayBillRepository.getByOrderId(orderId))
                .orElse(Collections.emptyList())
                .stream()
                .map(OrderWayBill::getShipOrderNumber)
                .collect(Collectors.toList());
        log.info("【新增物流】orderId: {}, 数据库存在的运单号: {}", delivery.getOrderId(),
                existExpressNos);

        List<OrderWayBill> wayBillList = delivery.getExpressList().stream()
                .filter(orderExpressBo -> !existExpressNos.contains(orderExpressBo.getShipOrderNumber()))
                .map(express -> {
                    OrderWayBill wayBill = new OrderWayBill();
                    wayBill.setOrderId(delivery.getOrderId());
                    wayBill.setExpressCompanyName(express.getExpressCompanyName());
                    wayBill.setShipOrderNumber(express.getShipOrderNumber());
                    return wayBill;
                })
                .collect(Collectors.toList());
        log.info("【新增物流】orderId: {}, 新增的运单信息:{}", delivery.getOrderId(), JsonUtil.toJsonString(wayBillList));
        if (CollectionUtil.isEmpty(wayBillList)) {
            return;
        }
        orderWayBillRepository.saveBatch(wayBillList);
    }

    @Override
    public OrderStatisticsResp getOrderStatistics(OrderStatisticsReq request) {

        BigDecimal saleAmount = orderRepository.sumOrderAmount(
                OrderStatisticsPo.builder()
                        .startPayDate(request.getBeginTime())
                        .endPayDate(request.getEndTime())
                        .shopId(request.getShopId()).build()
        );

        int payOrdersNum = orderRepository.countOrder(
                OrderStatisticsPo.builder()
                        .startPayDate(request.getBeginTime())
                        .endPayDate(request.getEndTime())
                        .shopId(request.getShopId()).build()
        );

        int orderNum = orderRepository.countOrder(
                OrderStatisticsPo.builder()
                        .startOrderDate(request.getBeginTime())
                        .endOrderDate(request.getEndTime())
                        .shopId(request.getShopId()).build()
        );

        OrderStatisticsResp orderStatisticsResp = new OrderStatisticsResp();
        orderStatisticsResp.setOrdersNum((long) orderNum);
        orderStatisticsResp.setPayOrdersNum((long) payOrdersNum);
        orderStatisticsResp.setSaleAmount(saleAmount);

        return orderStatisticsResp;
    }

    @Override
    public OrderStatisticsResp getOrderStatisticsByMember(OrderStatisticsMemberReq request) {

        BigDecimal saleAmount = orderRepository.sumOrderAmountMember(
                OrderStatisticsPo.builder()
                        .startPayDate(request.getBeginTime())
                        .endPayDate(request.getEndTime())
                        .shopId(request.getUserId()).build()
        );

        long payOrdersNum = orderRepository.lambdaQuery()
                .eq(Order::getUserId, request.getUserId())
                .between(Order::getOrderDate, request.getBeginTime(), request.getEndTime())
                .count();

        OrderStatisticsResp orderStatisticsResp = new OrderStatisticsResp();
        orderStatisticsResp.setOrdersNum(payOrdersNum);
        orderStatisticsResp.setPayOrdersNum(payOrdersNum);
        orderStatisticsResp.setSaleAmount(saleAmount);

        return orderStatisticsResp;
    }

    @Override
    public OrderStatisticsListResp getOrderStatisticsList(OrderStatisticsReq request) {

        // 初始化数据
        List<OrderStatisticsDataResp> list = new ArrayList<>();
        for (DateTime date : DateUtil.rangeToList(request.getBeginTime(), request.getEndTime(), DateField.DAY_OF_MONTH)) {
            OrderStatisticsDataResp dataResp = new OrderStatisticsDataResp();
            dataResp.setDateStr(DateUtil.format(date, OrderConst.DATE_FORMAT));
            dataResp.setAmount(BigDecimal.ZERO);
            list.add(dataResp);
        }
        List<OrderStatisticsModel> orderStatisticsList = orderRepository.orderStatistics(
                OrderStatisticsPo.builder()
                        .startPayDate(request.getBeginTime())
                        .endPayDate(request.getEndTime())
                        .shopId(request.getShopId())
                        .build()
        );
        if (CollUtil.isNotEmpty(orderStatisticsList)) {
            for (OrderStatisticsModel orderStatisticsDto : orderStatisticsList) {
                list.stream().filter(orderStatisticsDataResp -> orderStatisticsDataResp.getDateStr().equals(orderStatisticsDto.getPayDate())).forEach(orderStatisticsDataResp -> {
                    BigDecimal amount = orderStatisticsDto.getProductTotalAmount().add(orderStatisticsDto.getFreight()).add(orderStatisticsDto.getTax())
                            .subtract(orderStatisticsDto.getCouponAmount()).subtract(orderStatisticsDto.getDiscountAmount()).subtract(orderStatisticsDto.getMoneyOffAmount());
                    orderStatisticsDataResp.setAmount(amount);
                });
            }
        }
        list.parallelStream().forEach(
                resp -> {
                    String desc = String.format(OrderConst.FINANCE_ORDER_STATISTICS_DESC,
                            DateUtil.format(DateUtil.parse(resp.getDateStr(), OrderConst.DATE_FORMAT), "MM/dd"),
                            resp.getAmount());
                    resp.setDesc(desc);
                }
        );

        OrderStatisticsListResp orderStatisticsListResp = new OrderStatisticsListResp();
        orderStatisticsListResp.setList(list);

        return orderStatisticsListResp;
    }

//    @Override
//    public EachStatusCountResp queryEachStatusCount(Long userId) {
//        EachStatusCountResp result = new EachStatusCountResp();
//        result.setAllOrderCount(orderRepository.countNum(OrderRefundModel.builder().userId(userId).build()));
//        result.setWaitingForPay(orderRepository.countNum(OrderRefundModel.builder().userId(userId).inStatusList(Arrays.asList(OrderStatusEnum.UNDER_PAY.getCode())).build()));
//        result.setWaitingForDelivery(orderRepository.countNum(OrderRefundModel.builder().userId(userId).inStatusList(Arrays.asList(OrderStatusEnum.UNDER_SEND.getCode())).build()));
//        result.setWaitingForRecieve(orderRepository.countNum(OrderRefundModel.builder().userId(userId).inStatusList(Arrays.asList(OrderStatusEnum.UNDER_RECEIVE.getCode())).build()));
//        result.setWaitingForComments(orderRepository.countNum(OrderRefundModel.builder().userId(userId).inStatusList(Arrays.asList(OrderStatusEnum.FINISHED.getCode())).hasCommented(0).build()));
//        result.setRefundCount(orderRefundRepository.countNum(userId,
//                Arrays.asList(RefundStatusEnum.REFUND_SUCCESS.getCode(),
//                        RefundStatusEnum.PLATFORM_REFUSE.getCode(),
//                        RefundStatusEnum.BUYER_CANCEL.getCode())));
//        return result;
//    }

    @Override
    public OrderInfoDto queryLastOrderInfo(Long userId) {
        Order order = orderRepository.queryLastOrderInfo(userId);
        if (Objects.isNull(order)) {
            return new OrderInfoDto();
        }
        return JsonUtil.copy(order, OrderInfoDto.class);
    }

    @Override
    public BasePageResp<ErpOrderDetailBo> queryOrderPageForErp(QueryErpPageOrderReq req) {
        // count?
        SearchTimeTypeEnum timeType = req.getTimeType() == null ? SearchTimeTypeEnum.CREATE_TIME : req.getTimeType();
        Integer orderStatus = req.getStatus() == null ? null : req.getStatus().getCode();
        Date startCreateTime = null;
        Date endCreateTime = null;
        Date startUpdateTime = null;
        Date endUpdateTime = null;
        switch (timeType) {
            case CREATE_TIME:
                startCreateTime = req.getStartTime();
                endCreateTime = req.getEndTime();
                break;
            case UPDATE_TIME:
                startUpdateTime = req.getStartTime();
                endUpdateTime = req.getEndTime();
                break;
            default:
                break;
        }
        PageInfo<Order> orderPageInfo = orderRepository.queryOrderPage(req.getPageNo(), req.getPageSize(),
                req.getShopId(), req.getOrderId(), orderStatus,
                startCreateTime, endCreateTime, startUpdateTime, endUpdateTime);
        if (CollectionUtil.isEmpty(orderPageInfo.getList())) {
            return new BasePageResp<>(orderPageInfo.getPageNum(),
                    orderPageInfo.getPageSize(), orderPageInfo.getPages(),
                    orderPageInfo.getTotal(), Collections.emptyList());
        }

        List<String> orderIds =
                orderPageInfo.getList().stream().map(Order::getOrderId).collect(Collectors.toList());
        // 查询转换 order item
        Map<String, List<OrderItemInfoBo>> orderItemsMap = queryAndBuildOrderItems(orderIds);
        // 查询转换 express
        Map<String, List<OrderExpressBo>> expressMap = queryAndBuildOrderExpresses(orderIds);
        List<ErpOrderDetailBo> orderList = orderPageInfo.getList().stream()
                .map(order -> {
                    ErpOrderDetailBo detailBo = convertErpOrderDetail(order);
                    detailBo.setItemList(orderItemsMap.get(order.getOrderId()));
                    detailBo.setExpressList(expressMap.get(order.getOrderId()));
                    return detailBo;
                }).collect(Collectors.toList());

        // 查询发票信息
        List<OrderInvoice> invoiceList = orderInvoiceRepository.getByOrderIdList(orderIds);
        Map<String, OrderInvoice> invoiceMap = invoiceList.stream()
                .collect(Collectors.toMap(OrderInvoice::getOrderId, Function.identity(), (k1, k2) -> k1));
        orderList.forEach(order -> {
            OrderInvoice orderInvoice = invoiceMap.get(order.getOrderId());
            order.setOrderInvoice(orderInvoice == null ? null : JsonUtil.copy(orderInvoice, OrderInvoiceBo.class));
        });

        orderItemsMap.clear();
        expressMap.clear();
        invoiceMap.clear();
        return new BasePageResp<>(orderPageInfo.getPageNum(),
                orderPageInfo.getPageSize(), orderPageInfo.getPages(),
                orderPageInfo.getTotal(), orderList);
    }

    @Override
    public List<ErpOrderDetailBo> queryBatchOrderForErp(QueryBatchErpOrderReq req) {
        List<String> orderIds = req.getOrderIds();
        List<Order> orders;
        if (CollectionUtil.isNotEmpty(orderIds)) {
            orders = orderRepository.getByOrderIdList(req.getOrderIds());
        } else {
            orders = orderRepository.getBySourceOrderIdList(req.getSourceOrderIds());
            if (orders != null) {
                orderIds = orders.stream().map(Order::getOrderId).collect(Collectors.toList());
            }
        }
        if (CollectionUtil.isEmpty(orders)) {
            return Collections.emptyList();
        }
        // 查询转换 order item
        Map<String, List<OrderItemInfoBo>> orderItemsMap = queryAndBuildOrderItems(orderIds);
        // 查询转换 express
        Map<String, List<OrderExpressBo>> expressMap = queryAndBuildOrderExpresses(orderIds);
        List<ErpOrderDetailBo> orderList = orders.stream()
                .map(order -> {
                    ErpOrderDetailBo detailBo = convertErpOrderDetail(order);
                    detailBo.setItemList(orderItemsMap.get(order.getOrderId()));
                    detailBo.setExpressList(expressMap.get(order.getOrderId()));
                    return detailBo;
                }).collect(Collectors.toList());
        orderItemsMap.clear();
        expressMap.clear();
        return orderList;
    }

    @ExaminProcess(actionName = "供应商添加备注", processModel = ExaminModelEnum.ORDER,
            processType = ExaProEnum.MODIFY, serviceMethod = "sellerRemark",
            dto = SellerRemarkBo.class, entity = Order.class)
    @Override
    public void sellerRemark(SellerRemarkBo remarkBo) {
        Order dbOrder = orderRepository.getByOrderId(remarkBo.getOrderId());
        AssertUtil.throwIfNull(dbOrder, "订单不存在");
        AssertUtil.throwIfTrue(!dbOrder.getShopId().equals(remarkBo.getShop().getShopId()), "店铺不匹配");

        boolean cnt = orderRepository.updateSellerRemark(remarkBo.getOrderId(), remarkBo.getSellerRemark(), remarkBo.getSellerRemarkFlag());
        AssertUtil.throwIfTrue(!cnt, "修改卖家备注失败");
    }

    @Override
    public List<OrderDistributionFormResp> exportOrderDistribution(QueryOrderDistributionReq queryReq) {
        List<OrderDistributionFormResp> result = new ArrayList<>();
        List<Order> orderList = orderRepository.getByOrderIdList(queryReq.getOrderIdList());
        Map<Long, Order> orderMap = orderList.stream().collect(Collectors.toMap(Order::getId, Function.identity(), (v1, v2) -> v2));
        // 以订单项为维度
        List<OrderItem> orderItemList = orderItemRepository.getByOrderIdList(queryReq.getOrderIdList());
        Map<Long, OrderItem> orderItemMap = orderItemList.stream().collect(Collectors.toMap(OrderItem::getId, Function.identity(), (v1, v2) -> v2));

        List<String> skuIdList = orderItemList.stream().map(OrderItem::getSkuId).distinct().collect(Collectors.toList());
        // in查询一次最多200个,所以一次业务处理200条数据
        List<ProductSkuMergeDto> allProductSkuMergeDtoList = new ArrayList<>();
        int perNum = CommonConst.QUERY_LIMIT;
        int skuCount = (skuIdList.size() + perNum - 1) / perNum;
        for (int i = 0; i < skuCount; i++) {
            int start = perNum * i;
            int end = perNum * (i + 1) > skuIdList.size() ? skuIdList.size() : perNum * (i + 1);
            List<String> perSkuIdList = skuIdList.subList(start, end);
            ProductSkuQueryReq productSkuQueryReq = new ProductSkuQueryReq();
            productSkuQueryReq.setSkuIdList(perSkuIdList);
            ProductSkuMergeQueryResp resp = ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.queryProductSkuMerge(productSkuQueryReq));
            allProductSkuMergeDtoList.addAll(resp.getProductSkuList());
        }
        Map<String, ProductSkuMergeDto> skuMap = allProductSkuMergeDtoList.stream().collect(Collectors.toMap(ProductSkuMergeDto::getSkuId, Function.identity(), (v1, v2) -> v2));
        for (Map.Entry<Long, OrderItem> entry : orderItemMap.entrySet()) {
            Order order = orderMap.get(Long.parseLong(entry.getValue().getOrderId()));
            ProductSkuMergeDto sku = skuMap.get(entry.getValue().getSkuId());
            long stock = Objects.isNull(sku) || sku.getStock() == null ? 0 : sku.getStock();
            OrderDistributionFormResp resp = new OrderDistributionFormResp();
            resp.setOrderId(order.getOrderId());
            resp.setProductName(entry.getValue().getProductName());
            resp.setProductCode(Objects.isNull(sku) ? null : sku.getProductCode());
            resp.setSkuName(Objects.isNull(sku) ? null : this.getSpecValue(sku));
            resp.setQuantity(String.valueOf(entry.getValue().getQuantity()));
            resp.setStockQuantity(String.valueOf(stock + entry.getValue().getQuantity()));
            resp.setRemark(order.getUserRemark());
            result.add(resp);
        }
        return result;
    }

    @Override
    public List<OrderProductDistributionFormResp> exportOrderProductDistribution(QueryOrderDistributionReq queryReq) {
        List<OrderProductDistributionFormResp> result = new ArrayList<>();
        // 以sku为维度
        List<OrderItem> orderItemList = orderItemRepository.getByOrderIdList(queryReq.getOrderIdList());
        // 使用Stream API和groupingBy方法对OrderItem对象进行分组
        Map<String, List<OrderItem>> groupedBysku = orderItemList.stream().collect(Collectors.groupingBy(OrderItem::getSkuId));

        List<String> skuIdList = orderItemList.stream().map(OrderItem::getSkuId).distinct().collect(Collectors.toList());
        // in查询一次最多200个,所以一次业务处理200条数据
        List<ProductSkuMergeDto> allProductSkuMergeDtoList = new ArrayList<>();
        int perNum = CommonConst.QUERY_LIMIT;
        int skuCount = (skuIdList.size() + perNum - 1) / perNum;
        for (int i = 0; i < skuCount; i++) {
            int start = perNum * i;
            int end = perNum * (i + 1) > skuIdList.size() ? skuIdList.size() : perNum * (i + 1);
            List<String> perSkuIdList = skuIdList.subList(start, end);
            ProductSkuQueryReq productSkuQueryReq = new ProductSkuQueryReq();
            productSkuQueryReq.setSkuIdList(perSkuIdList);
            ProductSkuMergeQueryResp resp = ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.queryProductSkuMerge(productSkuQueryReq));
            allProductSkuMergeDtoList.addAll(resp.getProductSkuList());
        }
        Map<String, ProductSkuMergeDto> skuMap = allProductSkuMergeDtoList.stream().collect(Collectors.toMap(ProductSkuMergeDto::getSkuId, Function.identity(), (v1, v2) -> v2));

        for (Map.Entry<String, List<OrderItem>> entry : groupedBysku.entrySet()) {
            ProductSkuMergeDto sku = skuMap.get(entry.getKey());
            long stock = Objects.isNull(sku) || sku.getStock() == null ? 0 : sku.getStock();
            OrderProductDistributionFormResp resp = new OrderProductDistributionFormResp();
            resp.setProductName(Objects.isNull(sku) ? null : sku.getProductName());
            resp.setProductCode(Objects.isNull(sku) ? null : sku.getProductCode());
            resp.setSkuName(Objects.isNull(sku) ? null : this.getSpecValue(sku));
            resp.setQuantity(entry.getValue().stream().mapToLong(OrderItem::getQuantity).sum());
            resp.setStockQuantity(String.valueOf(resp.getQuantity() + stock));
            result.add(resp);
        }
        return result;
    }

    @Override
    public CountFlashOrderAndProductResp countFlashOrderAndProduct(ShopIdReq request) {
        CountFlashOrderAndProductModel countFlashOrderAndProductDto = orderRepository.countOrderAndProduct(request.getShopId(), OrderStatusEnum.FINISHED.getCode());
        return JsonUtil.copy(countFlashOrderAndProductDto, CountFlashOrderAndProductResp.class);
    }

    @Override
    public void sellerCancelOrder(CancelOrderBo cancelOrderBo) {
        Order dbOrder = orderRepository.getByOrderId(cancelOrderBo.getOrderId());
        Order updateOrder = new Order();
        AssertUtil.throwIfNull(dbOrder, "订单不存在");
        AssertUtil.throwIfTrue(!dbOrder.getShopId().equals(cancelOrderBo.getShopId()), "店铺不匹配");
        TransactionHelper.doInTransaction(() -> {
            // 更新订单状态
            int cnt = orderRepository.updateOrderClose(Collections.singletonList(dbOrder.getOrderId()),
                    OrderStatusEnum.UNDER_PAY.getCode(), OrderStatusEnum.CLOSED.getCode(), cancelOrderBo.getCancelReason());
            log.info("【供应商取消订单】更新订单状态，cnt={}", cnt);
            if (cnt != 1) {
                throw new BusinessException("订单状态已经变更，不能关闭");
            }
            // 保存操作日志
            String cancelReason = StrUtil.nullToDefault(cancelOrderBo.getCancelReason(), "");
            String userName = StrUtil.nullToDefault(cancelOrderBo.getUserName(), "");
            orderBizAssist.addOrderOperationLog(dbOrder.getOrderId(), userName, cancelReason);

            updateOrder.setOrderStatus(OrderStatusEnum.CLOSED.getCode());
            updateOrder.setCloseReason(cancelReason);
            updateOrder.setOrderId(dbOrder.getOrderId());
        });
        // 发送消息，异步回滚库存和营销
        orderMessagePublisher.sendOrderChangeMessage(dbOrder.getOrderId(), OrderMessageEventEnum.CANCEL_ORDER);
        // 记录接口操作日志，这个目前的aop日志切面不支持
        try {
            List<ChangeFiled> orderChanged = operationLogAssist.buildChangeList(dbOrder, updateOrder, ChangeFieldDesc.ORDER_SELLER_CANCEL_ORDER_ORDER);
            operationLogAssist.log(ExaminModelEnum.ORDER, ExaProEnum.MODIFY, "供应商取消订单",
                    JsonUtil.toJsonString(orderChanged), cancelOrderBo.getOperationUserId(), cancelOrderBo.getOperationShopId(), cancelOrderBo.getUserName());
        } catch (Exception e) {
            log.error("【供应商取消订单】记录接口操作日志失败", e);
        }
    }

    @Override
    public void reBuyBySeller(String orderId, Long shopId) {
        Order order = orderRepository.getByOrderId(orderId);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        if (!order.getShopId().equals(shopId)) {
            throw new BusinessException("店铺不匹配");
        }
        reBuyOrderItem(order.getUserId(), orderId);
    }

    @Override
    public OrderWayBillBo getUserOrderWayBill(Long userId, String orderId) {
        Order order = orderRepository.getByOrderId(orderId);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        if (!order.getUserId().equals(userId)) {
            throw new BusinessException("订单不属于当前用户");
        }
        List<OrderWayBill> wayBillList = orderWayBillRepository.getByOrderId(orderId);
        OrderWayBillBo wayBillBo = new OrderWayBillBo();
        buildOrderWayBill(wayBillBo, order, wayBillList);
        return wayBillBo;
    }

    @Override
    public BasePageResp<OrderInfoDto> getSoldTrades(OrderGetSoldTradesReq param) {
        param.checkParam();
        PageInfo<Order> orderPageInfo = orderRepository.getSoldTrades(param.getShopId(), param.getStartCreated(), param.getEndCreated(),
                param.getStatus(), param.getBuyerUname(), param.getPageNo(), param.getPageSize());
        if (CollectionUtil.isEmpty(orderPageInfo.getList())) {
            return new BasePageResp<>(orderPageInfo.getPageNum(),
                    orderPageInfo.getPageSize(), orderPageInfo.getPages(),
                    orderPageInfo.getTotal(), Collections.emptyList());
        }
        List<OrderInfoDto> orderInfoDtoList = orderPageInfo.getList().stream().map(order -> {
            OrderInfoDto orderInfoDto = JsonUtil.copy(order, OrderInfoDto.class);
            return orderInfoDto;
        }).collect(Collectors.toList());
        return new BasePageResp<>(orderPageInfo.getPageNum(),
                orderPageInfo.getPageSize(), orderPageInfo.getPages(),
                orderPageInfo.getTotal(), orderInfoDtoList);
    }

    @Override
    public BasePageResp<OrderInfoDto> getIncrementSoldTrades(OrderGetSoldTradesReq param) {
        param.checkParam();
        PageInfo<Order> orderPageInfo = orderRepository.getSoldTradesLastModifyTime(param.getShopId(), param.getStartCreated(), param.getEndCreated(),
                param.getStatus(), param.getBuyerUname(), param.getPageNo(), param.getPageSize());
        if (CollectionUtil.isEmpty(orderPageInfo.getList())) {
            return new BasePageResp<>(orderPageInfo.getPageNum(),
                    orderPageInfo.getPageSize(), orderPageInfo.getPages(),
                    orderPageInfo.getTotal(), Collections.emptyList());
        }
        List<OrderInfoDto> orderInfoDtoList = orderPageInfo.getList().stream().map(order -> {
            OrderInfoDto orderInfoDto = JsonUtil.copy(order, OrderInfoDto.class);
            return orderInfoDto;
        }).collect(Collectors.toList());
        return new BasePageResp<>(orderPageInfo.getPageNum(),
                orderPageInfo.getPageSize(), orderPageInfo.getPages(),
                orderPageInfo.getTotal(), orderInfoDtoList);
    }

    @Override
    public OrderInfoDto getTradeByOrderId(String orderId) {
        Order order = orderRepository.getByOrderId(orderId);
        if (order == null) {
            return null;
        }
        return JsonUtil.copy(order, OrderInfoDto.class);
    }

    @Override
    public List<String> generateOrderNo(Integer size) {
        List<String> orderIdList = bizNoGenerator.generateOrderNo(size);
        return orderIdList;
    }

    @Override
    public void confirmReceiptWx(ConfirmReceiptWxReq req) {
        Date now = new Date();
        String lockKey = String.format(OrderConst.CONFIRM_RECEIPT_ORDER_PREFIX, req.getThirdTransactionNo());
        LockHelper.lock(lockKey, ()->{
            Order order = orderRepository.getOne(new LambdaQueryWrapper<Order>().eq(Order::getGatewayOrderId, req.getThirdTransactionNo()).last("limit 1"));
            Assert.isTrue(ObjectUtil.isNotNull(order), "订单不存在");
            Assert.isTrue(OrderStatusEnum.UNDER_RECEIVE.getCode().equals(order.getOrderStatus()), "当前订单无法确认收货");
            List<String> orderIdList = Lists.newArrayList();
            orderIdList.add(order.getOrderId());
            List<String> gatewayOrderIdList = Lists.newArrayList();
            gatewayOrderIdList.add(order.getGatewayOrderId());
            // 查询订单明细
            List<OrderItem> orderItemList = orderItemRepository.getByOrderIdList(orderIdList);
            Map<String, List<OrderItem>> orderItemMap = orderItemList.stream().collect(Collectors.groupingBy(OrderItem::getOrderId));
            // 查询支付成功的记录，因为可能重复支付，支付记录表的数据可能不唯一，所以加上订单表上关联的支付单号查询
            CommonPayRecordQueryParamBo param = CommonPayRecordQueryParamBo.builder()
                    .payStatusEq(PayStatusEnum.PAY_SUCCESS.getCode())
                    .orderIdListIn(orderIdList)
                    .payNoListIn(gatewayOrderIdList)
                    .build();
            List<OrderPayRecord> paySuccessList = orderPayRecordRepository.getByCondition(param);
            Map<String, OrderPayRecord> paySuccessMap = paySuccessList.stream()
                    .collect(Collectors.toMap(OrderPayRecord::getOrderId, Function.identity(), (oldV, newV) -> newV));
            TransactionHelper.doInTransaction(() -> {
                // 修改订单为已完成
                int cnt = orderRepository.updateOrderReceived(order.getOrderId(), OrderStatusEnum.UNDER_RECEIVE.getCode(),
                        OrderStatusEnum.FINISHED.getCode(), now, CommonConst.MESSAGE_AUTO_FINISH_REMARK);
                // 订单更新成功，再更新其他数据
                if (cnt == 1) {
                    orderFinishForFinanceBizAssist.finishOrder(order, now, orderItemMap.get(order.getOrderId()), paySuccessMap.get(order.getOrderId()));
                }
            });
            try {
                orderMessagePublisher.sendOrderChangeMessage(order.getOrderId(), OrderMessageEventEnum.COMPLETE_ORDER);
            } catch (Exception e) {
                log.error("【手动确认收货】发送订单事件失败 ,orderId={}", order.getOrderId(), e);
            }
        });
    }


    //****************************************************************

    /**
     * 获取sku规格值
     * 逗号拼接 spec1Value_spec2Value_spec3Value
     * 如果值为空的跳过
     *
     * @param sku sku
     * @return 规格值
     */
    public String getSpecValue(ProductSkuMergeDto sku) {
        if (sku == null) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotEmpty(sku.getSpec1Value())) {
            sb.append(sku.getSpec1Value()).append(",");
        }
        if (StringUtils.isNotEmpty(sku.getSpec2Value())) {
            sb.append(sku.getSpec2Value()).append(",");
        }
        if (StringUtils.isNotEmpty(sku.getSpec3Value())) {
            sb.append(sku.getSpec3Value()).append(",");
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    private ErpOrderDetailBo convertErpOrderDetail(Order order) {
        ErpOrderDetailBo detail = new ErpOrderDetailBo();
        detail.setOrderId(order.getOrderId());
        detail.setSourceOrderId(order.getSourceOrderId());
        detail.setUserId(order.getUserId());
        detail.setShopId(order.getShopId());
        detail.setShopName(order.getShopName());
        detail.setOrderDate(order.getOrderDate());
        detail.setTotalAmount(order.getTotalAmount());
        detail.setProductTotalAmount(order.getProductTotalAmount());
        detail.setTotalAmount(order.getTotalAmount());
        detail.setActualPayAmount(order.getActualPayAmount());
        detail.setOrderStatus(OrderStatusEnum.getByCode(order.getOrderStatus()));
        detail.setCouponAmount(order.getCouponAmount());
        detail.setFreightAmount(NumberUtil.nullToZero(order.getFreight()));
        detail.setTaxAmount(order.getTax());
        detail.setUserRemark(order.getUserRemark());
        detail.setSellerRemark(order.getSellerRemark());
        detail.setPayChannelName(order.getPaymentTypeName());
        detail.setCreateDate(order.getCreateTime());
        detail.setUserName(order.getUserName());

        if (StringUtils.isNotBlank(order.getShipTo())) {
            ErpOrderAddressBo addressBo = new ErpOrderAddressBo();
            addressBo.setShipTo(order.getShipTo());
            addressBo.setCellPhone(order.getCellPhone());
            addressBo.setRegionId(order.getRegionId());
            addressBo.setTopRegionId(order.getTopRegionId());
            addressBo.setAddress(order.getAddress());
            addressBo.setRegionFullName(order.getRegionFullName());
            addressBo.setReceiveLatitude(order.getReceiveLatitude());
            addressBo.setReceiveLongitude(order.getReceiveLongitude());
            detail.setOrderAddress(addressBo);
        }
        return detail;
    }

    private Map<String, List<OrderItemInfoBo>> queryAndBuildOrderItems(List<String> orderIds) {
        List<OrderItem> orderItems = orderItemRepository.getByOrderIdList(orderIds);
        if (orderItems == null) {
            return Collections.emptyMap();
        }
        return orderItems.stream().map(orderItem -> {
            OrderItemInfoBo orderItemInfoBo = new OrderItemInfoBo();
            orderItemInfoBo.setOrderId(orderItem.getOrderId());
            orderItemInfoBo.setOrderItemId(orderItem.getId());
            orderItemInfoBo.setProductId(orderItem.getProductId());
            orderItemInfoBo.setProductName(orderItem.getProductName());
            orderItemInfoBo.setMainImagePath(orderItem.getThumbnailsUrl());
            orderItemInfoBo.setSkuId(orderItem.getSkuId());
            orderItemInfoBo.setQuantity(orderItem.getQuantity());
            orderItemInfoBo.setSalePrice(orderItem.getSalePrice());
            orderItemInfoBo.setRealTotalPrice(orderItem.getRealTotalPrice());
            orderItemInfoBo.setDiscountAmount(orderItem.getDiscountAmount());
            orderItemInfoBo.setRealDiscountAmount(getRealDiscountAmount(orderItem));

            orderItemInfoBo.setSku(orderItem.getSku());
            orderItemInfoBo.setSkuAutoId(orderItem.getSkuAutoId());
            //orderItemInfoBo.setEnabledRefundAmount(orderItem.getEnabledRefundAmount());
            BigDecimal updatedAmount = orderItem.getDiscountAmount();
            if (updatedAmount != null) {
                orderItemInfoBo.setUpdatedAmount(updatedAmount);
            }
            //color;size;version 非空拼接
            String skuName = Stream.of(orderItem.getColor(), orderItem.getSize(), orderItem.getVersion())
                    .filter(StringUtils::isNotBlank).collect(Collectors.joining(";"));
            orderItemInfoBo.setSkuName(skuName);

            orderItemInfoBo.setEnabledRefundAmount(orderItem.getEnabledRefundAmount());

            return orderItemInfoBo;
        }).collect(Collectors.groupingBy(OrderItemInfoBo::getOrderId));
    }

    private BigDecimal getRealDiscountAmount(OrderItem orderItem) {
        BigDecimal amount = BigDecimal.ZERO;
        if (orderItem.getCouponDiscount() != null) {
            amount = amount.add(orderItem.getCouponDiscount());
        }
        if (orderItem.getFullDiscount() != null) {
            amount = amount.add(orderItem.getFullDiscount());
        }
        if (orderItem.getMoneyOff() != null) {
            amount = amount.add(orderItem.getMoneyOff());
        }
        return amount;
    }

    private Map<String, List<OrderExpressBo>> queryAndBuildOrderExpresses(List<String> orderIds) {
        List<OrderWayBill> orderExpressBos = orderWayBillRepository.getByOrderIdList(orderIds);
        if (orderExpressBos == null) {
            return Collections.emptyMap();
        }
        return orderExpressBos.stream().map(orderWayBill -> {
            OrderExpressBo orderExpressBo = new OrderExpressBo();
            orderExpressBo.setExpressCompanyName(orderWayBill.getExpressCompanyName());
            orderExpressBo.setShipOrderNumber(orderWayBill.getShipOrderNumber());
            return Pair.of(orderWayBill.getOrderId(), orderExpressBo);
        }).collect(Collectors.groupingBy(Pair::getKey, Collectors.mapping(Pair::getValue,
                Collectors.toList())));
    }

    private UpdateReceiverPo buildUpdateReceiverPo(Order dbOrder, OrderReceiverBo updateReceiverBo) {
        UpdateReceiverPo updateReceiverPo = new UpdateReceiverPo();
        updateReceiverPo.setOrderId(dbOrder.getOrderId());
        updateReceiverPo.setShipTo(updateReceiverBo.getShipTo());
        updateReceiverPo.setCellPhone(updateReceiverBo.getCellPhone());
        updateReceiverPo.setRegionId(updateReceiverBo.getRegionId());
        updateReceiverPo.setTopRegionId(updateReceiverBo.getTopRegionId());
        // 根据regionId查询regionFullName
        RegionIdsReq regionIdsReq = new RegionIdsReq();
        regionIdsReq.setRegionIds(Collections.singletonList(updateReceiverBo.getRegionId()));
        Map<String, AllPathRegionResp> regionMap = ThriftResponseHelper.executeThriftCall(() -> regionQueryFeign.getAllPathRegions(regionIdsReq));
        if (MapUtil.isEmpty(regionMap)) {
            throw new BusinessException("收货地址不正确");
        }
        AllPathRegionResp region = regionMap.get(updateReceiverBo.getRegionId().toString());
        if (region == null) {
            throw new BusinessException("收货地址不正确");
        }
        updateReceiverPo.setTopRegionId(region.getProvinceId().intValue());
        updateReceiverPo.setRegionFullName(getRegionFullName(region));
        updateReceiverPo.setAddress(updateReceiverBo.getAddress());
        return updateReceiverPo;
    }

    private void reBuyOrderItem(Long userId, String orderId) {
        // 查询订单明细
        List<OrderItem> dbItemList = orderItemRepository.getByOrderIdList(Collections.singletonList(orderId));
        if (CollectionUtil.isEmpty(dbItemList)) {
            log.warn("【供应商重新购买】订单明细为空，orderId={}", orderId);
            return;
        }
        List<AddShoppingCartReq> addShoppingCartReqs = JsonUtil.copyList(dbItemList, AddShoppingCartReq.class, (s, t) -> t.setUserId(userId));
        AddShoppingCartBatchReq req = new AddShoppingCartBatchReq();
        req.setShoppingCartList(addShoppingCartReqs);
        ThriftResponseHelper.executeThriftCall(() -> shoppingCartCmdFeign.addShoppingCartBatch(req));
    }

    private String getRegionFullName(AllPathRegionResp allPathRegionResp) {
        return allPathRegionResp.getProvinceName() + " " + allPathRegionResp.getCityName() + " " + allPathRegionResp.getCountyName() + " " + allPathRegionResp.getTownsNames();
    }

    private boolean buildOrderWayBill(OrderWayBillBo orderWayBillBo, Order order, List<OrderWayBill> subBillList) {
        boolean anyHasExpress = false;
        orderWayBillBo.setOrderId(order.getOrderId());
        orderWayBillBo.setRegionId(order.getRegionId());
        orderWayBillBo.setShipTo(order.getShipTo());
        String regionFullName = order.getRegionFullName();
        if (StrUtil.isNotBlank(regionFullName)) {
            orderWayBillBo.setRegionFullName(regionFullName);
        } else {
            orderWayBillBo.setRegionFullName("");
        }
        String addressFullName = order.getRegionFullName();
        if (StrUtil.isNotBlank(order.getAddress())) {
            addressFullName = addressFullName + order.getAddress();
        }
        orderWayBillBo.setAddressFullName(addressFullName);

        if (CollUtil.isEmpty(subBillList)) {
            orderWayBillBo.setExpressList(Collections.emptyList());
        } else {
            anyHasExpress = true;
            List<OrderExpressBo> expressBoList = subBillList.stream()
                    .map(bill -> {
                        OrderExpressBo expressBo = new OrderExpressBo();
                        expressBo.setCellPhone(order.getCellPhone());
                        expressBo.setExpressCompanyName(bill.getExpressCompanyName());
                        expressBo.setShipOrderNumber(bill.getShipOrderNumber());
                        expressBo.setExpressCompanyCode(bill.getExpressCompanyCode());
                        return expressBo;
                    })
                    .collect(Collectors.toList());
            orderWayBillBo.setExpressList(expressBoList);
        }
        return anyHasExpress;
    }


    /**
     * 批量发货前置检测
     *
     * @param orderDeliveryBo 批量发货参数
     * <AUTHOR>
     */
    private void beforeBatchDeliverOrderValidate(BatchDeliverOrderParamBo orderDeliveryBo) {
        List<String> orderIdList = orderDeliveryBo.getOrderWayBillList().stream().map(OrderDeliveryBo::getOrderId).collect(Collectors.toList());
        List<Order> orderList = orderRepository.getByOrderIdList(orderIdList);
        if (CollectionUtils.isEmpty(orderList)) {
            throw new BusinessException("订单不存在");
        }
        orderList.forEach(order -> {
            // 状态检测
            if (!OrderStatusEnum.UNDER_SEND.getCode().equals(order.getOrderStatus())) {
                log.info("订单状态不满足批量发货条件，订单号：{}, 当前状态：{}", order.getOrderId(), order.getOrderStatus());
                throw new BusinessException("请选择【待发货】订单进行批量发货");
            }
        });
    }

}
