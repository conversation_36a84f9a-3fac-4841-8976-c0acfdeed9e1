package com.sankuai.shangou.seashop.order.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.common.enums.YesOrNoEnum;
import com.sankuai.shangou.seashop.order.common.es.EagleService;
import com.sankuai.shangou.seashop.order.common.remote.RemoteMemberService;
import com.sankuai.shangou.seashop.order.common.remote.SettingRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.base.TradeSiteSettingBo;
import com.sankuai.shangou.seashop.order.common.utils.SettingUtil;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryOrderBaseBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryOrderCommonBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryUserOrderBo;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class OrderSearchParamBuilder {

    @Resource
    private EagleService eagleService;
    @Resource
    private SettingRemoteService settingRemoteService;
    @Resource
    private RemoteMemberService remoteMemberService;

    /**
     * 构建卖家订单搜索查询条件
     *
     * @param searchBo
     * @return BoolQueryBuilder 引用对象其实会改变数据，但由于有些条件直接可以确定不需要继续查询的，返回null特殊处理所以返回builder
     * <AUTHOR>
     */
    protected BoolQueryBuilder buildCommonSearchCondition(BoolQueryBuilder boolQueryBuilder, QueryOrderCommonBo searchBo) {
        if (StrUtil.isNotBlank(searchBo.getOrderId())) {
            // 订单ID精确匹配
            String[] orderIds = searchBo.getOrderId().split(",");
            if (orderIds.length > 1) {
                boolQueryBuilder.filter(QueryBuilders.termsQuery("orderId", orderIds));
            } else {
                boolQueryBuilder.filter(QueryBuilders.termQuery("orderId", orderIds[0]));
            }
        }
        // cellPhone 是收货人手机号
        if (StrUtil.isNotBlank(searchBo.getCellPhone())) {
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("cellPhone", eagleService.appendWildcard(searchBo.getCellPhone())));
        }
        // userPhone 是商家(买家)手机号，没有放到ES，因为用户手机号码可以改
        if (StrUtil.isNotBlank(searchBo.getUserPhone())) {
            List<Long> userIds = remoteMemberService.queryUserIdByMobile(searchBo.getUserPhone());
            // 如果传入了手机号码，但是没有查到匹配的用户，则不需要再继续
            if (CollUtil.isEmpty(userIds)) {
                return null;
            }
            boolQueryBuilder.filter(QueryBuilders.termsQuery("userId", userIds));
        }
        if (StrUtil.isNotBlank(searchBo.getUserName())) {
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("userName", eagleService.appendWildcard(searchBo.getUserName())));
        }
        if (searchBo.getOrderStatus() != null && !OrderStatusEnum.UNKNOWN.getCode().equals(searchBo.getOrderStatus())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("orderStatus", searchBo.getOrderStatus()));
        }
        if (CollUtil.isNotEmpty(searchBo.getOrderStatusList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("orderStatus", searchBo.getOrderStatusList()));
        }
        if (searchBo.getShopIdList() != null) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("shopId", searchBo.getShopIdList()));
        }
        if (CollUtil.isNotEmpty(searchBo.getPaymentTypeList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("paymentType", searchBo.getPaymentTypeList()));
        }
        if (CollUtil.isNotEmpty(searchBo.getPaymentList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("payment", searchBo.getPaymentList()));
        }
        if (CollUtil.isNotEmpty(searchBo.getOrderTypeList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("orderType", searchBo.getOrderTypeList()));
        }
        if (searchBo.getInvoiceType() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("orderInvoice.invoiceType", searchBo.getInvoiceType()));
        }
        if (searchBo.getHasInvoice() != null && searchBo.getHasInvoice()) {
            boolQueryBuilder.filter(QueryBuilders.existsQuery("orderInvoice"));
        }
        if (searchBo.getHasComment() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("hasCommented", searchBo.getHasComment() ? 1 : 0));
        }
        if (CollUtil.isNotEmpty(searchBo.getPlatformList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("platform", searchBo.getPlatformList()));
        }
        appendOrderDateParam(boolQueryBuilder, searchBo.getOrderStartTime(), searchBo.getOrderEndTime());
        appendFinishDateParam(searchBo, boolQueryBuilder);
        return boolQueryBuilder;
    }


    /**
     * 构建商家订单搜索查询条件
     *
     * @param searchBo org.elasticsearch.index.query.BoolQueryBuilder
     * <AUTHOR>
     */
    protected BoolQueryBuilder buildUserSearchCondition(QueryUserOrderBo searchBo) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("userId", searchBo.getUserId()));
        String searchKey = searchBo.getSearchKey();
        if (StrUtil.isNotBlank(searchKey)) {
            // 子过滤
            BoolQueryBuilder searchKeyBuilder = QueryBuilders.boolQuery();
            boolean onlyNumber = StrUtil.isNumeric(searchKey);
            // 订单ID精确匹配
            searchKeyBuilder.should(QueryBuilders.termQuery("orderId", searchKey))
                    // 商品名称模糊匹配
                    .should(QueryBuilders.nestedQuery("orderItems",
                            QueryBuilders.wildcardQuery("orderItems.productName", eagleService.appendWildcard(searchKey)),
                            org.apache.lucene.search.join.ScoreMode.None));
            // 仅数字的情况下才匹配商品ID和规格ID
            if (onlyNumber) {
                // 如果不是long类型，则不匹配商品ID，否则会报错
                boolean isLong = NumberUtil.isLong(searchKey);
                if (isLong) {
                    // 商品ID精确匹配
                    searchKeyBuilder.should(QueryBuilders.nestedQuery("orderItems",
                            QueryBuilders.termQuery("orderItems.productId", searchKey),
                            org.apache.lucene.search.join.ScoreMode.None));
                }
                // 规格ID精确匹配
                searchKeyBuilder.should(QueryBuilders.nestedQuery("orderItems",
                        QueryBuilders.termQuery("orderItems.skuAutoId", searchKey),
                        org.apache.lucene.search.join.ScoreMode.None));
            }
            boolQueryBuilder.filter(searchKeyBuilder);
        }
        if (searchBo.getOrderStatus() != null && !OrderStatusEnum.UNKNOWN.getCode().equals(searchBo.getOrderStatus())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("orderStatus", searchBo.getOrderStatus()));
        }
        if (CollUtil.isNotEmpty(searchBo.getOrderStatusList())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("orderStatus", searchBo.getOrderStatusList()));
        }
        // 如果是查询待评价订单，1：订单还没有评价；2：订单在允许评价期限内
        if (Boolean.TRUE.equals(searchBo.getQueryUnCommented())) {
            TradeSiteSettingBo setting = settingRemoteService.getTradeSiteSetting();
            int commentTimeoutDays = SettingUtil.getIntValueOrDefault("订单完成后允许评价天数", setting.getOrderCommentTimeout(), CommonConst.DEFAULT_COMMENT_TIMEOUT_DAYS);
            // 如果订单完成超过了评价期限，就不能评价了，所以查询完成时间大于等于当前时间-超时时间，也就是 指定天数内完成的
            Date timeoutDate = DateUtil.offsetDay(new Date(), -commentTimeoutDays);
            Date beginOfDay = DateUtil.beginOfDay(timeoutDate);
            boolQueryBuilder.must(QueryBuilders.termQuery("hasCommented", YesOrNoEnum.NO.getCode()))
                    .must(QueryBuilders.existsQuery("finishDate"))
                    .must(QueryBuilders.termQuery("orderStatus", OrderStatusEnum.FINISHED.getCode()));
            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("finishDate");
            rangeQueryBuilder.gte(beginOfDay.getTime());
            boolQueryBuilder.must(rangeQueryBuilder);
        }
        if (StrUtil.isNotBlank(searchBo.getOrderId())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("orderId", searchBo.getOrderId()));
        }
        if (StrUtil.isNotBlank(searchBo.getProductName())) {
            boolQueryBuilder.must(
                    QueryBuilders.nestedQuery("orderItems",
                            QueryBuilders.wildcardQuery("orderItems.productName", eagleService.appendWildcard(searchBo.getProductName())),
                            org.apache.lucene.search.join.ScoreMode.None)
            );
        }
        appendOrderDateParam(boolQueryBuilder, searchBo.getOrderStartTime(), searchBo.getOrderEndTime());
        appendFinishDateParam(searchBo, boolQueryBuilder);
        return boolQueryBuilder;
    }

    protected void appendOrderDateParam(BoolQueryBuilder boolQueryBuilder, Date orderStartTime, Date orderEndTime) {
        if (orderStartTime != null || orderEndTime != null) {
            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("orderDate");
            if (orderStartTime != null) {
                rangeQueryBuilder.gte(orderStartTime.getTime());
            }
            if (orderEndTime != null) {
                rangeQueryBuilder.lte(orderEndTime.getTime());
            }
            boolQueryBuilder.filter(rangeQueryBuilder);
        }
    }

    protected void appendFinishDateParam(QueryOrderBaseBo searchBo, BoolQueryBuilder boolQueryBuilder) {
        if (searchBo.getFinishStartTime() != null || searchBo.getFinishEndTime() != null) {
            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("finishDate");
            if (searchBo.getFinishStartTime() != null) {
                rangeQueryBuilder.gte(searchBo.getFinishStartTime().getTime());
            }
            if (searchBo.getFinishEndTime() != null) {
                rangeQueryBuilder.lte(searchBo.getFinishEndTime().getTime());
            }
            boolQueryBuilder.filter(rangeQueryBuilder);
        }
    }


    protected List<SortBuilder<FieldSortBuilder>> buildFieldSortList() {
        List<SortBuilder<FieldSortBuilder>> sortBuilders = new ArrayList<>(2);
        appendDefaultFieldSort(sortBuilders);
        return sortBuilders;
    }

    protected void appendDefaultFieldSort(List<SortBuilder<FieldSortBuilder>> sortBuilders) {
        // 默认根据订单创建时间倒序排列，时间一致按照ID降序
        sortBuilders.add(SortBuilders.fieldSort("orderDate").order(SortOrder.DESC));
        sortBuilders.add(SortBuilders.fieldSort("orderId").order(SortOrder.DESC));
    }

}
