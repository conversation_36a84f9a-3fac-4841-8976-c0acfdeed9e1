package com.sankuai.shangou.seashop.order.core.thrift.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.enums.TResultCode;
import com.sankuai.shangou.seashop.order.common.config.SystemSwitchProps;
import com.sankuai.shangou.seashop.order.common.enums.SwitchBizTypeEnum;
import com.sankuai.shangou.seashop.order.core.mq.model.order.SellerRemarkBo;
import com.sankuai.shangou.seashop.order.core.service.EsOrderService;
import com.sankuai.shangou.seashop.order.core.service.OrderRefundSearchService;
import com.sankuai.shangou.seashop.order.core.service.OrderService;
import com.sankuai.shangou.seashop.order.core.service.ProductCommentService;
import com.sankuai.shangou.seashop.order.core.service.model.order.BatchDeliverOrderParamBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.CancelOrderBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ConfirmReceiveBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.CreateOrderBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.DelayReceiveBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.DeliverOrderParamBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ErpOrderDetailBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderDeliveryBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderExpressBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.SellerUpdateReceiverBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.UpdateFreightBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.UpdateItemAmountBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.UserUpdateReceiverBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefund;
import com.sankuai.shangou.seashop.order.dao.core.domain.ProductComment;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRefundRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.ProductCommentRepository;
import com.sankuai.shangou.seashop.order.thrift.core.OrderCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.*;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.BatchDeliverOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.ReBuyBySellerReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.SellerRemarkReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.CreateOrderResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.ErpOrderDetailResp;
import com.sankuai.shangou.seashop.pay.core.service.WxShippingBiz;
import com.sankuai.shangou.seashop.pay.thrift.core.request.GetOrderRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "订单命令接口", description = "订单相关命令操作接口")
@RestController
@RequestMapping("/orderCmd")
public class OrderCmdController implements OrderCmdFeign {

    @Resource
    private OrderService orderService;
    @Resource
    private EsOrderService esOrderService;
    @Resource
    private SystemSwitchProps systemSwitchProps;

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private OrderRefundSearchService orderRefundSearchService;

    @Resource
    private OrderRefundRepository orderRefundRepository;

    @Resource
    private ProductCommentRepository productCommentRepository;
    @Resource
    private ProductCommentService productCommentService;
    @Resource
    private WxShippingBiz wxShippingBiz;

    @PostMapping(value = "/createOrder", consumes = "application/json")
    @Operation(summary = "创建订单", description = "创建新订单")
    @Override
    public ResultDto<CreateOrderResp> createOrder(@RequestBody CreateOrderReq orderReq) {
        SystemSwitchProps.SwitchContent switchContent = systemSwitchProps.getSwitch(SwitchBizTypeEnum.ALLOW_CREATE_ORDER.getKey());
        if (switchContent != null && Boolean.FALSE.equals(switchContent.getAllowFlag())) {
            log.info("【订单】创建订单被系统开关禁止，不允许创建订单");
            ResultDto<CreateOrderResp> result = new ResultDto<>();
            String msg = StrUtil.isBlank(switchContent.getForbiddenMessage()) ? SwitchBizTypeEnum.ALLOW_CREATE_ORDER.getDefaultMessage() : switchContent.getForbiddenMessage();
            result.fail(TResultCode.SERVER_ERROR.value(), msg);
            return result;
        }
        return ThriftResponseHelper.responseInvoke("【订单】创建订单", orderReq, func -> {
            // 参数对象转换
            CreateOrderBo createOrderBo = JsonUtil.copy(orderReq, CreateOrderBo.class);
            // 业务逻辑处理
            CreateOrderResp resp = orderService.createOrder(createOrderBo);
            return resp;
        });
    }

    @PostMapping(value = "/cancelPay", consumes = "application/json")
    @Operation(summary = "取消支付", description = "取消订单支付")
    @Override
    public ResultDto<BaseResp> cancelPay(@RequestBody CancelPayReq cancelPayReq) {
        return ThriftResponseHelper.responseInvoke("【订单】取消支付", cancelPayReq, func -> {
            // 业务逻辑处理
            orderService.cancelPay(cancelPayReq.getUserId(), cancelPayReq.getOrderId());
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/cancelOrder", consumes = "application/json")
    @Operation(summary = "取消订单", description = "取消订单操作")
    @Override
    public ResultDto<BaseResp> cancelOrder(@RequestBody CancelOrderReq cancelOrderReq) {
        return ThriftResponseHelper.responseInvoke("【订单】取消订单", cancelOrderReq, func -> {
            // 业务逻辑处理
            CancelOrderBo cancelOrderBo = JsonUtil.copy(cancelOrderReq, CancelOrderBo.class);
            orderService.cancelOrder(cancelOrderBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/reBuy", consumes = "application/json")
    @Operation(summary = "订单重新加购", description = "订单重新加购操作")
    @Override
    public ResultDto<BaseResp> reBuy(@RequestBody ReBuyReq reBuyReq) {
        return ThriftResponseHelper.responseInvoke("【订单】订单重新加购", reBuyReq, func -> {
            // 业务逻辑处理
            orderService.reBuy(reBuyReq.getUserId(), reBuyReq.getOrderId());
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/updateReceiver", consumes = "application/json")
    @Operation(summary = "修改收货人信息", description = "用户修改收货人信息")
    @Override
    public ResultDto<BaseResp> updateReceiver(@RequestBody UpdateReceiverReq updateReceiverReq) {
        return ThriftResponseHelper.responseInvoke("【订单】修改收货人信息", updateReceiverReq, func -> {
            UserUpdateReceiverBo userUpdateReceiverBo = JsonUtil.copy(updateReceiverReq, UserUpdateReceiverBo.class);
            // 业务逻辑处理
            orderService.updateReceiver(userUpdateReceiverBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/updateReceiverBySeller", consumes = "application/json")
    @Operation(summary = "供应商修改收货人信息", description = "供应商修改收货人信息")
    @Override
    public ResultDto<BaseResp> updateReceiverBySeller(@RequestBody SellerUpdateReceiverReq updateReceiverReq) {
        return ThriftResponseHelper.responseInvoke("【订单】供应商修改收货人信息", updateReceiverReq, func -> {
            SellerUpdateReceiverBo updateReceiverBo = JsonUtil.copy(updateReceiverReq, SellerUpdateReceiverBo.class);
            // 业务逻辑处理
            orderService.updateReceiverBySeller(updateReceiverBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/updateItemAmount", consumes = "application/json")
    @Operation(summary = "供应商改价", description = "供应商修改订单商品价格")
    @Override
    public ResultDto<BaseResp> updateItemAmount(@RequestBody UpdateItemAmountReq updateItemAmountReq) {
        return ThriftResponseHelper.responseInvoke("【订单】供应商改价", updateItemAmountReq, func -> {
            updateItemAmountReq.checkParameter();
            UpdateItemAmountBo updateBo = JsonUtil.copy(updateItemAmountReq, UpdateItemAmountBo.class);
            // 业务逻辑处理
            orderService.updateItemAmount(updateBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/updateFreight", consumes = "application/json")
    @Operation(summary = "供应商修改运费", description = "供应商修改订单运费")
    @Override
    public ResultDto<BaseResp> updateFreight(@RequestBody UpdateFreightReq updateFreightReq) {
        return ThriftResponseHelper.responseInvoke("【订单】供应商修改运费", updateFreightReq, func -> {
            updateFreightReq.checkParameter();
            UpdateFreightBo updateBo = JsonUtil.copy(updateFreightReq, UpdateFreightBo.class);
            // 业务逻辑处理
            orderService.updateFreight(updateBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/delayReceive", consumes = "application/json")
    @Operation(summary = "买家延长收货", description = "买家延长订单收货时间")
    @Override
    public ResultDto<BaseResp> delayReceive(@RequestBody DelayReceiveReq delayReceiveReq) {
        return ThriftResponseHelper.responseInvoke("【订单】买家延长收货", delayReceiveReq, func -> {
            DelayReceiveBo updateBo = JsonUtil.copy(delayReceiveReq, DelayReceiveBo.class);
            // 业务逻辑处理
            orderService.delayReceive(updateBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/confirmReceive", consumes = "application/json")
    @Operation(summary = "买家确认收货", description = "买家确认订单收货")
    @Override
    public ResultDto<BaseResp> confirmReceive(@RequestBody ConfirmReceiveReq confirmReceiveReq) {
        return ThriftResponseHelper.responseInvoke("【订单】买家确认收货", confirmReceiveReq, func -> {
            ConfirmReceiveBo confirmReceiveBo = JsonUtil.copy(confirmReceiveReq, ConfirmReceiveBo.class);
            // 业务逻辑处理
            orderService.confirmReceive(confirmReceiveBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/confirmErpReceive", consumes = "application/json")
    @Operation(summary = "ERP确认收货", description = "ERP系统确认订单收货")
    @Override
    public ResultDto<ErpOrderDetailResp> confirmErpReceive(@RequestBody ConfirmReceiveReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】ERP确认收货", req, func -> {
            ConfirmReceiveBo confirmReceiveBo = JsonUtil.copy(func, ConfirmReceiveBo.class);
            // 业务逻辑处理
            ErpOrderDetailBo detailBo = orderService.confirmErpOrderReceive(confirmReceiveBo);
            return JsonUtil.copy(detailBo, ErpOrderDetailResp.class);
        });
    }

    /**
     * 单个订单发货接口，目前页面用的是批量接口，这个接口主要是与ERP对接使用
     *
     * @param deliverOrderReq 入参
     * <AUTHOR>
     */
    @PostMapping(value = "/deliverOrder", consumes = "application/json")
    @Operation(summary = "供应商发货", description = "供应商对单个订单发货")
    @Override
    public ResultDto<BaseResp> deliverOrder(@RequestBody DeliverOrderReq deliverOrderReq) {
        return ThriftResponseHelper.responseInvoke("【订单】供应商发货", deliverOrderReq, func -> {
            deliverOrderReq.checkParameter();
            DeliverOrderParamBo deliveryBo = JsonUtil.copy(deliverOrderReq, DeliverOrderParamBo.class);
            // 业务逻辑处理
            orderService.deliverOrder(deliveryBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/batchDeliverOrder", consumes = "application/json")
    @Operation(summary = "供应商批量发货", description = "供应商批量发货接口")
    @Override
    public ResultDto<BaseResp> batchDeliverOrder(@RequestBody BatchDeliverOrderReq orderDeliveryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】供应商批量发货", orderDeliveryReq, func -> {
            BatchDeliverOrderParamBo deliveryBo = JsonUtil.copy(orderDeliveryReq, BatchDeliverOrderParamBo.class);
            // 业务逻辑处理
            orderService.batchDeliverOrder(deliveryBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/updateExpress", consumes = "application/json")
    @Operation(summary = "供应商修改运单号", description = "供应商修改订单运单号")
    @Override
    public ResultDto<BaseResp> updateExpress(@RequestBody UpdateExpressReq updateExpressReq) {
        return ThriftResponseHelper.responseInvoke("【订单】供应商修改运单号", updateExpressReq, func -> {
            updateExpressReq.checkParameter();
            DeliverOrderParamBo deliveryBo = new DeliverOrderParamBo();
            OrderDeliveryBo wayBill = new OrderDeliveryBo();
            wayBill.setOrderId(updateExpressReq.getOrderId());
            if (CollUtil.isNotEmpty(updateExpressReq.getExpressList())) {
                wayBill.setExpressList(JsonUtil.copyList(updateExpressReq.getExpressList(), OrderExpressBo.class));
            }
            deliveryBo.setDelivery(wayBill);
            deliveryBo.setNeedExpress(updateExpressReq.getNeedExpress());
            deliveryBo.setOperationUserId(updateExpressReq.getOperationUserId());
            deliveryBo.setOperationShopId(updateExpressReq.getOperationShopId());
            // 业务逻辑处理
            orderService.updateExpress(deliveryBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/addExpress", consumes = "application/json")
    @Operation(summary = "新增订单运单信息", description = "新增订单运单信息")
    @Override
    public ResultDto<BaseResp> addExpress(@RequestBody UpdateExpressReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】新增订单运单信息", req, func -> {
            req.checkParameter();
            DeliverOrderParamBo deliveryBo = new DeliverOrderParamBo();
            OrderDeliveryBo wayBill = new OrderDeliveryBo();
            wayBill.setOrderId(req.getOrderId());
            if (CollUtil.isNotEmpty(req.getExpressList())) {
                wayBill.setExpressList(JsonUtil.copyList(req.getExpressList(), OrderExpressBo.class));
            }
            deliveryBo.setDelivery(wayBill);
            deliveryBo.setNeedExpress(req.getNeedExpress());
            deliveryBo.setOperationUserId(req.getOperationUserId());
            deliveryBo.setOperationShopId(req.getOperationShopId());
            // 业务逻辑处理
            orderService.addExpress(deliveryBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/sellerRemark", consumes = "application/json")
    @Operation(summary = "供应商添加备注", description = "供应商为订单添加备注")
    @Override
    public ResultDto<BaseResp> sellerRemark(@RequestBody SellerRemarkReq remarkReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】供应商添加备注", remarkReq, func -> {
            SellerRemarkBo remarkBo = JsonUtil.copy(remarkReq, SellerRemarkBo.class);
            remarkBo.setSellerRemark(remarkReq.getRemark());
            remarkBo.setSellerRemarkFlag(remarkReq.getRemarkFlag());
            // 业务逻辑处理
            orderService.sellerRemark(remarkBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/sellerCancelOrder", consumes = "application/json")
    @Operation(summary = "供应商取消订单", description = "供应商取消订单操作")
    @Override
    public ResultDto<BaseResp> sellerCancelOrder(@RequestBody CancelOrderReq cancelOrderReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】供应商取消订单", cancelOrderReq, func -> {
            // 业务逻辑处理
            CancelOrderBo cancelOrderBo = JsonUtil.copy(cancelOrderReq, CancelOrderBo.class);
            orderService.sellerCancelOrder(cancelOrderBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/reBuyBySeller", consumes = "application/json")
    @Operation(summary = "供应商处理用户订单重新购买", description = "供应商处理用户订单重新购买")
    @Override
    public ResultDto<BaseResp> reBuyBySeller(@RequestBody ReBuyBySellerReq reBuyBySellerReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】供应商处理用户订单重新购买", reBuyBySellerReq, func -> {
            // 业务逻辑处理
            reBuyBySellerReq.checkParameter();
            orderService.reBuyBySeller(reBuyBySellerReq.getOrderId(), reBuyBySellerReq.getShopId());
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/initOrderES", consumes = "application/json")
    @Operation(summary = "初始化订单ES", description = "初始化订单ES数据")
    @Override
    public ResultDto<BaseResp> initOrderES() throws TException {
        return ThriftResponseHelper.responseInvoke("初始化订单ES", null, func -> {
            // 业务逻辑处理
            List<Order> list = orderRepository.list();
            for (Order o : list){
                esOrderService.buildEsOrder(o.getOrderId());
            }
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/initOrderRefundES", consumes = "application/json")
    @Operation(summary = "初始化订单售后ES", description = "初始化订单售后ES数据")
    @Override
    public ResultDto<BaseResp> initOrderRefundES() throws TException {

        return ThriftResponseHelper.responseInvoke("初始化订单售后ES", null, func -> {
            // 业务逻辑处理
            List<OrderRefund> list = orderRefundRepository.list();
            for (OrderRefund o : list){
                orderRefundSearchService.buildEsRefund(o.getId());
            }
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/initProductCommentES", consumes = "application/json")
    @Operation(summary = "初始化商品评价ES", description = "初始化商品评价ES数据")
    @Override
    public ResultDto<BaseResp> initProductCommentES() throws TException {
        return ThriftResponseHelper.responseInvoke("初始化商品评价ES", null, func -> {
            // 业务逻辑处理
            LambdaQueryWrapper<ProductComment> queryWrapper=new LambdaQueryWrapper<>();
            queryWrapper.eq(ProductComment::getDeleted,false);
            List<ProductComment> list = productCommentRepository.list(queryWrapper);
            for (ProductComment o : list){
                productCommentService.buildEsProductComment(o.getProductCommentId());
            }
            return BaseResp.of();
        });
    }

    @GetMapping(value = "/queryWxStatus")
    @Override
    public ResultDto<Boolean> queryWxStatus(String thirdTransactionNo) {
        GetOrderRequest requewt = new GetOrderRequest();
        requewt.setTransactionId(thirdTransactionNo);
        Boolean flag = wxShippingBiz.getOrder(requewt);
        return ResultDto.newWithData(flag);
    }

    @PostMapping(value = "/confirmReceiptWx", consumes = "application/json")
    @Operation(summary = "【微信发货】确认收货", description = "【微信发货】确认收货")
    @Override
    public ResultDto<BaseResp> confirmReceiptWx(ConfirmReceiptWxReq req) {
        return ThriftResponseHelper.responseInvoke("【微信发货】确认收货", req, func -> {
            orderService.confirmReceiptWx(req);
            return BaseResp.of();
        });
    }
}
