package com.sankuai.shangou.seashop.order.core.service.model.order;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class EsOrderItemBo {

    /**
     * 订单明细ID
     */
    private Long itemId;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * sku id
     */
    private String skuId;
    /**
     * sku自增ID
     */
    private Long skuAutoId;
    /**
     * 购买数量
     */
    private Long quantity;
    /**
     * 限时购活动ID，用于限时购下单校验购买限制
     */
    private Long flashSaleId;
    /**
     * 实际应付金额
     */
    private BigDecimal realTotalPrice;

    // 冗余部分订单信息
    private Long userId;
    private String userName;
    private String orderId;
    private Integer orderStatus;
    private Long orderDate;
    private Long shopId;

}
