package com.sankuai.shangou.seashop.order.core.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;

import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.elasticsearch.search.aggregations.metrics.ParsedValueCount;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;

import com.google.common.base.Stopwatch;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.boot.utils.PageUtil;
import com.sankuai.shangou.seashop.base.lock.DistributedLockService;
import com.sankuai.shangou.seashop.base.lock.model.LockKey;
import com.sankuai.shangou.seashop.order.common.config.EsIndexProps;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.common.constant.EsConst;
import com.sankuai.shangou.seashop.order.common.constant.LockConst;
import com.sankuai.shangou.seashop.order.common.enums.OrderCommentStatusEnum;
import com.sankuai.shangou.seashop.order.common.es.EagleService;
import com.sankuai.shangou.seashop.order.common.es.model.EagleQueryResult;
import com.sankuai.shangou.seashop.order.common.remote.CashDepositRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.CustomServiceRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.FreightTemplateRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.MemberRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.ProductRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.SettingRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.base.TradeSiteSettingBo;
import com.sankuai.shangou.seashop.order.common.utils.SettingUtil;
import com.sankuai.shangou.seashop.order.common.utils.SkuUtil;
import com.sankuai.shangou.seashop.order.common.utils.ThreadPoolUtil;
import com.sankuai.shangou.seashop.order.core.service.EsOrderService;
import com.sankuai.shangou.seashop.order.core.service.assit.OrderBizAssist;
import com.sankuai.shangou.seashop.order.core.service.assit.PageQueryAssist;
import com.sankuai.shangou.seashop.order.core.service.model.Counter;
import com.sankuai.shangou.seashop.order.core.service.model.order.EsOrderBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.EsOrderInvoiceBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.EsOrderItemBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderInfoBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderItemInfoBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryOrderBaseBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryPlatformOrderBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QuerySellerOrderBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryUserOrderBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.StatsUserPurchaseSkuParamBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.UserPurchaseSkuBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.UserPurchaseSkuStatsBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.UserPurchaseSkuStatsSummaryBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderComment;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefund;
import com.sankuai.shangou.seashop.order.dao.core.domain.ProductComment;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderCommentRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderInvoiceRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderItemRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRefundRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.ProductCommentRepository;
import com.sankuai.shangou.seashop.order.dao.finance.domain.CashDeposit;
import com.sankuai.shangou.seashop.order.dao.finance.repository.CashDepositRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderPlatformEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderQueryFromEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayMethodEnum;
import com.sankuai.shangou.seashop.order.thrift.core.response.order.CustomServiceDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.SkuFitCategoryCashDepositResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductBasicDto;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberResp;
import com.sankuai.shangou.seashop.user.thrift.shop.dto.QueryFreightTemplateDto;
import com.sankuai.shangou.seashop.user.thrift.shop.response.CustomerServiceResp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class EsOrderServiceImpl extends OrderSearchParamBuilder implements EsOrderService {

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private EagleService eagleService;
    @Resource
    private DistributedLockService distributedLockService;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private OrderInvoiceRepository orderInvoiceRepository;
    @Resource
    private SettingRemoteService settingRemoteService;
    @Resource
    private ProductRemoteService productRemoteService;
    @Resource
    private OrderBizAssist orderBizAssist;
    @Resource
    private ShopRemoteService shopRemoteService;
    @Resource
    private CashDepositRemoteService cashDepositRemoteService;
    // CashDepositRepository 是目前财务和订单在同一个服务，直接注入
    @Resource
    private CashDepositRepository cashDepositRepository;
    @Resource
    private MemberRemoteService memberRemoteService;
    @Resource
    private CustomServiceRemoteService customerServiceRemoteService;
    @Resource
    private ProductCommentRepository productCommentRepository;
    @Resource
    private OrderCommentRepository orderCommentRepository;
    @Resource
    private FreightTemplateRemoteService freightTemplateRemoteService;
    @Resource
    private PageQueryAssist pageQueryAssist;
    @Resource
    private EsIndexProps esIndexProps;

    /**
     * json copy 需要忽略的字段
     */
    private static final String[] IGNORE_FIELDS = new String[]{"orderDate", "finishDate", "payDate"};

    /**
     * 构建订单ES数据，定时任务也是执行这个方法，每次会根据orderId加锁，并且查询最新的数据
     *
     * @param orderId void
     * <AUTHOR>
     */
    @Override
    public void buildEsOrder(String orderId) {
        String key = LockConst.LOCK_ES_ORDER_UPDATE_PATTERN + orderId;
        distributedLockService.tryLock(new LockKey(LockConst.SCENE_ES_ORDER_UPDATE, key), () -> {
            Order order = orderRepository.getByOrderId(orderId);
            List<OrderItem> orderItems = orderItemRepository.getByOrderIdList(Collections.singletonList(orderId));
            List<EsOrderItemBo> items = JsonUtil.copyList(orderItems, EsOrderItemBo.class, (db, es) -> {
                es.setItemId(db.getId());
                es.setOrderId(order.getOrderId());
                es.setUserId(order.getUserId());
                es.setUserName(order.getUserName());
                es.setOrderStatus(order.getOrderStatus());
                es.setOrderDate(order.getOrderDate().getTime());
            });
            // 构建ES存储对象
            Map<String/*docId*/, Map<String/*字段名称*/, Object/*字段值*/>> paramMap = new HashMap<>(2);
            // 构建需要保存的业务数据
            Map<String/*字段名称*/, Object/*字段值*/> doc = buildIndexData(order, items);
            paramMap.put(orderId, doc);
            // 调用ES部分更新
            eagleService.partUpdate(esIndexProps.getIdxOrder(), paramMap);

            // 更新订单明细索引
            Map<String/*docId*/, String> itemDocMap = items.stream()
                    .collect(Collectors.toMap(item -> String.valueOf(item.getItemId()), JsonUtil::toJsonString));
            //List<EsOrderItemBo>
            eagleService.batchUpdate(esIndexProps.getIdxOrderItem(), itemDocMap);
        });
    }

    @Override
    public void updateEsOrderPayData(String orderId, String payId, String tradeNo) {
        String key = LockConst.LOCK_ES_ORDER_UPDATE_PATTERN + orderId;
        distributedLockService.tryLock(new LockKey(LockConst.SCENE_ES_ORDER_UPDATE, key), () -> {
            // 构建ES存储对象
            Map<String/*docId*/, Map<String/*字段名称*/, Object/*字段值*/>> paramMap = new HashMap<>(2);
            Map<String/*字段名称*/, Object/*字段值*/> doc = new HashMap<>(4);
            doc.put("payId", payId);
            doc.put("tradeNo", tradeNo);

            paramMap.put(orderId, doc);
            // 调用ES部分更新
            eagleService.partUpdate(esIndexProps.getIdxOrder(), paramMap);
        });
    }

    /**
     * 商家订单搜索
     * <p>接口和参数构建分角色，这样权限独立，底层实际调用ES可以公用</p>
     *
     * @param searchBo 查询参数
     * <AUTHOR>
     */
    @Override
    public BasePageResp<OrderInfoBo> searchForUser(QueryUserOrderBo searchBo) {
        // 构建搜索请求
        SearchRequest searchRequest = buildUserSearchRequest(searchBo);
        log.info("【订单搜索】商家端搜索条件为: {}", JsonUtil.toJsonString(searchRequest));
        // 查询交易设置
        TradeSiteSettingBo setting = settingRemoteService.getTradeSiteSetting();
        BasePageResp<OrderInfoBo> pageResp = doSearchAndConvertData(searchRequest, searchBo, setting);
        // 填充额外数据
        appendExtDataForUser(pageResp, setting);
        return pageResp;
    }

    /**
     * 卖家订单搜索
     * <p>接口和参数构建分角色，这样权限独立，底层实际调用ES可以公用</p>
     *
     * @param searchBo 查询参数
     * <AUTHOR>
     */
    @Override
    public BasePageResp<OrderInfoBo> searchForSeller(QuerySellerOrderBo searchBo) {
        // 校验分页查询数据量，超过显示提示用户
        pageQueryAssist.checkOrderPageTotal(searchBo);
        // 构建搜索请求
        SearchRequest searchRequest = buildSellerSearchRequest(searchBo);
        log.info("【订单搜索】卖家端搜索条件为: {}", JsonUtil.toJsonString(searchRequest));
        // 约定：条件为null，代表入参条件没有符合的数据
        if (searchRequest == null) {
            return PageResultHelper.defaultEmpty(searchBo);
        }
        // 查询交易设置
        TradeSiteSettingBo setting = settingRemoteService.getTradeSiteSetting();
        return doSearchAndConvertData(searchRequest, searchBo, setting);
    }

    /**
     * 平台订单搜索
     * <p>接口和参数构建分角色，这样权限独立，底层实际调用ES可以公用</p>
     *
     * @param searchBo 查询参数
     * <AUTHOR>
     */
    @Override
    public BasePageResp<OrderInfoBo> searchForPlatform(QueryPlatformOrderBo searchBo) {
        // 校验分页查询数据量，超过显示提示用户
        pageQueryAssist.checkOrderPageTotal(searchBo);
        // 构建搜索请求
        SearchRequest searchRequest = buildPlatformSearchRequest(searchBo);
        log.info("【订单搜索】平台端搜索条件为: {}", JsonUtil.toJsonString(searchRequest));
        // 约定：条件为null，代表入参条件没有符合的数据
        if (searchRequest == null) {
            return PageResultHelper.defaultEmpty(searchBo);
        }
        // 查询交易设置
        TradeSiteSettingBo setting = settingRemoteService.getTradeSiteSetting();
        BasePageResp<OrderInfoBo> pageResp = doSearchAndConvertData(searchRequest, searchBo, setting);
        // 平台端需要对商家的手机号码脱敏
        if (pageResp != null && CollUtil.isNotEmpty(pageResp.getData())) {
            pageResp.getData().stream()
                    .filter(data -> StrUtil.isNotBlank(data.getUserPhone()))
                    .forEach(data -> data.setUserPhone(DesensitizedUtil.mobilePhone(data.getUserPhone())));
        }
        return pageResp;
    }

    @Override
    public Long countFlashSaleByProduct(Long userId, Long flashSaleId, Long productId) {
        // 构建查询请求
        SearchRequest searchRequest = new SearchRequest(esIndexProps.getIdxOrder());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("userId", userId))
                .mustNot(QueryBuilders.termQuery("orderStatus", OrderStatusEnum.CLOSED.getCode()));

        BoolQueryBuilder nestedQuery = QueryBuilders.boolQuery();
        nestedQuery.must(QueryBuilders.termQuery("orderItems.productId", productId))
                .must(QueryBuilders.termQuery("orderItems.flashSaleId", flashSaleId));
        boolQueryBuilder.must(QueryBuilders.nestedQuery("orderItems", nestedQuery,
                org.apache.lucene.search.join.ScoreMode.None));

        sourceBuilder.query(boolQueryBuilder);

        searchRequest.source(sourceBuilder);

        // 执行查询
        EagleQueryResult searchResult = eagleService.queryByCondition(searchRequest);

        // 处理查询结果
        return searchResult.getTotalHit();
    }

    @Override
    public Long countFlashSaleBySku(Long userId, Long flashSaleId, String skuId) {
        // 构建查询请求
        SearchRequest searchRequest = new SearchRequest(esIndexProps.getIdxOrder());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(QueryBuilders.matchQuery("user_id", userId));

        searchRequest.source(sourceBuilder);

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("userId", userId))
                .must(QueryBuilders.termQuery("orderItems.skuId", skuId))
                .must(QueryBuilders.termQuery("orderItems.flashSaleId", flashSaleId));

        // 执行查询
        EagleQueryResult searchResult = eagleService.queryByCondition(searchRequest);

        // 处理查询结果
        return searchResult.getTotalHit();
    }

    /**
     * 基于订单+sku维度平铺数据分页
     *
     * @param searchBo
     * <AUTHOR>
     */
    @Override
    public UserPurchaseSkuStatsBo pageUserPurchaseSku(StatsUserPurchaseSkuParamBo searchBo) {
        // 构建搜索请求，先分页搜索明细，订单信息后续根据明细结果二次查询
        SearchRequest searchRequest = buildStatsUserPurchaseSkuRequest(searchBo);
        log.info("【订单搜索】商家采购统计搜索条件为: {}", JsonUtil.toJsonString(searchRequest));
        // 调用ES进行查询和聚合
        EagleQueryResult searchResult = eagleService.queryByCondition(searchRequest);
        log.info("【订单搜索】商家采购统计搜索搜索结果为: {}", JsonUtil.toJsonString(searchResult));
        // 解析搜索结果
        UserPurchaseSkuStatsBo resultBo = resolveUserPurchaseSkuStats(searchResult, searchBo);
        log.info("【订单搜索】商家采购统计搜索订单结果: {}", JsonUtil.toJsonString(resultBo));
        // 填充其他数据，二次查询订单ES获取订单状态信息，查询明细获取SKU信息
        appendUserPurchaseSkuExtInfo(resultBo);
        return resultBo;
    }

    private BasePageResp<OrderInfoBo> doSearchAndConvertData(SearchRequest searchRequest, QueryOrderBaseBo searchBo, TradeSiteSettingBo setting) {
        // 调用ES进行查询和聚合
        EagleQueryResult searchResult = eagleService.queryByCondition(searchRequest);
        log.debug("【订单搜索】入参为: {}, 搜索结果为: {}", JsonUtil.toJsonString(searchBo), JsonUtil.toJsonString(searchResult));
        // 解析搜索结果
        BasePageResp<EsOrderBo> resultBo = resolveSearchResult(searchResult, searchBo);
        log.debug("【订单搜索】入参为: {}, 订单结果: {}", JsonUtil.toJsonString(searchBo), JsonUtil.toJsonString(resultBo));
        // 根据查询结果填充相关数据
        if (CollUtil.isEmpty(resultBo.getData())) {
            return PageResultHelper.defaultEmpty(searchBo);
        }
        return fillOrderInfo(resultBo, setting);
    }

    @Override
    public List<String> getOrderIdByProductFromItem(String productNameOrId) {
        // 构建查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // 商家端的商品搜索，从订单明细中找到订单再到售后表搜索
        if (StrUtil.isNotBlank(productNameOrId)) {
            // 如果不是long类型，则不匹配商品ID，否则会报错
            boolean isLong = NumberUtil.isLong(productNameOrId);
            if (isLong) {
                // 商品ID精确匹配
                boolQueryBuilder.must(QueryBuilders.termQuery("productId", productNameOrId))
                        // 商品名称模糊匹配
                        .should(QueryBuilders.wildcardQuery("productName", eagleService.appendWildcard(productNameOrId)));
            } else {
                // 商品名称模糊匹配
                boolQueryBuilder.must(QueryBuilders.wildcardQuery("productName", eagleService.appendWildcard(productNameOrId)));
            }
        }
        // 构建聚合，订单号去重聚合
        AggregationBuilder aggregationBuilder = AggregationBuilders.terms("orderIdAgg")
                .field("orderId")
                .size(EsConst.DEFAULT_AGG_SIZE);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(boolQueryBuilder)
                .size(0);
        // 设置聚合
        sourceBuilder.aggregation(aggregationBuilder);
        // 创建搜索请求
        SearchRequest searchRequest = new SearchRequest(esIndexProps.getIdxOrderItem());
        searchRequest.source(sourceBuilder);
        // 调用ES进行查询和聚合
        EagleQueryResult searchResult = eagleService.queryByCondition(searchRequest);
        // 解析聚合结果，去除订单号
        if (searchResult.getAggregations() != null) {
            Terms terms = searchResult.getAggregations().get("orderIdAgg");
            if (terms != null) {
                return terms.getBuckets().stream().map(Terms.Bucket::getKeyAsString).collect(Collectors.toList());
            }
        }
        return null;
    }


    //************************************************************

    private Map<String/*字段名称*/, Object/*字段值*/> buildIndexData(Order order, List<EsOrderItemBo> orderItems) {
        String orderId = order.getOrderId();
        EsOrderBo esOrderBo = JsonUtil.copy(order, EsOrderBo.class, IGNORE_FIELDS);
        if (order.getOrderDate() != null) {
            esOrderBo.setOrderDate(order.getOrderDate().getTime());
        }
        if (order.getFinishDate() != null) {
            esOrderBo.setFinishDate(order.getFinishDate().getTime());
        }
        if (order.getPayDate() != null) {
            esOrderBo.setPayDate(order.getPayDate().getTime());
        }
        esOrderBo.setOrderItems(orderItems);
        esOrderBo.setOrderInvoice(JsonUtil.copy(orderInvoiceRepository.getByOrderId(orderId), EsOrderInvoiceBo.class));
        Map<String/*字段名称*/, Object/*字段值*/> doc = JsonUtil.beanToMap(esOrderBo);
        log.info("【订单索引构建】订单ID为: {}, 索引内容为: {}", orderId, JsonUtil.toJsonString(doc));
        return doc;
    }

    /**
     * 构建商家查询订单的请求参数
     *
     * @param searchBo 请求参数
     * <AUTHOR>
     */
    private SearchRequest buildUserSearchRequest(QueryUserOrderBo searchBo) {
        // 构建查询条件
        BoolQueryBuilder boolQueryBuilder = buildUserSearchCondition(searchBo);
        // 构建排序
        List<SortBuilder<FieldSortBuilder>> sortBuilders = buildFieldSortList();
        return buildSortAndPageAndCreateRequest(searchBo, boolQueryBuilder, sortBuilders, Collections.emptyList());
    }

    /**
     * 构建卖家查询订单的请求参数
     *
     * @param searchBo 请求参数
     * <AUTHOR>
     */
    private SearchRequest buildSellerSearchRequest(QuerySellerOrderBo searchBo) {
        // 构建查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("shopId", searchBo.getShopId()));
        BoolQueryBuilder condBuilder = buildCommonSearchCondition(boolQueryBuilder, searchBo);
        // 约定：条件为null，代表入参条件没有符合的数据
        if (condBuilder == null) {
            return null;
        }
        // 构建排序
        List<SortBuilder<FieldSortBuilder>> sortBuilders = buildFieldSortList();
        return buildSortAndPageAndCreateRequest(searchBo, boolQueryBuilder, sortBuilders, Collections.emptyList());
    }

    /**
     * 构建卖家查询订单的请求参数
     *
     * @param searchBo 请求参数
     * <AUTHOR>
     */
    private SearchRequest buildPlatformSearchRequest(QueryPlatformOrderBo searchBo) {
        // 构建查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // 从店铺ES获取店铺ID
        if (StrUtil.isNotBlank(searchBo.getShopName())) {
            List<Long> shopIdList = shopRemoteService.searchShopIdByNameFromEs(searchBo.getShopName());
            searchBo.setShopIdList(shopIdList);
        }
        BoolQueryBuilder condBuilder = buildCommonSearchCondition(boolQueryBuilder, searchBo);
        // 约定：条件为null，代表入参条件没有符合的数据
        if (condBuilder == null) {
            return null;
        }
        if (StrUtil.isNotBlank(searchBo.getPayId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("payId", searchBo.getPayId()));
        }
        if (StrUtil.isNotBlank(searchBo.getTradeNo())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("tradeNo", searchBo.getTradeNo()));
        }
        if (!CollectionUtil.isEmpty(searchBo.getOrderIdList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("orderId", searchBo.getOrderIdList()));
        }
        if (!CollectionUtil.isEmpty(searchBo.getOrderStatusList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("orderStatus", searchBo.getOrderStatusList()));
        }
        if (searchBo.getUserId() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("userId", searchBo.getUserId()));
        }
        // 构建排序
        List<SortBuilder<FieldSortBuilder>> sortBuilders = buildFieldSortList();
        return buildSortAndPageAndCreateRequest(searchBo, boolQueryBuilder, sortBuilders, Collections.emptyList());
    }

    private SearchRequest buildSortAndPageAndCreateRequest(BasePageReq searchBo,
                                                           BoolQueryBuilder boolQueryBuilder,
                                                           List<SortBuilder<FieldSortBuilder>> sortBuilders,
                                                           List<AggregationBuilder> aggregationBuilders) {
        // 整合查询条件
        int from = PageUtil.getStart(searchBo.getPageNo(), searchBo.getPageSize());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(boolQueryBuilder)
                .from(from)
                .size(searchBo.getPageSize())
                .trackTotalHits(true);
        // 设置排序
        if (CollUtil.isNotEmpty(sortBuilders)) {
            sortBuilders.forEach(sourceBuilder::sort);
        }
        // 设置聚合
        if (CollUtil.isNotEmpty(aggregationBuilders)) {
            aggregationBuilders.forEach(sourceBuilder::aggregation);
        }
        // 创建搜索请求
        SearchRequest searchRequest = new SearchRequest(esIndexProps.getIdxOrder());
        searchRequest.source(sourceBuilder);
        return searchRequest;
    }


    /**
     * 解析搜索结果
     *
     * @param searchResult ES结果
     * <AUTHOR>
     */
    private BasePageResp<EsOrderBo> resolveSearchResult(EagleQueryResult searchResult, QueryOrderBaseBo searchBo) {
        if (searchResult.getTotalHit() != null && searchResult.getTotalHit() == 0) {
            BasePageResp<EsOrderBo> basePageResp = new BasePageResp<>();
            basePageResp.setPageNo(searchBo.getPageNo());
            basePageResp.setPageSize(searchBo.getPageSize());
            basePageResp.setData(Collections.emptyList());
            basePageResp.setTotalCount(0L);
            basePageResp.setPages(0);
            return basePageResp;
        }
        // 解析商品列表
        List<EsOrderBo> productList = searchResult.getHits().stream()
                .map(hit -> JsonUtil.parseObject(hit, EsOrderBo.class))
                .collect(Collectors.toList());
        // 构造分页结果
        int totalHit = searchResult.getTotalHit().intValue();
        BasePageResp<EsOrderBo> basePageResp = new BasePageResp<>();
        basePageResp.setData(productList);
        basePageResp.setPages(PageUtil.totalPage(totalHit, searchBo.getPageSize()));
        basePageResp.setTotalCount(searchResult.getTotalHit());
        basePageResp.setPageNo(searchBo.getPageNo());
        basePageResp.setPageSize(searchBo.getPageSize());
        return basePageResp;
    }


    private BasePageResp<OrderInfoBo> fillOrderInfo(BasePageResp<EsOrderBo> resultBo, TradeSiteSettingBo setting) {
        List<String> orderIdList = new ArrayList<>(10);
        Set<Long> userIdSet = new HashSet<>(10);
        resultBo.getData().forEach(esOrderBo -> {
            orderIdList.add(esOrderBo.getOrderId());
            userIdSet.add(esOrderBo.getUserId());
        });
        List<Order> orderList = orderRepository.getByOrderIdList(orderIdList);
        Map<String, Order> orderMap = orderList.stream()
            .collect(Collectors.toMap(Order::getOrderId, order -> order, (oldV, newV) -> newV));
        // 获取订单的售后信息
        List<OrderItem> orderItemList = orderItemRepository.getByOrderIdList(orderIdList);
        List<OrderRefund> refundList = orderRefundRepository.getByOrderIdList(orderIdList);
        Map<String, List<OrderRefund>> refundGroupOrderMap = refundList.stream()
            .collect(Collectors.groupingBy(OrderRefund::getOrderId));
        // 获取用户信息
        Map<Long, MemberResp> userMap = memberRemoteService.getMemberByUserIdListMap(new ArrayList<>(userIdSet));
        int refundCloseDays = SettingUtil.getIntValueOrDefault("售后维权期天数", setting.getSalesReturnTimeout(), CommonConst.DEFAULT_REFUND_CLOSE_DAYS);
        Date now = new Date();
        Date lastFinishDate = DateUtil.offsetDay(now, -refundCloseDays);
        return PageResultHelper.transfer(resultBo, OrderInfoBo.class, orderIno -> {
            // 设置订单相关信息
            Order dbOrder = orderMap.get(orderIno.getOrderId());
            if (dbOrder != null) {
                orderIno.setPlatform(dbOrder.getPlatform());
                orderIno.setPlatformDesc(OrderPlatformEnum.getDesc(dbOrder.getPlatform()));
                orderIno.setPayDate(dbOrder.getPayDate());
                orderIno.setFinishDate(dbOrder.getFinishDate());
                orderIno.setPayment(dbOrder.getPayment());
                orderIno.setPaymentDesc(PayMethodEnum.getDesc(dbOrder.getPayment()));
                orderIno.setGatewayOrderId(dbOrder.getGatewayOrderId());
                orderIno.setOrderStatus(dbOrder.getOrderStatus());
                orderIno.setOrderStatusDesc(OrderStatusEnum.getDesc(dbOrder.getOrderStatus()));
                orderIno.setSellerRemark(dbOrder.getSellerRemark());
                orderIno.setSellerRemarkFlag(dbOrder.getSellerRemarkFlag());
                orderIno.setActualPayAmount(dbOrder.getActualPayAmount());
                orderIno.setOrderType(dbOrder.getOrderType());
                orderIno.setOrderTypeDesc(OrderTypeEnum.getDesc(orderIno.getOrderType()));
                //orderIno.setOrderSource(dbOrder.getOrderSource());
                //orderIno.setOrderSourceDesc(OrderSourceEnum.getDesc(orderIno.getOrderSource()));
                orderIno.setCellPhone(dbOrder.getCellPhone());
                orderIno.setReceiveDelay(dbOrder.getReceiveDelay());
                // 设置默认值然后重置
                orderIno.setHasOverAfterSales(false);
                if (dbOrder.getFinishDate() != null && refundCloseDays >= 0) {
                    orderIno.setHasOverAfterSales(DateUtil.compare(dbOrder.getFinishDate(), lastFinishDate) < 0);
                }
                MemberResp member = userMap.get(dbOrder.getUserId());
                if (member != null) {
                    orderIno.setUserPhone(member.getCellPhone());
                    orderIno.setNick(member.getNick());
                }
                orderBizAssist.appendRemainPayTimeIfNecessary(dbOrder, orderIno, setting, now);
                orderBizAssist.appendEncryptShipInfoIfNecessary(dbOrder, orderIno, setting, now, OrderQueryFromEnum.SELLER_PC);
            } else {
                // ES有订单，数据库没有，理论不会存在，如果有报错出发报警
                log.error("【订单搜索】订单ID为: {} 的订单信息为空", orderIno.getOrderId());
            }

            // 处理订单售后
            List<OrderRefund> orderRefundList = refundGroupOrderMap.get(orderIno.getOrderId());
            // 设置订单维度的售后信息
            boolean continueItem = orderBizAssist.appendOrderRefundInfoAndReturnContinueItem(orderIno, orderRefundList);
            Map<Long, List<OrderRefund>> itemRefundMap = orderBizAssist.getItemRefund(orderRefundList);

            // 转换并设置订单商品信息
            Counter counter = new Counter();
            List<OrderItemInfoBo> orderItemInfoBoList = orderItemList.stream()
                .filter(orderItem -> StrUtil.equals(orderItem.getOrderId(), orderIno.getOrderId()))
                .map(orderItem -> {
                    OrderItemInfoBo bo = JsonUtil.copy(orderItem, OrderItemInfoBo.class);
                    bo.setOrderItemId(orderItem.getId());
                    bo.setMainImagePath(orderItem.getThumbnailsUrl());
                    bo.setOriginTotalAmount(NumberUtil.mul(orderItem.getSalePrice(), orderItem.getQuantity()));
                    String skuName = SkuUtil.assembleSkuName(orderItem.getColor(), orderItem.getSize(), orderItem.getVersion());
                    bo.setSkuName(skuName);
                    // 如果没有订单级别的售后才需要进一步处理明细级别的
                    orderBizAssist.appendOrderItemRefundInfo(orderIno, bo, itemRefundMap.get(orderItem.getId()), continueItem);
                    counter.increment(orderItem.getQuantity());
                    return bo;
                })
                // 列表页面，明细最多展示20个，这里没考虑查询条件有商品信息的
                .limit(CommonConst.DEFAULT_ORDER_ITEM_SIZE_DISPLAY)
                .collect(Collectors.toList());
            orderIno.setItemList(orderItemInfoBoList);
            orderIno.setProductQuantity(counter.getCount());

        });
    }

    /**
     * 用户的采购统计需要返回订单+sku维度，从明细ES查询
     *
     * @param searchBo org.elasticsearch.action.search.SearchRequest
     * <AUTHOR>
     */
    private SearchRequest buildStatsUserPurchaseSkuRequest(StatsUserPurchaseSkuParamBo searchBo) {
        // 构建查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // 用户条件必填
        boolQueryBuilder.filter(QueryBuilders.termQuery("userId", searchBo.getUserId()));
        // 业务上，这里固定查询已支付的订单
        boolQueryBuilder.mustNot(QueryBuilders.termsQuery("orderStatus",
                Arrays.asList(OrderStatusEnum.CLOSED.getCode(), OrderStatusEnum.PAYING.getCode(), OrderStatusEnum.UNDER_PAY.getCode())));
        if (StrUtil.isNotBlank(searchBo.getOrderId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("orderId", searchBo.getOrderId()));
        }
        // 商品名称使用wildcard实现模糊匹配，wildcard基于通配符实现
        if (StrUtil.isNotBlank(searchBo.getProductName())) {
            String productName = eagleService.appendWildcard(searchBo.getProductName());
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("productName", productName));
        }
        if (StrUtil.isNotBlank(searchBo.getSkuId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("skuId", searchBo.getSkuId()));
        }
        if (searchBo.getSkuAutoId() != null) {
            log.info("skuAutoId: {}", searchBo.getSkuAutoId());
            boolQueryBuilder.filter(QueryBuilders.termQuery("skuAutoId", searchBo.getSkuAutoId()));
        }
        if (searchBo.getProductId() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("productId", searchBo.getProductId()));
        }
        // 下单时间参数
        appendOrderDateParam(boolQueryBuilder, searchBo.getOrderStartTime(), searchBo.getOrderEndTime());


        // 整合查询条件
        int from = PageUtil.getStart(searchBo.getPageNo(), searchBo.getPageSize());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(boolQueryBuilder)
                .from(from)
                .size(searchBo.getPageSize());
        // 构建聚合查询
        List<AggregationBuilder> aggregationBuilders = new ArrayList<>();

        // 聚合sku
        // 符合条件的商品总数
        AggregationBuilder itemQuantityAgg = AggregationBuilders.sum("quantitySum").field("quantity");
        aggregationBuilders.add(itemQuantityAgg);
        // 符合条件的商品总金额
        AggregationBuilder itemAmountAgg = AggregationBuilders.sum("totalProductAmount").field("realTotalPrice");
        aggregationBuilders.add(itemAmountAgg);
        // 聚合用户订单数量：订单数量（售后导致订单关闭不扣减）
        AggregationBuilder orderCountAgg = AggregationBuilders.count("totalOrderCount").field("orderId");
        aggregationBuilders.add(orderCountAgg);

        // 设置排序
        List<SortBuilder<FieldSortBuilder>> sortBuilders = new ArrayList<>(1);
        // 默认根据ID倒序
        sortBuilders.add(SortBuilders.fieldSort("itemId").order(SortOrder.DESC));
        if (CollUtil.isNotEmpty(sortBuilders)) {
            sortBuilders.forEach(sourceBuilder::sort);
        }
        // 设置聚合
        if (CollUtil.isNotEmpty(aggregationBuilders)) {
            aggregationBuilders.forEach(sourceBuilder::aggregation);
        }
        // 创建搜索请求
        SearchRequest searchRequest = new SearchRequest(esIndexProps.getIdxOrderItem());
        searchRequest.source(sourceBuilder);

        // 使用聚合实现明细的平铺
        return searchRequest;
    }

    private UserPurchaseSkuStatsBo resolveUserPurchaseSkuStats(EagleQueryResult searchResult, StatsUserPurchaseSkuParamBo searchBo) {
        // 定义默认返回值
        BasePageResp<UserPurchaseSkuBo> pageData = PageResultHelper.defaultEmpty(searchBo);
        UserPurchaseSkuStatsBo statsBo = UserPurchaseSkuStatsBo.defaultEmpty(pageData);
        if (searchResult.getTotalHit() != null && searchResult.getTotalHit() == 0) {
            return statsBo;
        }
        BasePageResp<EsOrderItemBo> esPage = resolveItemSearchResult(searchResult, searchBo);
        BasePageResp<UserPurchaseSkuBo> itemPage = PageResultHelper.transfer(esPage, UserPurchaseSkuBo.class);
        statsBo.setPageData(itemPage);

        UserPurchaseSkuStatsSummaryBo summaryBo = statsBo.getSummary();
        // 解析订单总数
        ParsedValueCount totalOrderCount = searchResult.getAggregations().get("totalOrderCount");
        if (totalOrderCount != null) {
            summaryBo.setOrderCount(totalOrderCount.getValue());
        }
        // 解析采购sku总数量
        ParsedSum quantitySumAgg = searchResult.getAggregations().get("quantitySum");
        if (quantitySumAgg != null) {
            // 数量直接取整
            Long quantitySum = new BigDecimal(quantitySumAgg.getValueAsString()).longValue();
            summaryBo.setSkuQuantitySum(quantitySum);
        }
        // 解析订单总金额
        ParsedSum totalOrderAmount = searchResult.getAggregations().get("totalProductAmount");
        if (totalOrderAmount != null) {
            BigDecimal value = new BigDecimal(totalOrderAmount.getValueAsString());
            // 保留两位有效数字
            value = value.setScale(2, RoundingMode.HALF_UP);
            summaryBo.setTotalAmount(value);
        }

        statsBo.setSummary(summaryBo);

        return statsBo;
    }

    /**
     * 解析搜索结果
     *
     * @param searchResult ES结果
     * <AUTHOR>
     */
    private BasePageResp<EsOrderItemBo> resolveItemSearchResult(EagleQueryResult searchResult, BasePageReq searchBo) {
        // 解析商品列表
        List<EsOrderItemBo> productList = searchResult.getHits().stream()
                .map(hit -> JsonUtil.parseObject(hit, EsOrderItemBo.class))
                .collect(Collectors.toList());
        // 构造分页结果
        int totalHit = searchResult.getTotalHit().intValue();
        BasePageResp<EsOrderItemBo> basePageResp = new BasePageResp<>();
        basePageResp.setData(productList);
        basePageResp.setPages(PageUtil.totalPage(totalHit, searchBo.getPageSize()));
        basePageResp.setTotalCount(searchResult.getTotalHit());
        basePageResp.setPageNo(searchBo.getPageNo());
        basePageResp.setPageSize(searchBo.getPageSize());
        return basePageResp;
    }

    private void appendUserPurchaseSkuExtInfo(UserPurchaseSkuStatsBo resultBo) {
        // 前置处理，分页对象不会为空，这里没有考虑为空的情况，直接取值
        List<UserPurchaseSkuBo> dataList = resultBo.getPageData().getData();
        if (CollUtil.isEmpty(dataList)) {
            return;
        }
        // ES 分页聚合，只返回了itemId，所以需要查询订单明细和订单数据
        List<Long> itemIdList = dataList.stream().map(UserPurchaseSkuBo::getItemId).collect(Collectors.toList());
        List<OrderItem> itemList = orderItemRepository.listByIds(itemIdList);

        List<String> orderIdList = itemList.stream().map(OrderItem::getOrderId).collect(Collectors.toList());
        List<Order> orderList = orderRepository.getByOrderIdList(orderIdList);

        Map<String, Order> orderMap = orderList.stream().collect(Collectors.toMap(Order::getOrderId, order -> order, (oldV, newV) -> newV));
        Map<Long, OrderItem> itemMap = itemList.stream().collect(Collectors.toMap(OrderItem::getId, item -> item, (oldV, newV) -> newV));

        dataList.forEach(data -> {
            // 需要先设置明细，因为要设设置orderId
            OrderItem item = itemMap.get(data.getItemId());
            if (item != null) {
                data.setOrderId(item.getOrderId());
                data.setProductName(item.getProductName());
                data.setProductId(item.getProductId());
                data.setSkuId(item.getSkuId());
                data.setQuantity(item.getQuantity());
                data.setRealTotalPrice(item.getRealTotalPrice());
                List<String> skuDescList = Stream.of(item.getColor(), item.getSize(), item.getVersion())
                        .filter(StrUtil::isNotBlank)
                        .collect(Collectors.toList());
                data.setSkuDescList(skuDescList);
            }
            if (orderMap.containsKey(data.getOrderId())) {
                Order order = orderMap.get(data.getOrderId());
                OrderStatusEnum status = OrderStatusEnum.valueOf(order.getOrderStatus());
                if (status != null) {
                    data.setOrderStatus(status.getCode());
                    data.setOrderStatusDesc(status.getDesc());
                }
            }

        });
    }

    /**
     * 订单的额外信息包括：客服入口、保证金标识、7天无理由标识、立即发货标识、评价入口
     * 以上数据多线程获取设置，且允许降级，异常不影响订单列表返回
     *
     * @param orderPage 订单列表
     * @param setting   交易设置
     *                  void
     * <AUTHOR>
     */
    private void appendExtDataForUser(BasePageResp<OrderInfoBo> orderPage, TradeSiteSettingBo setting) {
        if (orderPage == null || CollUtil.isEmpty(orderPage.getData())) {
            return;
        }
        int size = orderPage.getPageSize();
        Set<Long> shopIdSet = new HashSet<>(size);
        Set<String> skuIdSet = new HashSet<>(size);
        List<String> orderIdList = new ArrayList<>(size);
        Set<Long> productIdSet = new HashSet<>(size);
        for (OrderInfoBo order : orderPage.getData()) {
            shopIdSet.add(order.getShopId());
            orderIdList.add(order.getOrderId());
            for (OrderItemInfoBo item : order.getItemList()) {
                skuIdSet.add(item.getSkuId());
                productIdSet.add(item.getProductId());
            }
        }
        List<Long> shopIdList = new ArrayList<>(shopIdSet);
        List<Long> productIdList = new ArrayList<>(productIdSet);

        // 多线程获取数据
        Stopwatch stopwatch = Stopwatch.createStarted();

//        ThreadPool threadPool = ThreadPoolUtil.ASYNC_API_POOL;
        ThreadPoolExecutor executor = ThreadPoolUtil.ASYNC_API_POOL;
        // 保证金相关，用户设置保障标识
        CompletableFuture<Map<String, SkuFitCategoryCashDepositResp>> skuCateMapFuture = CompletableFuture.supplyAsync(() -> this.getSkuCateDeposit(new ArrayList<>(skuIdSet)), executor);
        CompletableFuture<Map<Long, CashDeposit>> shopDepositMapFuture = CompletableFuture.supplyAsync(() -> this.getShopDeposit(shopIdList), executor);
        // 获取运费模板，用于设置立即发货(闪电)标识
        CompletableFuture<Map<Long, ProductBasicDto>> productFuture = CompletableFuture.supplyAsync(() -> this.getProductIgnoreException(productIdList), executor);
        CompletableFuture<Map<Long, QueryFreightTemplateDto>> freightTplFuture = productFuture.thenApplyAsync(productMap -> this.getFreightTplIgnoreException(getTemplateIdList(productMap)));
        // 调用客服接口，用于设置客服标识
        CompletableFuture<Map<Long, List<CustomerServiceResp>>> customerServiceMapFuture = CompletableFuture.supplyAsync(() -> customerServiceRemoteService.getByShopToMap(shopIdList), executor);
        // 获取订单评价信息
        CompletableFuture<Map<String, List<ProductComment>>> commentMapFuture = CompletableFuture.supplyAsync(() -> this.getOrderProductComment(orderIdList), executor);
        CompletableFuture<Map<String, OrderComment>> orderCommentMapFuture = CompletableFuture.supplyAsync(() -> this.getOrderComment(orderIdList), executor);

        // 当所有异步调用都完成时，执行业务逻辑
        try {
            // 等待所有异步调用完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(skuCateMapFuture, shopDepositMapFuture, freightTplFuture,
                    customerServiceMapFuture, commentMapFuture, orderCommentMapFuture);
            allFutures.get();
            // 收集所有结果
            Map<String, SkuFitCategoryCashDepositResp> skuCateMap = skuCateMapFuture.get();
            Map<Long, CashDeposit> shopDepositMap = shopDepositMapFuture.get();
            Map<Long, ProductBasicDto> productMap = productFuture.get();
            Map<Long, QueryFreightTemplateDto> freightTplMap = freightTplFuture.get();
            Map<Long, List<CustomerServiceResp>> customerServiceMap = customerServiceMapFuture.get();
            Map<String, List<ProductComment>> commentMap = commentMapFuture.get();
            Map<String, OrderComment> orderCommentMap = orderCommentMapFuture.get();


            stopwatch.stop();
            log.info("获取订单额外数据和标识耗时：{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));
            // 处理业务逻辑
            for (OrderInfoBo order : orderPage.getData()) {
                // 保证金是店铺维度，但是页面显示是在商品
                CashDeposit cashDeposit = shopDepositMap.get(order.getShopId());
                Boolean showGuaranteeFlag = cashDeposit != null && cashDeposit.getTotalBalance().compareTo(BigDecimal.ZERO) > 0;
                for (OrderItemInfoBo item : order.getItemList()) {
                    SkuFitCategoryCashDepositResp skuCate = skuCateMap.get(item.getSkuId());
                    // 新加逻辑判断是否缴纳了足够保证金
                    Boolean enableNoReasonReturn = skuCate != null && skuCate.getCategoryCashDepositResp() != null
                            && Boolean.TRUE.equals(skuCate.getCategoryCashDepositResp().getEnableNoReasonReturn());
                    item.setEnableNoReasonReturn(enableNoReasonReturn);

                    item.setShowGuaranteeFlag(showGuaranteeFlag);

                    // 从运费模板判断立即发货标识
                    ProductBasicDto product = productMap.get(item.getProductId());
                    if (product == null) {
                        item.setShowThunderFlag(false);
                    } else {
                        QueryFreightTemplateDto freightTpl = freightTplMap.get(product.getFreightTemplateId());
                        boolean showThunderFlag = freightTpl != null && CommonConst.FLAG_DELIVER_IMMEDIATELY.equals(freightTpl.getSendTime());
                        item.setShowThunderFlag(showThunderFlag);
                    }
                }
                // 处理客服
                List<CustomerServiceResp> serviceList = customerServiceMap.get(order.getShopId());
                if (CollUtil.isNotEmpty(serviceList)) {
                    order.setCustomServiceList(JsonUtil.copyList(serviceList, CustomServiceDto.class));
                }
                // 处理评价
                OrderCommentStatusEnum commentStatusEnum = getOrderCommentStatus(order, orderCommentMap.get(order.getOrderId()), commentMap.get(order.getOrderId()), setting);
                order.setCommentStatus(commentStatusEnum.getCode());
                order.setCommentStatusDesc(commentStatusEnum.getDesc());
            }
        } catch (Exception e) {
            log.error("填充订单额外数据和标识异常", e);
        }
    }

    /**
     * 获取SKU对应的类目保证金配置。异常不影响主流程
     */
    private Map<String, SkuFitCategoryCashDepositResp> getSkuCateDeposit(List<String> skuIdList) {
        try {
            return cashDepositRemoteService.getSkuFitCateConfigMap(skuIdList);
        } catch (Exception e) {
            log.error("【订单搜索】获取SKU保证金配置异常", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 获取店铺保证金。异常不影响主流程
     */
    private Map<Long, CashDeposit> getShopDeposit(List<Long> shopIdList) {
        if (CollUtil.isEmpty(shopIdList)) {
            return Collections.emptyMap();
        }
        try {
            List<CashDeposit> depositList = cashDepositRepository.queryByShopIdList(shopIdList);
            return depositList.stream().collect(Collectors.toMap(CashDeposit::getShopId, deposit -> deposit, (oldV, newV) -> newV));
        } catch (Exception e) {
            log.error("【订单搜索】获取店铺保证金异常", e);
            return Collections.emptyMap();
        }
    }

    private Map<String, OrderComment> getOrderComment(List<String> orderIdList) {
        List<OrderComment> commentList = orderCommentRepository.getByOrderIdList(orderIdList);
        if (CollUtil.isEmpty(commentList)) {
            return Collections.emptyMap();
        }
        return commentList.stream().collect(Collectors.toMap(OrderComment::getOrderId, Function.identity(), (oldV, newV) -> newV));
    }

    /**
     * 获取订单商品评价状态。
     * 评价会针对商品维度，目前数据保存是批量同时保存的，所以取订单商品评价的任意一条数据进行判断，因为时间是一样的
     *
     * @param orderIdList 订单号
     * <AUTHOR>
     */
    private Map<String, List<ProductComment>> getOrderProductComment(List<String> orderIdList) {
        List<ProductComment> commentList = productCommentRepository.listByOrderIdList(orderIdList);
        if (CollUtil.isEmpty(commentList)) {
            return Collections.emptyMap();
        }
        // 根据订单号分组后取第一条评论
        return commentList.stream()
                .collect(Collectors.groupingBy(ProductComment::getOrderId));
    }

    private OrderCommentStatusEnum getOrderCommentStatus(OrderInfoBo order, OrderComment orderComment, List<ProductComment> validProdCommentList, TradeSiteSettingBo setting) {
        // 订单已完成才能评价
        if (!OrderStatusEnum.FINISHED.getCode().equals(order.getOrderStatus())) {
            return OrderCommentStatusEnum.NOT_COMMENTABLE;
        }
        int commentTimeoutDays = SettingUtil.getIntValueOrDefault("订单完成后允许评价天数", setting.getOrderCommentTimeout(), CommonConst.DEFAULT_COMMENT_TIMEOUT_DAYS);
        // 如果订单完成超过了评价期限，就不能评价了
        Date timeoutDate = DateUtil.offsetDay(order.getFinishDate(), commentTimeoutDays);
        boolean commentClosed = order.getFinishDate() != null && DateUtil.compare(timeoutDate, new Date()) < 0;
        if (orderComment == null && commentClosed) {
            return OrderCommentStatusEnum.NOT_COMMENTABLE;
        }
        // 订单评论表存在数据，商品评论表也一定会存在，但可能是被删除的；而商品评论表有记录，无论是否删除，订单评论表都有记录，所以只需判断订单评论
        if (orderComment == null) {
            return OrderCommentStatusEnum.NOT_COMMENT;
        }
        // 此时订单评论有记录，如果订单评论被删除，或者商品评论都被删除(list为空)，则不可评论，因为被删除了
        if (Boolean.TRUE.equals(orderComment.getDeleted()) || CollUtil.isEmpty(validProdCommentList)) {
            return OrderCommentStatusEnum.NOT_COMMENTABLE;
        }
        ProductComment anyValid = validProdCommentList.get(0);
        // 优先看有没有追评
        if (anyValid.getAppendDate() != null) {
            return OrderCommentStatusEnum.ADDITIONAL_COMMENT;
        }
        if (anyValid.getReviewDate() != null) {
            return OrderCommentStatusEnum.COMMENTED;
        }
        return OrderCommentStatusEnum.NOT_COMMENT;
    }


    private Map<Long, ProductBasicDto> getProductIgnoreException(List<Long> productIdList) {
        try {
            List<ProductBasicDto> productList = productRemoteService.queryProductList(productIdList);
            if (CollUtil.isNotEmpty(productIdList)) {
                return productList.stream().collect(Collectors.toMap(ProductBasicDto::getProductId, Function.identity(), (o1, o2) -> o2));
            }
            return Collections.emptyMap();
        } catch (Exception e) {
            log.error("获取商品信息异常", e);
            return Collections.emptyMap();
        }
    }

    private List<Long> getTemplateIdList(Map<Long, ProductBasicDto> productMap) {
        if (MapUtil.isEmpty(productMap)) {
            return null;
        }
        return productMap.values().stream().map(ProductBasicDto::getFreightTemplateId).distinct().collect(Collectors.toList());
    }

    private Map<Long, QueryFreightTemplateDto> getFreightTplIgnoreException(List<Long> tplIdList) {
        try {
            return freightTemplateRemoteService.getFreightTplByIdListMap(tplIdList);
        } catch (Exception e) {
            log.error("获取运费模板异常", e);
            return Collections.emptyMap();
        }
    }

}
