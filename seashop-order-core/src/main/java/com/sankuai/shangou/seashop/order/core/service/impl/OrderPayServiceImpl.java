package com.sankuai.shangou.seashop.order.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Throwables;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.exception.LockFailException;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.lock.DistributedLockService;
import com.sankuai.shangou.seashop.base.lock.model.LockKey;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.common.constant.LockConst;
import com.sankuai.shangou.seashop.order.common.remote.PayRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.SettingRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.base.TradeSiteSettingBo;
import com.sankuai.shangou.seashop.order.common.remote.model.pay.AdaPayCreateExpandBo;
import com.sankuai.shangou.seashop.order.common.remote.model.pay.CreatePayBo;
import com.sankuai.shangou.seashop.order.core.mq.model.pay.PayResultBo;
import com.sankuai.shangou.seashop.order.core.service.OrderPayService;
import com.sankuai.shangou.seashop.order.core.service.assit.BizNoGenerator;
import com.sankuai.shangou.seashop.order.core.service.assit.pay.OrderPayResultHandlerAssist;
import com.sankuai.shangou.seashop.order.core.service.model.UserBo;
import com.sankuai.shangou.seashop.order.core.service.model.pay.InitiatePayBo;
import com.sankuai.shangou.seashop.order.core.service.model.pay.PcOrderPayInfoBo;
import com.sankuai.shangou.seashop.order.core.service.model.pay.PcPrePayOrderInfoBo;
import com.sankuai.shangou.seashop.order.core.service.model.pay.QueryOrderPayBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPayRecord;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderPayRecordRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.dao.finance.domain.SettlementConfig;
import com.sankuai.shangou.seashop.order.dao.finance.repository.SettlementConfigRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayMethodEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayPlatformEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderUserReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryPayChannelReq;
import com.sankuai.shangou.seashop.pay.core.assist.pay.util.PaymentChannelUtil;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.BusinessTypeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PayStateEnums;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentChannelEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.response.OrderPayResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.PayPaymentCreateResp;
import com.sankuai.shangou.seashop.user.thrift.account.ShopMemberQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryMemberReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class OrderPayServiceImpl implements OrderPayService {

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderPayRecordRepository orderPayRecordRepository;
    @Resource
    private PayRemoteService payRemoteService;
    @Resource
    private SettingRemoteService settingRemoteService;
    @Resource
    private DistributedLockService distributedLockService;
    @Resource
    private BizNoGenerator bizNoGenerator;
    @Resource
    private SettlementConfigRepository settlementConfigRepository;
    @Resource
    private OrderPayResultHandlerAssist orderPayResultHandlerAssist;
    @Resource
    private PaymentChannelUtil paymentChannelUtil;
    @Resource
    private ShopMemberQueryFeign shopMemberQueryFeign;

    /**
     * 获取PC端订单支付参数，包括基本订单信息和支付渠道
     *
     * @param queryOrderPay 获取订单支付参数的请求入参
     * <AUTHOR>
     */
    @Override
    public PcOrderPayInfoBo queryPcOrderPayInfo(QueryOrderPayBo queryOrderPay) {
        List<Order> orderList = orderRepository.getByOrderIdListForceMaster(queryOrderPay.getOrderIdList());
        log.info("获取订单支付方式，当前订单信息:{}", JsonUtil.toJsonString(orderList));
        if (CollUtil.isEmpty(orderList)) {
            throw new BusinessException("订单不存在");
        }
        List<String> orderIdList = new ArrayList<>(orderList.size());
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (Order order : orderList) {
            orderIdList.add(order.getOrderId());
            totalAmount = totalAmount.add(order.getTotalAmount());
        }
        // 校验订单是否可以支付
        validateOrderCanPay(queryOrderPay.getUserId(), orderList, orderIdList);

        PcOrderPayInfoBo pcOrderPayInfoBo = new PcOrderPayInfoBo();

        TradeSiteSettingBo setting = settingRemoteService.getTradeSiteSetting();
        PcPrePayOrderInfoBo orderInfoBo = new PcPrePayOrderInfoBo();
        orderInfoBo.setOrderIdList(orderIdList);
        orderInfoBo.setOrderAmount(totalAmount);
        // 获取支付配置
        int payTimeoutHour = NumberUtil.parseInt(setting.getUnpaidTimeout(), CommonConst.DEFAULT_PAY_TIMEOUT_HOUR);
        log.info("【支付】获取支付配置，支付超时时间：{}小时", payTimeoutHour);
        orderInfoBo.setRemainPayHour(payTimeoutHour);

        // 提取订单集合里面下单时间最早的时间
        Date orderDate = orderList.stream().map(Order::getOrderDate).min(Comparator.naturalOrder()).get();
        // 计算剩余支付时间(秒) 下单时间 + 支付超时时间 - 当前时间
        Long remainPayTime = (DateUtil.offsetHour(orderDate, payTimeoutHour).getTime() - new Date().getTime()) / 1000;
        orderInfoBo.setRemainPayTime(remainPayTime);

        pcOrderPayInfoBo.setOrderInfo(orderInfoBo);
        // 获取支付配置，获取超过多少金额，显示企业网银支付渠道
        String showEnterprisePayAmountStr = StrUtil.blankToDefault(setting.getCompanyBankOrderAmount(), CommonConst.DEFAULT_AMOUNT_STR_SHOW_COMPANY_BANK);
        BigDecimal showEnterprisePayAmount = new BigDecimal(showEnterprisePayAmountStr);
        log.info("【支付】获取支付配置，显示企业网银支付渠道金额：{}", showEnterprisePayAmountStr);
        List<PayMethodEnum> channelList = paymentChannelUtil.getEnablePayMethods(PayPlatformEnum.PC);
        // 如果订单金额没有达到企业网银的条件，则不返回企业网银支付渠道
        if (totalAmount.compareTo(showEnterprisePayAmount) < 0) {
            channelList.remove(PayMethodEnum.COMPANY_BANK);
        }
        pcOrderPayInfoBo.setPayMethodList(channelList);
        return pcOrderPayInfoBo;
    }

    /**
     * 发起支付，支持多笔订单合并支付
     * <p>发起支付的过程是，用户已经选择好了支付方式(小程序默认微信支付)，然后发起支付</p>
     * <p>订单服务需要保存 多笔订单合并支付的映射，实际付款时多笔订单是一笔支付单，为了保证回调的时候知道处理哪些订单，所以需要维护映射</p>
     * <p>考虑可能一个账号多人登录，同时支付的情况，用分布式锁处理</p>
     * <p>因为这里是多笔订单的，所以状态变化没有使用状态机</p>
     *
     * @param initiatePayBo 发起支付的请求参数
     * <AUTHOR>
     */
    @Override
    public String initiatePayment(InitiatePayBo initiatePayBo) {
        // 使用分布式锁，防止同时对同一笔订单发起支付
        String batchNo = bizNoGenerator.generatePayNo();
        log.info("【支付】发起支付，批次号：{}", batchNo);
        DateTime now = DateUtil.date();
        try {
            // 主动推断支付渠道
            PaymentChannelEnum paymentChannel = paymentChannelUtil.autoMatchPaymentChannel(initiatePayBo.getPayMethod());
            initiatePayBo.setPaymentChannel(paymentChannel.getCode());
            // 当支付方式为 H5 和 商城的时候 需要取用户的微信公众号OpenId
            if(initiatePayBo.getPayMethod().equals(PayMethodEnum.WECHAT_H5.getCode()) || initiatePayBo.getPayMethod().equals(PayMethodEnum.WECHAT_JS.getCode())) {
                // 查询用户信息 获取真实姓名
                try {
                    QueryMemberReq req = new QueryMemberReq();
                    req.setId(initiatePayBo.getUserId());
                    MemberResp memberResp = ThriftResponseHelper.executeThriftCall(() -> shopMemberQueryFeign.queryMember(req));
                    if (memberResp != null) {
                        initiatePayBo.setOpenId(memberResp.getWxmpOpenId());
                    } else {
                        throw new BusinessException("用户信息不存在");
                    }
                }
                catch (Exception e) {
                    log.error("[支付]查询用户信息失败, userId: {}", initiatePayBo.getUserId(), e);
                    throw new BusinessException(e.getMessage());
                }
            }
            CreatePayBo createPayBo = transOrderStatusAndBuildPayParam(initiatePayBo, batchNo);
            // 调用远程接口发起支付调用。可能前面订单状态已经改了，但是发起支付失败，依赖于用于取消支付和后续的超时取消
            PayPaymentCreateResp createResp = payRemoteService.createPay(createPayBo);
            /*if (createResp == null || StrUtil.isBlank(createResp.getChannelPayId())) {
                throw new BusinessException("发起支付失败，请稍后重试");
            }*/
            // 将汇付的交易ID保存到支付映射记录表，没放到大事务里面
            orderPayRecordRepository.updatePayNoByBatchNo(batchNo, createResp.getChannelPayId());
            Map<String, String> payParam = createResp.getExpend();
            // 获取支付超时时间
            String payTimeoutHour = getPayTimeoutHour();
            payParam.put("payTimeStart", DateUtil.format(now, DatePattern.NORM_DATETIME_PATTERN));
            payParam.put("payTimeOut", String.valueOf(payTimeoutHour));
            return JsonUtil.toJsonString(payParam);
        }
        catch (LockFailException e) {
            log.warn("发起支付失败，锁已被占用", e);
            throw new BusinessException("订单已发起支付，请勿重复支付");
        }
        catch (BusinessException e) {
            log.warn("发起支付失败", e);
            throw e;
        }
        catch (Throwable e) {
            log.error("发起支付失败", e);
            throw new BusinessException("发起支付失败，请稍后重试");
        }
    }

    /**
     * 确认支付结果，这个是用户在点击完成支付按钮时，为了防止减少支付回调慢导致的延迟，此时查一遍支付结果并对应处理，
     * 所以一般的错误和数据问题可以直接忽略
     * <AUTHOR>
     * @param queryReq
     * void
     */
    @Override
    public Boolean confirmPayResult(QueryPayChannelReq queryReq) {
        List<String> orderIdList = queryReq.getOrderIdList();
        log.info("【确认支付结果】订单ID：{}", orderIdList);
        // 锁的粒度统一用orderId，虽然可能同一笔订单同时发起单笔支付和合并支付，但锁到订单维度也可以，还能保证后续的锁的级别一致
        List<LockKey> lockKeyList = orderIdList.stream()
                .map(orderId -> new LockKey(LockConst.SCENE_ORDER_STATUS_CHANGE, orderId))
                .collect(Collectors.toList());
        log.info("【确认支付结果】获取分布式锁：{}", JsonUtil.toJsonString(lockKeyList));
        try {
            distributedLockService.tryMultiReentrantLock(lockKeyList, () -> {
                List<Order> orderList = orderRepository.getByOrderIdListForceMaster(orderIdList);
                if (CollUtil.isEmpty(orderList)) {
                    log.warn("【确认支付结果】当前订单不存在，订单ID：{}", orderIdList);
                    return;
                }
                boolean anyNotUser = orderList.stream().anyMatch(order -> !order.getUserId().equals(queryReq.getUserId()));
                // 有订单不属于用户报错提示
                if (anyNotUser) {
                    throw new BusinessException("数据异常，有订单不属于当前用户");
                }
                // 既然是确认支付结果，所以需要确认的是支付中的，因为需要查询最新的数据，所以放到锁里面
                // 之所以订单号可能查到多条支付记录，是因为可能某些情况下，同一个订单不同时间发起过多笔支付。
                // 比如拉起支付后，已经生成支付二维码了，取消支付，再次拉起支付，这个时候两个二维码都可以支付，有可能有两个支付记录的结果，这种情况都能支付成功，只不过会有一笔异常订单
                List<OrderPayRecord> payRecordList = orderPayRecordRepository.getByOrderIdListAndPayStatusForceMaster(orderIdList, Collections.singletonList(PayStatusEnum.PAYING.getCode()));
                log.info("【确认支付结果】对应的支付记录:{}", JsonUtil.toJsonString(payRecordList));
                if (CollUtil.isEmpty(payRecordList)) {
                    log.warn("【确认支付结果】当前订单没有支付中的支付记录，订单ID：{}", orderIdList);
                    return;
                }
                List<String> batchNoList = payRecordList.stream().map(OrderPayRecord::getBatchNo).collect(Collectors.toList());
                Map<String, OrderPayResp> payResultMap = payRemoteService.queryPayResult(batchNoList);
                if (MapUtil.isEmpty(payResultMap)) {
                    log.warn("【确认支付结果】支付结果查询失败，订单ID：{}", orderIdList);
                    return;
                }
                payResultMap.forEach((batchNo, orderPayResp) -> {
                    if (orderPayResp == null || PayStateEnums.UNPAID.getStatus().equals(orderPayResp.getPayState())) {
                        log.warn("【确认支付结果】支付中的支付记录没有查询到支付信息或还是支付中,batchNo={}", batchNo);
                        return;
                    }
                    // 此时，支付记录有支付结果，则需要处理支付结果
                    // 对于支付失败的，orderPayResultHandlerAssist 会将订单设置成 待付款
                    PayResultBo payResultBo = JsonUtil.copy(orderPayResp, PayResultBo.class);
                    payResultBo.setErrorMsg(orderPayResp.getChannelPayMsg());
                    payResultBo.setPayStatus(orderPayResp.getPayState());
                    orderPayResultHandlerAssist.handlePayResult(payResultBo);
                });
            });

            // 反查一下订单 只需要要任何一个已经支付成功则返回true
            List<Order> orderList = orderRepository.getByOrderIdListForceMaster(orderIdList);
            return orderList.stream().anyMatch(order -> order.getPayDate() != null);
        } catch (LockFailException e) {
            log.warn("【确认支付结果】获取分布式锁失败", e);
            throw new BusinessException("服务器繁忙, 请稍后再试~");
        } catch (BusinessException e) {
            log.warn("【确认支付结果】支付结果查询异常", e);
            throw e;
        } catch (Exception e) {
            log.error("【确认支付结果】支付结果查询异常", e);
            throw new BusinessException("系统异常");
        }
    }

    @Override
    public List<Long> queryPayOrderUserId(QueryOrderUserReq queryReq) {
        return orderRepository.queryPayOrderUserId(queryReq.getPayDate(), queryReq.getTargetUserIds());
    }

    //************************************************************


    /**
     * 变更订单状态并构建支付参数
     * <AUTHOR>
     * @param initiatePayBo 需要发起支付的参数
	 * @param batchNo 当前支付批次号
     */
    private CreatePayBo transOrderStatusAndBuildPayParam(InitiatePayBo initiatePayBo, String batchNo) throws Throwable {
        List<String> orderIdList = initiatePayBo.getOrderIdList();
        log.info("【支付】发起支付，订单ID：{}", orderIdList);
        // 锁的粒度统一用orderId，虽然可能同一笔订单同时发起单笔支付和合并支付，但锁到订单维度也可以，还能保证后续的锁的级别一致
        List<LockKey> lockKeyList = orderIdList.stream()
                .map(orderId -> new LockKey(LockConst.SCENE_ORDER_STATUS_CHANGE, orderId))
                .collect(Collectors.toList());
        log.info("【支付】发起支付，获取分布式锁：{}", JsonUtil.toJsonString(lockKeyList));
        return distributedLockService.tryMultiReentrantLock(lockKeyList, () -> {
            List<Order> orderList = orderRepository.getByOrderIdListForceMaster(initiatePayBo.getOrderIdList());
            log.info("【支付】发起支付，订单信息：{}", JsonUtil.toJsonString(orderList));
            if (orderIdList.size() != orderList.size()) {
                throw new BusinessException("有订单不存在");
            }
            // 基本校验
            validateOrderCanPay(initiatePayBo.getUserId(), orderList, orderIdList);
            // 单独事务控制
            TransactionHelper.doInTransaction(() -> {
                // 保存订单支付记录
                saveOrderPayRecord(orderList, batchNo, initiatePayBo);
                // 修改订单状态为支付中，这里因为涉及批量，所以没有采用状态机处理
                BigDecimal settleFeeRate = null;
                if (PaymentChannelEnum.ADAPAY.getCode().equals(initiatePayBo.getPaymentChannel())) {
                    // 汇付才需要手续费
                    // 获取结算手续费比例，创建订单时默认设置为0，因为要区分支付方式，每次发起支付时重置
                    SettlementConfig settlementConfig = settlementConfigRepository.getConfig();
                    log.info("【支付】发起支付，获取结算手续费比例：{}", JsonUtil.toJsonString(settlementConfig));
                    settleFeeRate = settlementConfig.getSettlementFeeRate();
                    if (PayMethodEnum.WECHAT_APPLET.getCode().equals(initiatePayBo.getPayMethod()) ||
                            PayMethodEnum.WECHAT_H5.getCode().equals(initiatePayBo.getPayMethod())) {
                        settleFeeRate = settlementConfig.getWxFeeRate();
                    }
                    log.info("【支付】发起支付，获取结算手续费比例：{}", settleFeeRate);
                    // 费率是百分数，需要除以100保存
                    settleFeeRate = settleFeeRate.divide(CommonConst.HUNDRED, 4, RoundingMode.HALF_UP);

                }
                // 过滤掉0元订单，这些直接就是待付款了，不需要支付
                List<String> needUpdateOrderIdList = orderList.stream()
                        // 非0元订单，且是待付款的需要修改状态
                        .filter(order -> BigDecimal.ZERO.compareTo(order.getTotalAmount()) != 0 && OrderStatusEnum.UNDER_PAY.getCode().equals(order.getOrderStatus()))
                        .map(Order::getOrderId)
                        .collect(Collectors.toList());
                log.info("【支付】发起支付，需要修改订单状态的订单ID：{}", JsonUtil.toJsonString(needUpdateOrderIdList));
                int updateCnt = orderRepository.updateOrderPaying(needUpdateOrderIdList, OrderStatusEnum.UNDER_PAY.getCode(), OrderStatusEnum.PAYING.getCode(), settleFeeRate);
                /*if (updateCnt != needUpdateOrderIdList.size()) {
                    throw new BusinessException("存在不可支付的订单");
                }*/
            });
            // 构建支付接口的参数
            return buildOrderPayParam(batchNo, initiatePayBo, orderList);
        });
    }


    public String getPayTimeoutHour() {
        try {
            TradeSiteSettingBo tradeSiteSetting = settingRemoteService.getTradeSiteSetting();
            return tradeSiteSetting.getUnpaidTimeout();
        }
        catch (Throwable e) {
            log.warn("[PAY] get pay setting error:{}", Throwables.getStackTraceAsString(e));
        }
        return "2";
    }


    private void validateOrderCanPay(Long userId, List<Order> orderList, List<String> orderIdList) {
        if (CollectionUtils.isEmpty(orderList)) {
            throw new BusinessException("订单不存在");
        }
        boolean anyNotSuit = orderList.stream()
                // 校验 订单与当前用户不匹配 或者 订单状态不是待支付（0元订单不考虑）
                .anyMatch(order -> !order.getUserId().equals(userId) ||
                        // 非0元订单，但状态不是待付款
                        (!OrderStatusEnum.UNDER_PAY.getCode().equals(order.getOrderStatus()) &&
                                BigDecimal.ZERO.compareTo(order.getTotalAmount()) != 0)
                );
        log.info("【支付】发起支付，订单是否可支付：{}", anyNotSuit);
        if (anyNotSuit) {
            throw new BusinessException("存在不可支付的订单");
        }
        // 获取订单支付中和支付成功的，如果有不允许支付，对于支付中的，让用户取消支付后再发起支付
        List<OrderPayRecord> payList = orderPayRecordRepository.getByOrderIdListAndPayStatusForceMaster(orderIdList,
                Arrays.asList(PayStatusEnum.PAY_SUCCESS.getCode(), PayStatusEnum.PAYING.getCode()));
        // 按订单号分组，聚合数量
        Map<String, Long> orderPayCountMap = payList.stream()
                .collect(Collectors.groupingBy(OrderPayRecord::getOrderId, Collectors.counting()));
        log.info("【支付】发起支付，获取支付中和支付成功支付记录：{}", JsonUtil.toJsonString(orderPayCountMap));
        // 因为.net没有支付中，历史数据不能完全判断支付中抛出异常，所以加上订单是支付中状态的才抛出异常
        // 对于历史数据，订单是待付款，又有支付记录是支付中的，看定时任务等拿结果补偿处理
        /*boolean anyOrderPayingAndExistsPayRecord = orderList.stream()
                .anyMatch(order -> OrderStatusEnum.PAYING.getCode().equals(order.getOrderStatus()) && orderPayCountMap.getOrDefault(order.getOrderId(), 0L) > 0);
        log.info("【支付】发起支付，是否存在订单是支付中但是存在支付中和成功的支付记录：{}", anyOrderPayingAndExistsPayRecord);
        if (anyOrderPayingAndExistsPayRecord) {
            throw new BusinessException("存在不可支付的订单");
        }*/
    }

    private void saveOrderPayRecord(List<Order> orderList, String batchNo, InitiatePayBo initiatePayBo) {
        // 保存支付单
        Date now = new Date();
        List<OrderPayRecord> recordList = orderList.stream()
                .map(order -> {
                    OrderPayRecord record = new OrderPayRecord();
                    record.setOrderId(order.getOrderId());
                    record.setBatchNo(batchNo);
                    record.setOrderAmount(order.getTotalAmount());
                    record.setPayChannel(initiatePayBo.getPaymentChannel());
                    record.setPayMethod(initiatePayBo.getPayMethod());
                    record.setPayStatus(PayStatusEnum.PAYING.getCode());
                    record.setCreateTime(now);
                    record.setUpdateTime(now);
                    return record;
                })
                .collect(Collectors.toList());
        orderPayRecordRepository.saveBatch(recordList);
    }

    private CreatePayBo buildOrderPayParam(String batchNo, InitiatePayBo initiatePayBo, List<Order> orderList) {
        // 调用支付服务的接口放到最后面，防止支付服务挂了，订单服务已经修改了订单状态，导致订单状态不一致，但不妨到锁里面，减小锁粒度
        CreatePayBo createPayBo = new CreatePayBo();
        // 支付服务这里的orderId实际是支付批次单号，从.net迁移没有改字段名
        // 订单服务多笔订单对应一个支付单，支付服务支付单与汇付有映射
        createPayBo.setOrderId(batchNo);
        createPayBo.setPaymentType(initiatePayBo.getPayMethod());
        createPayBo.setBusinessType(BusinessTypeEnum.ORDER.getType());
        createPayBo.setGoodsTitle(batchNo);
        createPayBo.setGoodsDesc(batchNo);
        createPayBo.setDeviceIp(initiatePayBo.getClientIp());
        createPayBo.setPaymentChannel(initiatePayBo.getPaymentChannel());
        AdaPayCreateExpandBo expandBo = new AdaPayCreateExpandBo();
        expandBo.setOpenId(initiatePayBo.getOpenId());
        expandBo.setAcctIssrId(initiatePayBo.getBankCode());
        expandBo.setClientIp(initiatePayBo.getClientIp());
        expandBo.setCallbackUrl(initiatePayBo.getCallbackUrl());
        createPayBo.setExpend(expandBo);

        BigDecimal allAmount = orderList.stream()
                .map(Order::getActualPayAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        createPayBo.setPayAmount(allAmount);

        if (StrUtil.isEmpty(createPayBo.getDeviceIp())) {
            createPayBo.setDeviceIp(getLocalIp());
            AdaPayCreateExpandBo expend = createPayBo.getExpend();
            if (expend != null && StrUtil.isEmpty(expend.getClientIp())) {
                expend.setClientIp(getLocalIp());
            }
        }
        return createPayBo;
    }

    private String getLocalIp() {
        try {
            InetAddress inetAddress = InetAddress.getLocalHost();
            return inetAddress.getHostAddress();
        } catch (UnknownHostException e) {
            log.warn("获取本机IP失败", e);
            return "127.0.0.1";
        }
    }

}
