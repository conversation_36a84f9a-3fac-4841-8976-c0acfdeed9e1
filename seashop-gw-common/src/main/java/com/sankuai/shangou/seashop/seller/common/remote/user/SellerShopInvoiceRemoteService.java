package com.sankuai.shangou.seashop.seller.common.remote.user;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopInvoiceCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopInvoiceQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopInvoiceReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveShopInvoiceReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopInvoiceResp;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/15 11:49
 */
@Service
public class SellerShopInvoiceRemoteService {

    @Resource
    private ShopInvoiceCmdFeign shopInvoiceCmdFeign;

    @Resource
    private ShopInvoiceQueryFeign shopInvoiceQueryFeign;

    public BaseResp saveShopInvoice(SaveShopInvoiceReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> shopInvoiceCmdFeign.saveShopInvoice(req));
    }

    public QueryShopInvoiceResp queryShopInvoice(QueryShopInvoiceReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> shopInvoiceQueryFeign.queryShopInvoice(req));

    }
}
