package com.sankuai.shangou.seashop.seller.common.remote.product;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.hishop.starter.storage.client.StorageClient;
import com.sankuai.shangou.seashop.base.boot.response.BaseImportResp;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign;
import com.sankuai.shangou.seashop.product.thrift.core.ProductQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.SkuQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.common.EsScrollClearReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.*;
import com.sankuai.shangou.seashop.product.thrift.core.request.sku.SkuQueryReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.specification.SpecValueDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.*;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.LadderPriceDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductBasicDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductDetailDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductSkuMergeDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.sku.SkuListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.sku.SkuQueryResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.specification.SpecificationResp;
import com.sankuai.shangou.seashop.seller.common.constant.SellerConstant;
import com.sankuai.shangou.seashop.seller.common.convert.SellerProductConvert;
import com.sankuai.shangou.seashop.seller.common.remote.promotion.SellerPromotionRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiProductDetailResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiSpecificationResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.dto.ApiProductDetailDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/01/02 18:17
 */
@Service
public class SellerProductRemoteService {

    @Resource
    private ProductQueryFeign productQueryFeign;
    @Resource
    private SkuQueryFeign skuQueryFeign;
    @Resource
    private ProductCmdFeign productCmdFeign;
    @Resource
    private SellerPromotionRemoteService sellerPromotionRemoteService;
    @Resource
    private SellerProductConvert SellerProductConvert;

    @Resource
    private SpecificationRemote specificationRemote;
    @Resource
    private StorageClient storageClient;

    /**
     * 查询商品列表
     *
     * @param request 查询条件
     * @return 商品列表
     */
    public BasePageResp<ProductPageResp> queryProduct(QueryProductReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.queryProduct(request));
    }

    public ProductListResp queryProductById(QueryProductByIdReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.queryProductById(request));
    }

    public ApiProductDetailResp queryProductDetail(ProductQueryDetailReq request) {
        ProductDetailResp resp = ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.queryProductDetail(request));
        ProductDetailDto product = resp.getResult();

        ApiProductDetailDto apiProduct = SellerProductConvert.convertApiProductDetailDto(product);
        appendAllSpecValues(apiProduct);

        // 查询商品参加了哪些营销活动
        apiProduct.setProductPromotion(sellerPromotionRemoteService.queryProductPromotionDto(product.getProductId(), product.getShopId()));
        return ApiProductDetailResp.builder().result(apiProduct).build();
    }

    private void appendAllSpecValues(ApiProductDetailDto product) {
        List<ApiSpecificationResp> specSelectList = product.getSpecSelectList();
        if (CollUtil.isEmpty(specSelectList)) {
            return;
        }

        List<Long> nameIds = specSelectList.stream().map(item -> item.getId()).collect(Collectors.toList());
        List<SpecificationResp> specList = specificationRemote.listByNameIds(nameIds);
        Map<Long, SpecificationResp> specMap = specList.stream()
                .collect(Collectors.toMap(SpecificationResp::getId, Function.identity(), (k1, k2) -> k1));

        specSelectList.forEach(spec -> {
            List<SpecValueDto> allValues = JsonUtil.copyList(spec.getValues(), SpecValueDto.class, (source, target) -> {
                target.setShowPic(null);
            });
            allValues = Optional.ofNullable(allValues).orElse(Collections.emptyList());
            List<Long> valueIds = allValues.stream().map(SpecValueDto::getId).collect(Collectors.toList());

            SpecificationResp templateSpec = specMap.get(spec.getId());
            if (templateSpec != null && CollUtil.isNotEmpty(templateSpec.getValues())) {
                allValues.addAll(templateSpec.getValues().stream().filter(item -> !valueIds.contains(item.getId())).collect(Collectors.toList()));
            }
            spec.setAllValues(allValues);
        });
    }

    /**
     * 根据商品id 查询sku信息
     *
     * @param productIds 商品id
     * @return sku信息
     */
    public List<SkuQueryResp> querySku(List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.EMPTY_LIST;
        }

        List<SkuQueryResp> skuList = new ArrayList<>();
        List<List<Long>> productIdsArr = Lists.partition(productIds, SellerConstant.QUERY_LIMIT);
        productIdsArr.forEach(subProductIds -> {
            SkuQueryReq request = new SkuQueryReq();
            request.setProductIds(subProductIds);
            SkuListResp skuListResp = ThriftResponseHelper.executeThriftCall(() -> skuQueryFeign.querySkuList(request));
            if (skuListResp != null && CollectionUtils.isNotEmpty(skuListResp.getSkuList())) {
                skuList.addAll(skuListResp.getSkuList());
            }
        });
        return skuList;
    }

    /**
     * 根据商品id 查询阶梯价格
     *
     * @param productIds 商品id
     * @return 阶梯价格
     */
    public List<LadderPriceDto> queryLadderPrice(List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.EMPTY_LIST;
        }

        List<LadderPriceDto> ladderPriceList = new ArrayList<>();
        List<List<Long>> productIdsArr = Lists.partition(productIds, SellerConstant.QUERY_LIMIT);
        productIdsArr.forEach(subProductIds -> {
            QueryLadderPriceReq request = new QueryLadderPriceReq();
            request.setProductIds(subProductIds);
            ProductLadderPriceResp resp = ThriftResponseHelper
                .executeThriftCall(() -> productQueryFeign.getLadderPriceBoList(request));
            if (resp != null && CollectionUtils.isNotEmpty(resp.getLadderPriceList())) {
                ladderPriceList.addAll(resp.getLadderPriceList());
            }
        });
        return ladderPriceList;
    }

    /**
     * 查询SKU维度的商品集合
     *
     * @param request
     * @return
     */
    public ProductSkuMergeQueryResp queryProductSkuMerge(ProductSkuQueryReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.queryProductSkuMerge(request));
    }

    public List<ProductSkuMergeDto> queryProductSkuMergeBySkuAutoIds(List<Long> skuAutoIds) {
        if (CollectionUtils.isEmpty(skuAutoIds)) {
            return Collections.emptyList();
        }

        List<List<Long>> skuAutoIdsArr = Lists.partition(skuAutoIds, SellerConstant.QUERY_LIMIT);
        List<ProductSkuMergeDto> productSkuList = Collections.synchronizedList(new ArrayList<>());
        skuAutoIdsArr.parallelStream().forEach(subSkuAutoIds -> {
            ProductSkuQueryReq request = new ProductSkuQueryReq();
            request.setSkuAutoIds(subSkuAutoIds);
            ProductSkuMergeQueryResp resp = ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.queryProductSkuMerge(request));
            if (resp != null && CollectionUtils.isNotEmpty(resp.getProductSkuList())) {
                productSkuList.addAll(resp.getProductSkuList());
            }
        });
        return productSkuList;
    }

    public BaseResp createProduct(SaveProductReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> productCmdFeign.createProduct(request));
    }

    public BaseResp updateProduct(SaveProductReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> productCmdFeign.updateProduct(request));
    }

    public BaseResp batchOnOffSaleProduct(ProductOnOffSaleReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> productCmdFeign.batchOnOffSaleProduct(request));
    }

    public BaseResp batchDeleteProduct(ProductDeleteReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> productCmdFeign.batchDeleteProduct(request));
    }

    public BaseResp batchSaveProductShopSequence(ProductUpdateSequenceReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> productCmdFeign.batchSaveProductShopSequence(request));
    }

    public BaseResp batchBindDescriptionTemplate(BindDescriptionTemplateReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> productCmdFeign.batchBindDescriptionTemplate(request));
    }

    public BaseResp bindRecommendProduct(BindRecommendProductReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> productCmdFeign.bindRecommendProduct(request));
    }

    public BaseResp batchBindFreightTemplate(BindFreightTemplateReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> productCmdFeign.batchBindFreightTemplate(request));
    }

    public BaseResp batchUpdateProductPrice(ProductSkuUpdatePriceReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> productCmdFeign.batchUpdateProductPrice(request));
    }

    public BaseResp batchUpdateSafeStock(ProductUpdateSafeStockReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> productCmdFeign.batchUpdateSafeStock(request));
    }

    public BaseResp batchUpdateStock(ProductUpdateStockReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> productCmdFeign.batchUpdateStock(request));
    }

    public BaseImportResp importStock(ProductImportReq request) {
        return dealImportResp(ThriftResponseHelper.executeThriftCall(() -> productCmdFeign.importStock(dealProductImport(request))));
    }

    public BaseImportResp importOffSale(ProductImportReq request) {
        return dealImportResp(ThriftResponseHelper.executeThriftCall(() -> productCmdFeign.importOffSale(dealProductImport(request))));
    }

    public BaseImportResp importPrice(ProductImportReq request) {
        return dealImportResp(ThriftResponseHelper.executeThriftCall(() -> productCmdFeign.importPrice(dealProductImport(request))));
    }

    public StatisticalProductResp querySellerStatisticalProduct(Long shopId) {
        return ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.querySellerStatisticalProduct(shopId));
    }

    /**
     * 查询商品基本信息
     *
     * @param productId
     */
    public ProductBasicDto getProductBasic(Long productId) {
        QueryProductBasicReq req = new QueryProductBasicReq();
        req.setProductIds(Arrays.asList(productId));
        ProductBasicResp resp = ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.queryProductBasic(req));
        return CollectionUtils.isEmpty(resp.getProductList()) ? null : resp.getProductList().get(0);
    }

    public BaseImportResp importProduct(ProductImportReq remoteReq) {
        return dealImportResp(ThriftResponseHelper.executeThriftCall(() -> productCmdFeign.importProduct(dealProductImport(remoteReq))));
    }

    public BaseImportResp importProductUpdate(ProductImportReq remoteReq) {
        return dealImportResp(ThriftResponseHelper.executeThriftCall(() -> productCmdFeign.importProductUpdate(dealProductImport(remoteReq))));
    }

    public GenProductCodeResp generateProductCode() {
        return ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.generateProductCode());
    }

    private ProductImportReq dealProductImport(ProductImportReq remoteReq) {
        remoteReq.setFilePath(appendS3UrlHeader(remoteReq.getFilePath()));
        return remoteReq;
    }

    private BaseImportResp dealImportResp(BaseImportResp resp) {
        if (resp == null) {
            return null;
        }
        // resp.setFilePath(StringUtils.isEmpty(resp.getFilePath()) ? resp.getFilePath() : appendS3UrlHeader(resp.getFilePath()));
        return resp;
    }

    private String appendS3UrlHeader(String url) {
        if (StringUtils.isEmpty(url)) {
            return url;
        }
        return storageClient.formatUrl(url);
    }

    public void clearScrollId(String scrollId) {
        EsScrollClearReq req = new EsScrollClearReq();
        req.setScrollId(scrollId);
        ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.clearScrollId(req));
    }
}
