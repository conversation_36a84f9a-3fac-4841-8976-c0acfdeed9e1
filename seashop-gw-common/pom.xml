<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hishop.himall</groupId>
        <artifactId>himall-gw</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>seashop-gw-common</artifactId>
    <packaging>jar</packaging>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-gw-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-order-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-trade-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-erp-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-util</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-jdk8</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-security</artifactId>
        </dependency>

    </dependencies>

</project>