<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hishop.starter</groupId>
        <artifactId>hishop-parent</artifactId>
        <version>2.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <name>himall-base</name>

    <groupId>com.hishop.himall</groupId>
    <artifactId>himall-base</artifactId>
    <version>1.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <properties>
        <hishop.starter.version>2.0.1-SNAPSHOT</hishop.starter.version>
        <base.version>1.0.1-SNAPSHOT</base.version>
        <base.api.version>1.0.1-SNAPSHOT</base.api.version>
        <himall.trade.version>1.0.1-SNAPSHOT</himall.trade.version>
        <himall.order.version>1.0.1-SNAPSHOT</himall.order.version>
        <histore.report.version>1.0.1-SNAPSHOT</histore.report.version>
<!--        <himall.promotion.version>1.0.0-SNAPSHOT</himall.promotion.version>-->
<!--        <himall.product.version>1.0.0-SNAPSHOT</himall.product.version>-->
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.hishop.starter</groupId>
                <artifactId>hishop-xxl-job-client-boot-starter</artifactId>
                <version>${hishop.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.starter</groupId>
                <artifactId>hishop-rocketmq-starter</artifactId>
                <version>${hishop.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-api</artifactId>
                <version>${base.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-boot</artifactId>
                <version>${base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-common</artifactId>
                <version>${base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-core</artifactId>
                <version>${base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-dao</artifactId>
                <version>${base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-security</artifactId>
                <version>${base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-util</artifactId>
                <version>${base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-user-account</artifactId>
                <version>${base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-user-auth</artifactId>
                <version>${base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-user-shop</artifactId>
                <version>${base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-user-common</artifactId>
                <version>${base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-trade-api</artifactId>
                <version>${himall.trade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-order-api</artifactId>
                <version>${himall.order.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>himall-report-api</artifactId>
                <version>${histore.report.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <modules>
        <module>seashop-base-api</module>
        <module>seashop-base-boot</module>
        <module>seashop-base-common</module>
        <module>seashop-base-dao</module>
        <module>seashop-base-server</module>
        <module>seashop-base-core</module>
        <module>seashop-base-util</module>
        <module>seashop-base-security</module>
        <module>seashop-base-user-account</module>
        <module>seashop-base-user-shop</module>
        <module>seashop-base-user-auth</module>
        <module>seashop-base-user-common</module>
    </modules>


</project>
