package com.hishop.himall.report.core.service.impl;

import com.hishop.himall.report.api.request.BatchReportSourceCartReq;
import com.hishop.himall.report.api.request.BatchReportSourceCouponReq;
import com.hishop.himall.report.api.request.BatchReportSourceFollowProductReq;
import com.hishop.himall.report.api.request.BatchReportSourceFollowShopReq;
import com.hishop.himall.report.api.request.BatchReportSourceOrderBillReq;
import com.hishop.himall.report.api.request.BatchReportSourceOrderItemReq;
import com.hishop.himall.report.api.request.BatchReportSourceOrderReq;
import com.hishop.himall.report.api.request.BatchReportSourceProductReq;
import com.hishop.himall.report.api.request.BatchReportSourceRefundReq;
import com.hishop.himall.report.api.request.BatchReportSourceRegionReq;
import com.hishop.himall.report.api.request.BatchReportSourceShopReq;
import com.hishop.himall.report.api.request.BatchReportSourceUserReq;
import com.hishop.himall.report.api.request.BatchReportSourceVisitReq;
import com.hishop.himall.report.api.request.CartReq;
import com.hishop.himall.report.api.request.ReportSourceCartReq;
import com.hishop.himall.report.api.request.ReportSourceCouponReq;
import com.hishop.himall.report.api.request.ReportSourceFollowProductReq;
import com.hishop.himall.report.api.request.ReportSourceFollowShopReq;
import com.hishop.himall.report.api.request.ReportSourceOrderBillReq;
import com.hishop.himall.report.api.request.ReportSourceOrderItemReq;
import com.hishop.himall.report.api.request.ReportSourceOrderReq;
import com.hishop.himall.report.api.request.ReportSourceProductReq;
import com.hishop.himall.report.api.request.ReportSourceRefundReq;
import com.hishop.himall.report.api.request.ReportSourceRegionReq;
import com.hishop.himall.report.api.request.ReportSourceShopReq;
import com.hishop.himall.report.api.request.ReportSourceUserReq;
import com.hishop.himall.report.api.request.ReportSourceVisitReq;
import com.hishop.himall.report.api.request.VisitsReq;
import com.hishop.himall.report.core.service.ReportService;
import com.hishop.himall.report.core.util.PageUtils;
import com.hishop.himall.report.dao.domain.ReportSourceCart;
import com.hishop.himall.report.dao.domain.ReportSourceCoupon;
import com.hishop.himall.report.dao.domain.ReportSourceFollowProduct;
import com.hishop.himall.report.dao.domain.ReportSourceFollowShop;
import com.hishop.himall.report.dao.domain.ReportSourceOrder;
import com.hishop.himall.report.dao.domain.ReportSourceOrderBill;
import com.hishop.himall.report.dao.domain.ReportSourceOrderItem;
import com.hishop.himall.report.dao.domain.ReportSourceProduct;
import com.hishop.himall.report.dao.domain.ReportSourceRefund;
import com.hishop.himall.report.dao.domain.ReportSourceRegion;
import com.hishop.himall.report.dao.domain.ReportSourceShop;
import com.hishop.himall.report.dao.domain.ReportSourceUser;
import com.hishop.himall.report.dao.domain.ReportSourceVisit;
import com.hishop.himall.report.dao.mapper.ReportSourceCartMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceCouponMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceFollowProductMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceFollowShopMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceOrderBillMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceOrderItemMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceOrderMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceProductMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceRefundMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceRegionMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceShopMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceUserMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceVisitMapper;
import com.hishop.himall.report.dao.models.ReportSourceFollowShopDelete;
import com.hishop.himall.report.dao.models.SourceFollowProductDelete;
import com.hishop.himall.report.dao.models.SourceOrderBillDelete;
import com.hishop.himall.report.dao.service.ReportSourceOrderBillService;
import com.hishop.himall.report.dao.service.ReportSourceRegionService;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.SquirrelUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ReportServiceImpl implements ReportService {

    @Resource
    private ReportSourceVisitMapper reportSourceVisitMapper;
    @Resource
    private ReportSourceCartMapper reportSourceCartMapper;

    @Resource
    private ReportSourceCouponMapper reportSourceCouponMapper;

    @Resource
    private ReportSourceFollowProductMapper reportSourceFollowProductMapper;
    @Resource
    private ReportSourceFollowShopMapper reportSourceFollowShopMapper;

    @Resource
    private ReportSourceOrderMapper reportSourceOrderMapper;
    @Resource
    private ReportSourceOrderBillMapper reportSourceOrderBillMapper;
    @Resource
    private ReportSourceOrderItemMapper reportSourceOrderItemMapper;

    @Resource
    private ReportSourceProductMapper reportSourceProductMapper;

    @Resource
    private ReportSourceRefundMapper reportSourceRefundMapper;

    @Resource
    private ReportSourceShopMapper reportSourceShopMapper;

    @Resource
    private ReportSourceUserMapper reportSourceUserMapper;

    @Resource
    private ReportSourceRegionMapper reportSourceRegionMapper;

    @Resource
    private ReportSourceRegionService reportSourceRegionService;
    @Resource
    private ReportSourceOrderBillService reportSourceOrderBillService;
    @Resource
    private SquirrelUtil squirrelUtil;


    @Override
    public void createCart(CartReq req) {
        ReportSourceCart item = new ReportSourceCart();
        item.setUserId(req.getUserId());
        item.setShopId(req.getShopId());
        item.setProductId(req.getProductId());
        item.setQuantity(req.getCount());
        item.setCreateTime(req.getCreateTime());
        item.setId(req.getId());
        reportSourceCartMapper.insert(item);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCreateCart(BatchReportSourceCartReq req) {
        List<ReportSourceCartReq> list = req.getList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Function<ReportSourceCartReq, ReportSourceCart> function = v -> JsonUtil.copy(v, ReportSourceCart.class);
        Map<Integer, List<ReportSourceCart>> map = PageUtils.split(list, function);
        map.values().parallelStream()
                .forEach(values -> {
                    List<Long> ids = values.stream().map(ReportSourceCart::getId)
                            .filter(v -> v != null)
                            .distinct()
                            .collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(ids)) {
                        reportSourceCartMapper.deleteIds(ids);
                    }
                    reportSourceCartMapper.batchInsert(values);
                });
    }

    @Override
    public void updateCart(CartReq req) {
        ReportSourceCart item = new ReportSourceCart();
        item.setUserId(req.getUserId());
        item.setShopId(req.getShopId());
        item.setProductId(req.getProductId());
        item.setQuantity(req.getCount());
        item.setCreateTime(req.getCreateTime());
        item.setId(req.getId());
        reportSourceCartMapper.updateById(item);
    }

    @Override
    public void createCoupon(ReportSourceCouponReq req) {
        ReportSourceCoupon coupon = JsonUtil.copy(req, ReportSourceCoupon.class);
        reportSourceCouponMapper.insert(coupon);
    }

    @Override
    public void batchCreateSourceCoupon(BatchReportSourceCouponReq req) {
        List<ReportSourceCouponReq> list = req.getList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Function<ReportSourceCouponReq, ReportSourceCoupon> function = v -> JsonUtil.copy(v, ReportSourceCoupon.class);
        Map<Integer, List<ReportSourceCoupon>> map = PageUtils.split(list, function);
        map.values().parallelStream()
                .forEach(values -> {
                    List<Long> recordIds = values.stream().map(ReportSourceCoupon::getRecordId).distinct()
                            .collect(Collectors.toList());
                    reportSourceCouponMapper.deleteByRecordId(recordIds);
                    reportSourceCouponMapper.batchInsert(values);

                });
    }


    @Override
    public void updateCoupon(ReportSourceCouponReq req) {
        ReportSourceCoupon coupon = JsonUtil.copy(req, ReportSourceCoupon.class);
        reportSourceCouponMapper.updateById(coupon);
    }

    /**
     * 访问记录
     *
     * @param req 访问记录
     */


    @Override
    public void updateVisit(VisitsReq req) {

    }

    @Override
    public void createFollowProductReq(ReportSourceFollowProductReq req) {
        ReportSourceFollowProduct followProduct = JsonUtil.copy(req, ReportSourceFollowProduct.class);
        reportSourceFollowProductMapper.insert(followProduct);
    }

    @Override
    public void batchCreateSourceFollowProduct(BatchReportSourceFollowProductReq req) {
        List<ReportSourceFollowProductReq> list = req.getList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Function<ReportSourceFollowProductReq, ReportSourceFollowProduct> function = v -> JsonUtil.copy(v,
                                                                                                        ReportSourceFollowProduct.class);
        Map<Integer, List<ReportSourceFollowProduct>> map = PageUtils.split(list, function);

        map.values().parallelStream().forEach(values -> {
            List<SourceFollowProductDelete> deletes = values.stream()
                    .map(v -> new SourceFollowProductDelete(v.getUserId(), v.getProductId()))
                    .collect(Collectors.toList());
            reportSourceFollowProductMapper.deleteByFollow(deletes);

            reportSourceFollowProductMapper.batchInsert(values);
        });
    }

    @Override
    public void updateFollowProductReq(ReportSourceFollowProductReq req) {
        ReportSourceFollowProduct followProduct = JsonUtil.copy(req, ReportSourceFollowProduct.class);
        reportSourceFollowProductMapper.updateById(followProduct);
    }

    @Override
    public void createSourceFollowShop(ReportSourceFollowShopReq req) {
        ReportSourceFollowShop followShop = JsonUtil.copy(req, ReportSourceFollowShop.class);
        reportSourceFollowShopMapper.insert(followShop);
    }


    @Override
    public void batchCreateSourceFollowShop(BatchReportSourceFollowShopReq req) {
        List<ReportSourceFollowShopReq> list = req.getList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Function<ReportSourceFollowShopReq, ReportSourceFollowShop> function = v -> JsonUtil.copy(v,
                                                                                                  ReportSourceFollowShop.class);
        Map<Integer, List<ReportSourceFollowShop>> map = PageUtils.split(list, function);
        map.values().parallelStream()
                .forEach(values -> {
                    List<ReportSourceFollowShopDelete> deletes = values.stream()
                            .map(v -> new ReportSourceFollowShopDelete(v.getUserId(), v.getShopId()))
                            .collect(Collectors.toList());
                    reportSourceFollowShopMapper.deleteByFollow(deletes);
                    reportSourceFollowShopMapper.batchInsert(values);
                });
    }

    @Override
    public void updateSourceFollowShop(ReportSourceFollowShopReq req) {
        ReportSourceFollowShop followShop = JsonUtil.copy(req, ReportSourceFollowShop.class);
        reportSourceFollowShopMapper.updateById(followShop);
    }

    @Override
    public void createSourceOrder(ReportSourceOrderReq req) {
        ReportSourceOrder sourceOrder = JsonUtil.copy(req, ReportSourceOrder.class);
        reportSourceOrderMapper.insert(sourceOrder);
    }

    @Override
    public void batchCreateSourceOrder(BatchReportSourceOrderReq req) {
        List<ReportSourceOrderReq> list = req.getList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Function<ReportSourceOrderReq, ReportSourceOrder> func = v -> JsonUtil.copy(v, ReportSourceOrder.class);
        Map<Integer, List<ReportSourceOrder>> split = PageUtils.split(list, func);
        split.values().parallelStream()
                .forEach(values -> {
                    List<String> orderIds =
                            values.stream().map(ReportSourceOrder::getOrderId).distinct()
                                    .collect(Collectors.toList());
                    reportSourceOrderMapper.deleteOrderIds(orderIds);
                    reportSourceOrderMapper.batchInsert(values);
                });
    }

    @Override
    public void updateSourceOrder(ReportSourceOrderReq req) {
        ReportSourceOrder sourceOrder = JsonUtil.copy(req, ReportSourceOrder.class);
        reportSourceOrderMapper.updateById(sourceOrder);
    }

    @Override
    public void createSourceOrderItem(ReportSourceOrderItemReq req) {
        ReportSourceOrderItem reportSourceOrderItem = JsonUtil.copy(req, ReportSourceOrderItem.class);
        reportSourceOrderItemMapper.insert(reportSourceOrderItem);
    }

    @Override
    public void batchCreateSourceOrderItem(BatchReportSourceOrderItemReq req) {
        List<ReportSourceOrderItemReq> list = req.getList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Function<ReportSourceOrderItemReq, ReportSourceOrderItem> func = v -> JsonUtil.copy(v,
                                                                                            ReportSourceOrderItem.class);
        Map<Integer, List<ReportSourceOrderItem>> map = PageUtils.split(list, func);
        map.values().parallelStream()
                .forEach(values -> {
                    List<Long> orderIds = values.stream().map(ReportSourceOrderItem::getOrderItemId)
                            .distinct()
                            .collect(Collectors.toList());
                    reportSourceOrderItemMapper.deleteOrderItemIds(orderIds);
                    reportSourceOrderItemMapper.batchInsert(values);
                });
    }

    @Override
    public void updateSourceOrderItem(ReportSourceOrderItemReq req) {
        ReportSourceOrderItem reportSourceOrderItem = JsonUtil.copy(req, ReportSourceOrderItem.class);
        reportSourceOrderItemMapper.updateById(reportSourceOrderItem);
    }

    @Override
    public void createSourceProduct(ReportSourceProductReq req) {
        ReportSourceProduct product = JsonUtil.copy(req, ReportSourceProduct.class);
        reportSourceProductMapper.insert(product);
    }

    @Override
    public void batchCreateSourceProduct(BatchReportSourceProductReq req) {
        List<ReportSourceProductReq> list = req.getList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Function<ReportSourceProductReq, ReportSourceProduct> func = v -> JsonUtil.copy(v, ReportSourceProduct.class);
        PageUtils.split(list, func)
                .values()
                .forEach(values -> {
                    List<Long> productIds = values.stream().map(ReportSourceProduct::getProductId).distinct()
                            .collect(Collectors.toList());
                    reportSourceProductMapper.deleteProductIds(productIds);
                    reportSourceProductMapper.batchInsert(values);
                });
    }

    @Override
    public void updateSourceProduct(ReportSourceProductReq req) {
        ReportSourceProduct product = JsonUtil.copy(req, ReportSourceProduct.class);
        reportSourceProductMapper.updateById(product);
    }

    @Override
    public void createSourceRefund(ReportSourceRefundReq req) {
        ReportSourceRefund refund = JsonUtil.copy(req, ReportSourceRefund.class);
        reportSourceRefundMapper.insert(refund);
    }

    @Override
    public void batchCreateSourceRefund(BatchReportSourceRefundReq req) {
        List<ReportSourceRefundReq> list = req.getList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Function<ReportSourceRefundReq, ReportSourceRefund> function = v -> JsonUtil.copy(v, ReportSourceRefund.class);
        Map<Integer, List<ReportSourceRefund>> map = PageUtils.split(list, function);
        map.values().parallelStream()
                .forEach(values -> {
                    List<Long> refundIds = values.stream()
                            .map(ReportSourceRefund::getRefundId)
                            .distinct()
                            .collect(Collectors.toList());
                    reportSourceRefundMapper.deleteRefundIds(refundIds);
                    reportSourceRefundMapper.batchInsert(values);
                });
    }

    @Override
    public void updateSourceRefund(ReportSourceRefundReq req) {
        ReportSourceRefund refund = JsonUtil.copy(req, ReportSourceRefund.class);
        reportSourceRefundMapper.updateById(refund);
    }

    @Override
    public void createSourceShop(ReportSourceShopReq req) {
        ReportSourceShop shop = JsonUtil.copy(req, ReportSourceShop.class);
        reportSourceShopMapper.insert(shop);
    }


    @Override
    public void batchCreateSourceShop(BatchReportSourceShopReq req) {
        List<ReportSourceShopReq> list = req.getList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Function<ReportSourceShopReq, ReportSourceShop> func = v -> JsonUtil.copy(v, ReportSourceShop.class);
        Map<Integer, List<ReportSourceShop>> map = PageUtils.split(list, func);

        map.values().parallelStream()
                .forEach(values -> {
                    List<Long> shopIds = values.stream()
                            .map(ReportSourceShop::getShopId).distinct().collect(Collectors.toList());
                    reportSourceShopMapper.deleteShopIds(shopIds);
                    reportSourceShopMapper.batchInsert(values);
                });
    }

    @Override
    public void updateSourceShop(ReportSourceShopReq req) {
        ReportSourceShop shop = JsonUtil.copy(req, ReportSourceShop.class);
        reportSourceShopMapper.updateById(shop);
    }

    @Override
    public void createSourceUser(ReportSourceUserReq req) {
        ReportSourceUser user = JsonUtil.copy(req, ReportSourceUser.class);
        reportSourceUserMapper.insert(user);
    }

    @Override
    public void batchCreateSourceUser(BatchReportSourceUserReq req) {
        List<ReportSourceUserReq> list = req.getList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Function<ReportSourceUserReq, ReportSourceUser> func = v -> JsonUtil.copy(v, ReportSourceUser.class);
        Map<Integer, List<ReportSourceUser>> map = PageUtils.split(list, func);
        map.values().parallelStream()
                .forEach(values -> {
                    List<Long> userIds = values.stream().map(ReportSourceUser::getUserId).distinct()
                            .collect(Collectors.toList());
                    reportSourceUserMapper.deleteUserIds(userIds);
                    reportSourceUserMapper.batchInsert(values);
                });
    }

    @Override
    public void updateSourceUser(ReportSourceUserReq req) {
        ReportSourceUser user = JsonUtil.copy(req, ReportSourceUser.class);
        reportSourceUserMapper.updateById(user);
    }

    @Override
    public void createSourceRegion(ReportSourceRegionReq req) {
        ReportSourceRegion region = JsonUtil.copy(req, ReportSourceRegion.class);
//        reportSourceRegionMapper.insert(region);
        reportSourceRegionService.saveOrUpdate(region);
    }

    @Override
    public void batchCreateSourceRegion(BatchReportSourceRegionReq req) {
        List<ReportSourceRegionReq> list = req.getList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Function<ReportSourceRegionReq, ReportSourceRegion> func = v -> JsonUtil.copy(v, ReportSourceRegion.class);
        Map<Integer, List<ReportSourceRegion>> map = PageUtils.split(list, func);
        map.values().parallelStream()
                .forEach(values -> {
                    List<Long> regionIds = values.stream().map(ReportSourceRegion::getId).distinct()
                            .collect(Collectors.toList());
                    reportSourceRegionMapper.deleteRegionIds(regionIds);
                    reportSourceRegionMapper.batchInsert(values);
                });
    }

    @Override
    public void createSourceOrderBill(ReportSourceOrderBillReq req) {
        ReportSourceOrderBill sourceOrder = JsonUtil.copy(req, ReportSourceOrderBill.class);
        reportSourceOrderBillService.saveOrUpdate(sourceOrder);
    }

    @Override
    public void batchCreateSourceOrderBill(BatchReportSourceOrderBillReq req) {
        List<ReportSourceOrderBillReq> list = req.getList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Function<ReportSourceOrderBillReq, ReportSourceOrderBill> func = v -> JsonUtil.copy(v,
                                                                                            ReportSourceOrderBill.class);
        Map<Integer, List<ReportSourceOrderBill>> map = PageUtils.split(list, func);
        map.values().parallelStream()
                .forEach(values -> {
                    List<Long> deletes = values.stream()
                            .map(ReportSourceOrderBill::getBillId)
                            .distinct()
                            .collect(Collectors.toList());
                    reportSourceOrderBillMapper.deleteBills(deletes);
                    reportSourceOrderBillMapper.batchInsert(values);
                });
    }

    /**
     * 访问记录
     *
     * @param req 访问记录
     */
    @Override
    public void createVisit(VisitsReq req) {
        for (VisitsReq.Item item : req.getItems()) {
            ReportSourceVisit visit = new ReportSourceVisit();
            visit.setVisitor(req.getVisitor());
            visit.setShopId(item.getShopId());
            visit.setProductId(item.getProductId());
            visit.setPage(item.getPage());
            reportSourceVisitMapper.insert(visit);
        }
    }

    @Override
    public void batchCreateSourceVisit(BatchReportSourceVisitReq req) {
        List<ReportSourceVisitReq> list = req.getList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Function<ReportSourceVisitReq, ReportSourceVisit> func = v -> JsonUtil.copy(v, ReportSourceVisit.class);
        Map<Integer, List<ReportSourceVisit>> map = PageUtils.split(list, func);
        map.values().parallelStream()
                .forEach(values -> reportSourceVisitMapper.batchInsert(values));

    }
}
