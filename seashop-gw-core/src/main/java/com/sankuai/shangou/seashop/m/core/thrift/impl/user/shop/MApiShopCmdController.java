package com.sankuai.shangou.seashop.m.core.thrift.impl.user.shop;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.user.shop.MApiShopService;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiCmdAgreementReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiCmdCreateQRReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiCmdShopSeqReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiCmdShopStatusReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiCmdShopStepsOneReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiCmdShopStepsThreeReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiCmdShopStepsTwoReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiCompanyCmdShopReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiPersonalCmdShopReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiShopTypeCmdReq;

/**
 * @description: 供应商服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/mApi/apiShop")
public class MApiShopCmdController {
    @Resource
    MApiShopService mApiShopService;


    @PostMapping(value = "/residencyApplication", consumes = "application/json")
    public ResultDto<String> residencyApplication(@RequestBody ApiCmdAgreementReq cmdAgreementReq) {
        return ThriftResponseHelper.responseInvoke("residencyApplication", cmdAgreementReq, req -> mApiShopService.residencyApplication(cmdAgreementReq));

    }

    @PostMapping(value = "/editProfilesOne", consumes = "application/json")
    public ResultDto<Long> editProfilesOne(@RequestBody ApiCmdShopStepsOneReq cmdShopStepsOneReq) {
        return ThriftResponseHelper.responseInvoke("editBaseInfo", cmdShopStepsOneReq, req -> mApiShopService.editProfilesOne(cmdShopStepsOneReq));
    }

    @PostMapping(value = "/editProfilesTwo", consumes = "application/json")
    public ResultDto<Long> editProfilesTwo(@RequestBody ApiCmdShopStepsTwoReq cmdShopStepsTwoReq) {
        return ThriftResponseHelper.responseInvoke("editBankInfo", cmdShopStepsTwoReq, req -> mApiShopService.editProfilesTwo(cmdShopStepsTwoReq));
    }

    @PostMapping(value = "/editProfilesThree", consumes = "application/json")
    public ResultDto<Long> editProfilesThree(@RequestBody ApiCmdShopStepsThreeReq cmdShopStepsThreeReq) {
        return ThriftResponseHelper.responseInvoke("editCategoryInfo", cmdShopStepsThreeReq,
            req -> mApiShopService.editProfilesThree(cmdShopStepsThreeReq));
    }

    @PostMapping(value = "/editShopPersonal", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> editShopPersonal(@Validated @RequestBody ApiPersonalCmdShopReq cmdShopReq) {
        return ThriftResponseHelper.responseInvoke("editShopPersonal", cmdShopReq, req -> mApiShopService.editShopPersonal(cmdShopReq));
    }

    @PostMapping(value = "/editShopEnterprise", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> editShopEnterprise(@Validated @RequestBody ApiCompanyCmdShopReq cmdShopReq) {
        return ThriftResponseHelper.responseInvoke("editShopEnterprise", cmdShopReq, req -> mApiShopService.editShopEnterprise(cmdShopReq));
    }

    @PostMapping(value = "/freezeShop", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> freezeShop(@Validated @RequestBody ApiCmdShopStatusReq cmdShopStatusReq) {
        return ThriftResponseHelper.responseInvoke("freezeShop", cmdShopStatusReq, req -> mApiShopService.freezeShop(cmdShopStatusReq));
    }

    @PostMapping(value = "/auditing", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> auditing(@Validated @RequestBody ApiCmdShopStatusReq cmdShopStatusReq) {
        return ThriftResponseHelper.responseInvoke("freezeShop", cmdShopStatusReq, req -> mApiShopService.auditing(cmdShopStatusReq));
    }

    @PostMapping(value = "/sendDepositRemind", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> sendDepositRemind(@RequestBody BaseIdReq baseIdReq) throws TException {
        return ThriftResponseHelper.responseInvoke("sendDepositRemind", baseIdReq, req -> mApiShopService.sendDepositRemind(baseIdReq));
    }

    @PostMapping(value = "/setShopType", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> setShopType(@RequestBody ApiShopTypeCmdReq cmdShopTypeReq) throws TException {
        return ThriftResponseHelper.responseInvoke("setShopType", cmdShopTypeReq, req -> mApiShopService.setShopType(cmdShopTypeReq));
    }

    @GetMapping(value = "/getBankRegion")
    public ResultDto<String> getBankRegion() {
        return ThriftResponseHelper.responseInvoke("getBankRegion", null, req -> mApiShopService.getBankRegion());
    }

    @PostMapping(value = "/createQrCode", consumes = "application/json")
    public ResultDto<String> createQrCode(@RequestBody ApiCmdCreateQRReq baseIdReq) {
        return ThriftResponseHelper.responseInvoke("createQrCode", baseIdReq, req -> mApiShopService.createQrCode(req));
    }

    @PostMapping(value = "/updateSeq", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> updateSeq(@RequestBody ApiCmdShopSeqReq baseIdReq) {
        return ThriftResponseHelper.responseInvoke("updateSeq", baseIdReq, req -> mApiShopService.updateSeq(req));
    }


}
