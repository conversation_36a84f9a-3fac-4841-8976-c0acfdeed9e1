package com.sankuai.shangou.seashop.mall.core.thrift.impl.trade;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.trade.thrift.core.PreOrderCmdFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiUserDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.order.ApiSubmitOrderReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade.ApiChooseCouponReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade.ApiChooseShippingAddressReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade.ApiPreviewChangeSkuQuantityReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.ApiChooseCouponResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.ApiPreviewChangeSkuQuantityResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.ApiPreviewOrderResp;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.mall.common.remote.trade.MallPreOrderRemoteService;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.trade.thrift.core.request.ChooseCouponReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.ChooseShippingAddressReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PreviewChangeSkuQuantityReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.SubmitOrderReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.ChooseCouponResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.PreviewChangeSkuQuantityResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.PreviewOrderResp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mallApi/apiPreOrder")
@Slf4j
public class MallApiPreOrderCmdController {

    @Resource
    private MallPreOrderRemoteService mallPreOrderRemoteService;
    @Resource
    private PreOrderCmdFeign preOrderCmdFeign;

    @NeedLogin
    @PostMapping(value = "/submitOrder", consumes = "application/json")
    public ResultDto<ApiPreviewOrderResp> submitOrder(@RequestBody ApiSubmitOrderReq orderReq) {
        log.info("【预订单】提交订单, 请求参数={}", JsonUtil.toJsonString(orderReq));
        // 参数基础校验
        orderReq.checkParameter();
        // 参数转换
        SubmitOrderReq req = JsonUtil.copy(orderReq, SubmitOrderReq.class);
        UserDto user = new UserDto();

        LoginMemberDto member = TracerUtil.getMemberDto();;
        user.setUserId(member.getId());
        user.setUserName(member.getName());
        user.setUserPhone(member.getUserPhone());
        req.setUser(user);

        ResultDto<PreviewOrderResp> resp = mallPreOrderRemoteService.submitOrder(req);

        ResultDto<ApiPreviewOrderResp> result = ResultDto.newWithData(JsonUtil.copy(resp.getData(), ApiPreviewOrderResp.class));
        if (!resp.isSuccess()) {
            result.fail(resp.getCode(), resp.getMessage());
        }
        return result;
    }

    @NeedLogin
    @PostMapping(value = "/chooseCoupon", consumes = "application/json")
    public ResultDto<ApiChooseCouponResp> chooseCoupon(@RequestBody ApiChooseCouponReq chooseCouponReq) {
        Long userId = TracerUtil.getMemberDto().getId();
        log.info("【预订单】选择优惠券, userId={}, 请求参数={}", userId, JsonUtil.toJsonString(chooseCouponReq));
        chooseCouponReq.setUserId(userId);
        return ThriftResponseHelper.responseInvoke("chooseCoupon", chooseCouponReq, func -> {
            ChooseCouponResp resp = ThriftResponseHelper.executeThriftCall(() ->
                    preOrderCmdFeign.chooseCoupon(JsonUtil.copy(func, ChooseCouponReq.class)));
            return JsonUtil.copy(resp, ApiChooseCouponResp.class);
        });
    }

    @NeedLogin
    @PostMapping(value = "/chooseShippingAddress", consumes = "application/json")
    public ResultDto<ApiPreviewOrderResp> chooseShippingAddress(@RequestBody ApiChooseShippingAddressReq chooseShippingAddressReq) {
        LoginMemberDto loginDto = TracerUtil.getMemberDto();;
        Long userId = loginDto.getId();
        log.info("【预订单】选择收货地址, userId={}, 请求参数={}", userId, JsonUtil.toJsonString(chooseShippingAddressReq));
        ApiUserDto user = new ApiUserDto();
        user.setUserId(userId);
        user.setUserName(loginDto.getName());
        user.setUserPhone(loginDto.getUserPhone());
        chooseShippingAddressReq.setUser(user);
        return ThriftResponseHelper.responseInvoke("chooseShippingAddress", chooseShippingAddressReq, func -> {

            PreviewOrderResp resp = ThriftResponseHelper.executeThriftCall(() ->
                    preOrderCmdFeign.chooseShippingAddress(JsonUtil.copy(func, ChooseShippingAddressReq.class)));
            return JsonUtil.copy(resp, ApiPreviewOrderResp.class);
        });
    }

    @NeedLogin
    @PostMapping(value = "/changePreviewSkuCount", consumes = "application/json")
    public ResultDto<ApiPreviewChangeSkuQuantityResp> changePreviewSkuCount(@RequestBody ApiPreviewChangeSkuQuantityReq changeQuantityReq) throws TException {
        changeQuantityReq.checkParameter();
        Long userId = TracerUtil.getMemberDto().getId();
        log.info("【预订单】预览订单页修改数量, userId={}, 请求参数={}", userId, JsonUtil.toJsonString(changeQuantityReq));
        changeQuantityReq.setUserId(userId);
        if (changeQuantityReq.getShippingAddressId() == null && changeQuantityReq.getShippingAddress() != null) {
            changeQuantityReq.setShippingAddressId(changeQuantityReq.getShippingAddress().getId());
        }
        ResultDto<PreviewChangeSkuQuantityResp> resp = mallPreOrderRemoteService.changePreviewSkuCount(JsonUtil.copy(changeQuantityReq, PreviewChangeSkuQuantityReq.class));
        ResultDto<ApiPreviewChangeSkuQuantityResp> result = ResultDto.newWithData(JsonUtil.copy(resp.getData(), ApiPreviewChangeSkuQuantityResp.class));
        if (!resp.isSuccess()) {
            result.fail(resp.getCode(), resp.getMessage());
        }
        return result;
    }

}
