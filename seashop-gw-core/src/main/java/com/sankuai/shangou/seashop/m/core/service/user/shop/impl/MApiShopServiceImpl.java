package com.sankuai.shangou.seashop.m.core.service.user.shop.impl;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.core.service.user.shop.MApiShopService;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.*;
import com.sankuai.shangou.seashop.m.thrift.user.shop.response.*;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.*;
import com.sankuai.shangou.seashop.user.thrift.shop.response.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 权限服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class MApiShopServiceImpl implements MApiShopService {
    @Resource
    private ShopQueryFeign shopQueryFeign;
    @Resource
    private ShopCmdFeign shopCmdFeign;


    @Override
    public ApiShopIdsResp getShopIds(ApiQueryShopReq queryShopReq) {
        QueryShopReq queryShopReq1 = JsonUtil.copy(queryShopReq, QueryShopReq.class);
        ShopIdsResp shopIdsResp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.getShopIds(queryShopReq1));
        return JsonUtil.copy(shopIdsResp, ApiShopIdsResp.class);
    }

    @Override
    public String residencyApplication(ApiCmdAgreementReq cmdAgreementReq) {
        return ThriftResponseHelper.executeThriftCall(() ->
                shopCmdFeign.residencyApplication(JsonUtil.copy(cmdAgreementReq, CmdAgreementReq.class)));
    }

    @Override
    public Long editProfilesOne(ApiCmdShopStepsOneReq cmdShopStepsOneReq) {
        return ThriftResponseHelper.executeThriftCall(() ->
                shopCmdFeign.editBaseInfo(JsonUtil.copy(cmdShopStepsOneReq, CmdShopStepsOneReq.class)));
    }

    @Override
    public Long editProfilesTwo(ApiCmdShopStepsTwoReq cmdShopStepsTwoReq) {
        return ThriftResponseHelper.executeThriftCall(() ->
                shopCmdFeign.editBankInfo(JsonUtil.copy(cmdShopStepsTwoReq, CmdShopStepsTwoReq.class)));
    }

    @Override
    public Long editProfilesThree(ApiCmdShopStepsThreeReq cmdShopStepsThreeReq) {
        return ThriftResponseHelper.executeThriftCall(() ->
                shopCmdFeign.editCategoryInfo(JsonUtil.copy(cmdShopStepsThreeReq, CmdShopStepsThreeReq.class)));
    }

    @Override
    public BasePageResp<ApiShopResp> queryPage(ApiShopQueryPagerReq request) {
        ShopQueryPagerReq shopQueryPagerReq = JsonUtil.copy(request, ShopQueryPagerReq.class);
        BasePageResp<ShopResp> shopPageResp = ThriftResponseHelper.executeThriftCall(() ->
                shopQueryFeign.queryPage(shopQueryPagerReq));
        return PageResultHelper.transfer(shopPageResp, ApiShopResp.class);
    }

    @Override
    public ApiShopDetailResp queryDetail(BaseIdReq shopId) {
        ShopDetailResp shopDetailResp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.queryDetail(shopId));
        return JsonUtil.copy(shopDetailResp, ApiShopDetailResp.class);
    }

    @Override
    public BaseResp auditing(ApiCmdShopStatusReq shopId) {
        CmdShopStatusReq cmdShopStatusReq = JsonUtil.copy(shopId, CmdShopStatusReq.class);
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.auditing(cmdShopStatusReq));
    }

    @Override
    public Long editShopPersonal(ApiPersonalCmdShopReq cmdShopReq) {
        CmdShopReq cmdShopReq1 = JsonUtil.copy(cmdShopReq, CmdShopReq.class);
        cmdShopReq1.setOperationShopId(cmdShopReq.getOperationShopId());
        cmdShopReq1.setOperationUserId(cmdShopReq.getOperationUserId());
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.editShopPersonal(cmdShopReq1));
    }

    @Override
    public Long editShopEnterprise(ApiCompanyCmdShopReq cmdShopReq) {
        CmdShopReq cmdShopReq1 = JsonUtil.copy(cmdShopReq, CmdShopReq.class);
        cmdShopReq1.setOperationShopId(cmdShopReq.getOperationShopId());
        cmdShopReq1.setOperationUserId(cmdShopReq.getOperationUserId());
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.editShopEnterprise(cmdShopReq1));
    }

    @Override
    public Long freezeShop(ApiCmdShopStatusReq cmdShopStatusReq) {
        CmdShopStatusReq cmdShopStatusReq1 = JsonUtil.copy(cmdShopStatusReq, CmdShopStatusReq.class);
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.freezeShop(cmdShopStatusReq1));
    }

    @Override
    public BaseResp sendDepositRemind(BaseIdReq baseIdReq) {
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.sendDepositRemind(baseIdReq));

    }

    @Override
    public ApiShopUserCountResp countShopUser() {
        ShopUserCountResp shopUserCountResp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.countShopUser());
        //转化结果
        return JsonUtil.copy(shopUserCountResp, ApiShopUserCountResp.class);
    }

    @Override
    public List<ApiShopResp> queryShopsByIds(ApiShopQueryReq baseBatchIdReq) {
        List<ShopResp> list = ThriftResponseHelper.executeThriftCall(() ->
                shopQueryFeign.queryShopsByIds(JsonUtil.copy(baseBatchIdReq, ShopQueryReq.class)));
        return JsonUtil.copyList(list, ApiShopResp.class);
    }

    @Override
    public BasePageResp<ApiShopSimpleResp> querySimplePage(ApiShopQueryPagerReq request) {
        ShopQueryPagerReq shopQueryPagerReq = JsonUtil.copy(request, ShopQueryPagerReq.class);
        BasePageResp<ShopSimpleResp> basePageResp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.querySimplePage(shopQueryPagerReq));
        //判空
        if (basePageResp == null) {
            return new BasePageResp<>();
        }
        BasePageResp<ApiShopSimpleResp> respBasePageResp = PageResultHelper.transfer(basePageResp, ApiShopSimpleResp.class);
        //设置链接
        respBasePageResp.getData().forEach(shopSimpleResp -> {
            shopSimpleResp.setLink("https://shop.seashop.com/shop/" + shopSimpleResp.getId());
            shopSimpleResp.setPcLink("https://shop.seashop.com/shop/" + shopSimpleResp.getId());
        });
        return respBasePageResp;
    }

    @Override
    public BaseResp setShopType(ApiShopTypeCmdReq shopId) {
        ShopTypeCmdReq shopTypeCmdReq = JsonUtil.copy(shopId, ShopTypeCmdReq.class);
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.setShopType(shopTypeCmdReq));
    }

    @Override
    public String getBankRegion() {
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.getBankRegion());
    }

    @Override
    public String createQrCode(ApiCmdCreateQRReq apiCmdCreateQRReq) {
        CmdCreateQRReq cmdCreateQRReq = JsonUtil.copy(apiCmdCreateQRReq, CmdCreateQRReq.class);
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.createQR(cmdCreateQRReq));
    }


    @Override
    public BaseResp updateSeq(ApiCmdShopSeqReq apiCmdShopSeqReq) {
        CmdShopSeqReq cmdShopSeqReq = JsonUtil.copy(apiCmdShopSeqReq, CmdShopSeqReq.class);
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.updateSeq(cmdShopSeqReq));
    }

    @Override
    public String queryShopCategoryDetail(BaseIdReq shopId) {
        return ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.queryShopCategoryDetail(shopId));
    }

}
