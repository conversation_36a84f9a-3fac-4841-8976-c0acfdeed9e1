package com.sankuai.shangou.seashop.seller.core.service.promotion.excel.dto;

import com.alibaba.excel.annotation.ExcelProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2023/11/15/015
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@Getter
@Setter
public class ExclusivePriceProductExcelDto {

    @ExcelProperty(value = "商品Id")
    private String productId;

    @ExcelProperty(value = "规格Id")
    private String skuAutoId;

    @ExcelProperty(value = "商家账号")
    private String userName;

    @ExcelProperty(value = "价格")
    private String exclusivePrice;

    @ExcelProperty(value = "错误信息")
    private String msg;
}
