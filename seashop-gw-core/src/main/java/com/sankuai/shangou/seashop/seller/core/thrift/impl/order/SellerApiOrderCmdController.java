package com.sankuai.shangou.seashop.seller.core.thrift.impl.order;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.order.thrift.core.OrderCmdFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.core.dto.ShopDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.CancelOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.SellerUpdateReceiverReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.UpdateExpressReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.UpdateFreightReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.UpdateItemAmountReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.BatchDeliverOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.ReBuyBySellerReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.SellerRemarkReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiBatchDeliverOrderReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiCancelOrderReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiReBuyBySellerReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiSellerRemarkReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiSellerUpdateReceiverReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiUpdateExpressReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiUpdateFreightReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiUpdateItemAmountReq;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sellerApi/apiOrder")
@Slf4j
public class SellerApiOrderCmdController {

    @Resource
    private OrderCmdFeign orderCmdFeign;

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/updateReceiverBySeller", consumes = "application/json")
    public ResultDto<BaseResp> updateReceiverBySeller(@RequestBody ApiSellerUpdateReceiverReq updateReceiverReq) {
        return ThriftResponseHelper.responseInvoke("【订单】供应商修改收货人信息", updateReceiverReq, func -> {
            SellerUpdateReceiverReq req = JsonUtil.copy(updateReceiverReq, SellerUpdateReceiverReq.class);
            LoginShopDto shop = TracerUtil.getShopDto();
            req.setShopId(shop.getShopId());
            req.setUserName(shop.getName());
            return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.updateReceiverBySeller(req));
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/updateItemAmount", consumes = "application/json")
    public ResultDto<BaseResp> updateItemAmount(@RequestBody ApiUpdateItemAmountReq updateItemAmountReq) {
        return ThriftResponseHelper.responseInvoke("【订单】供应商改价", updateItemAmountReq, func -> {
            UpdateItemAmountReq req = JsonUtil.copy(updateItemAmountReq, UpdateItemAmountReq.class);
            LoginShopDto shop = TracerUtil.getShopDto();
            req.setShopId(shop.getShopId());
            req.setUserName(shop.getName());
            return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.updateItemAmount(req));
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/updateFreight", consumes = "application/json")
    public ResultDto<BaseResp> updateFreight(@RequestBody ApiUpdateFreightReq updateFreightReq) {
        return ThriftResponseHelper.responseInvoke("【订单】供应商修改运费", updateFreightReq, func -> {
            UpdateFreightReq req = JsonUtil.copy(updateFreightReq, UpdateFreightReq.class);
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.setUserName(TracerUtil.getShopDto().getName());
            return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.updateFreight(req));
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/batchDeliverOrder", consumes = "application/json")
    public ResultDto<BaseResp> batchDeliverOrder(@RequestBody ApiBatchDeliverOrderReq orderDeliveryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】供应商批量发货", orderDeliveryReq, func -> {
            BatchDeliverOrderReq req = JsonUtil.copy(orderDeliveryReq, BatchDeliverOrderReq.class);
            LoginShopDto loginShop = TracerUtil.getShopDto();
            ShopDto shop = new ShopDto();
            shop.setShopId(loginShop.getShopId());
            shop.setShopName(loginShop.getName());
            req.setShop(shop);

            UserDto userDto = new UserDto();
            userDto.setUserId(loginShop.getManagerId());
            userDto.setUserName(loginShop.getName());
            req.setUser(userDto);
            return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.batchDeliverOrder(req));
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/updateExpress", consumes = "application/json")
    public ResultDto<BaseResp> updateExpress(@RequestBody ApiUpdateExpressReq updateExpressReq) throws TException {

        return ThriftResponseHelper.responseInvoke("【订单】供应商修改物流信息", updateExpressReq, func -> {
            UpdateExpressReq req = JsonUtil.copy(updateExpressReq, UpdateExpressReq.class);
            req.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.updateExpress(req));
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/sellerRemark", consumes = "application/json")
    public ResultDto<BaseResp> sellerRemark(@RequestBody ApiSellerRemarkReq remarkReq) throws TException {

        return ThriftResponseHelper.responseInvoke("【订单】供应商添加备注", remarkReq, func -> {
            SellerRemarkReq param = JsonUtil.copy(remarkReq, SellerRemarkReq.class);
            ShopDto shop = new ShopDto();
            shop.setShopId(TracerUtil.getShopDto().getShopId());
            shop.setShopName(TracerUtil.getShopDto().getName());
            param.setShop(shop);
            return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.sellerRemark(param));
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/cancelOrder", consumes = "application/json")
    public ResultDto<BaseResp> cancelOrder(@RequestBody ApiCancelOrderReq cancelOrderReq) throws TException {

        return ThriftResponseHelper.responseInvoke("【订单】供应商取消订单", cancelOrderReq, func -> {
            LoginShopDto shop = TracerUtil.getShopDto();

            CancelOrderReq req = JsonUtil.copy(func, CancelOrderReq.class);
            req.setUserId(shop.getManagerId());
            req.setUserName(shop.getName());
            req.setShopId(shop.getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.sellerCancelOrder(req));
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/reBuyBySeller", consumes = "application/json")
    public ResultDto<BaseResp> reBuyBySeller(@RequestBody ApiReBuyBySellerReq reBuyBySellerReq) throws TException {

        return ThriftResponseHelper.responseInvoke("【订单】供应商将用户订单重新加入购物车", reBuyBySellerReq, func -> {
            LoginShopDto shop = TracerUtil.getShopDto();
            reBuyBySellerReq.setShopId(shop.getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.reBuyBySeller(JsonUtil.copy(func, ReBuyBySellerReq.class)));
        });
    }
}
