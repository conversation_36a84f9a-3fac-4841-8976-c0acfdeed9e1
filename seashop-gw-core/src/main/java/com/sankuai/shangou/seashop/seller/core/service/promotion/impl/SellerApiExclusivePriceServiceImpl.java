package com.sankuai.shangou.seashop.seller.core.service.promotion.impl;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.excel.EasyExcel;
import com.hishop.starter.storage.client.StorageClient;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.handler.AbstractListDataGetter;
import com.sankuai.shangou.seashop.base.export.output.FileWriteAndOutputWay;
import com.sankuai.shangou.seashop.base.s3plus.S3plusStorageService;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductSkuQueryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductSkuMergeDto;
import com.sankuai.shangou.seashop.seller.common.constant.SellerConstant;
import com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService;
import com.sankuai.shangou.seashop.seller.common.remote.user.SellerMemberRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.promotion.SellerApiExclusivePriceService;
import com.sankuai.shangou.seashop.seller.core.service.promotion.excel.dto.ExclusivePriceProductExcelDataHolder;
import com.sankuai.shangou.seashop.seller.core.service.promotion.excel.dto.ExclusivePriceProductExcelDto;
import com.sankuai.shangou.seashop.seller.core.service.promotion.excel.listener.ImportExclusivePriceProductListener;
import com.sankuai.shangou.seashop.seller.core.service.promotion.excel.po.ExclusivePriceProductExcelPo;
import com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion.bo.ApiExclusivePriceImportBo;
import com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion.dto.ApiExclusivePriceImportDto;
import com.sankuai.shangou.seashop.seller.thrift.core.dto.promotion.ApiMemberProductDto;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberResp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2023/11/14/014
 * @description:
 */
@Service
@Slf4j
public class SellerApiExclusivePriceServiceImpl implements SellerApiExclusivePriceService {

    @Resource
    private S3plusStorageService s3plusStorageService;
    @Resource
    private FileWriteAndOutputWay fileOutputWay;
    @Resource
    private SellerProductRemoteService sellerProductRemoteService;
    @Resource
    private SellerMemberRemoteService sellerMemberRemoteService;
    @Resource
    private StorageClient storageClient;

    @Override
    public ApiExclusivePriceImportDto importData(ApiExclusivePriceImportBo bo) {

        try (
            // S3上获取上传文件的文件流
            InputStream inputStream = s3plusStorageService.readExcelFromOnlineUrl(storageClient.formatUrl(bo.getFileUrl()))
            ) {

            // 解析Excel
            ImportExclusivePriceProductListener importExclusivePriceProductListener = new ImportExclusivePriceProductListener();
            EasyExcel.read(inputStream, ExclusivePriceProductExcelPo.class, importExclusivePriceProductListener)
                .sheet()
                .doRead();
            ExclusivePriceProductExcelDataHolder data = importExclusivePriceProductListener.getData();
            List<ExclusivePriceProductExcelDto> productList = data.getProductList();
            // 排除重复的数据
            Set<String> set = new HashSet<>();
            for (ExclusivePriceProductExcelDto dto : productList) {
                if (StrUtil.isNotBlank(dto.getMsg())) {
                    continue;
                }
                String key = dto.getProductId() + "-" + dto.getSkuAutoId() + "-" + dto.getUserName();
                boolean add = set.add(key);
                if (!add) {
                    dto.setMsg("重复数据");
                }
            }

            List<ApiMemberProductDto> productDtoList = JsonUtil.copyList(productList, ApiMemberProductDto.class);

            // 1、校验账户信息，获取memberId
            verifyMerchant(productDtoList);

            // 2、校验商品信息，获取商品名称、规格名称和商城价格
            checkProduct(productDtoList, bo.getShopId());

            ApiExclusivePriceImportDto dto = new ApiExclusivePriceImportDto();
            dto.setProductDtoList(productDtoList.stream().filter(c -> StringUtils.isBlank(c.getMsg())).collect(Collectors.toList()));
            List<ExclusivePriceProductExcelDto> errList = null;
            List<ApiMemberProductDto> successDtoList = productDtoList.stream().filter(c -> StrUtil.isBlank(c.getMsg())).collect(Collectors.toList());
            List<ApiMemberProductDto> errDtoList = productDtoList.stream().filter(c -> StringUtils.isNotBlank(c.getMsg())).collect(Collectors.toList());
            // 过滤出错误数据，写入文件，上传S3
            if (CollUtil.isNotEmpty(errDtoList)) {
                errList = JsonUtil.copyList(errDtoList, ExclusivePriceProductExcelDto.class);
            }

            if (CollectionUtils.isNotEmpty(errList)) {
                String filePath = fileOutputWay.writeAndOutput(
                    new AbstractListDataGetter("error", errList, ExclusivePriceProductExcelDto.class),
                    UUID.randomUUID().toString(), new Date(), null);

                String downloadUrl = s3plusStorageService.getDownloadUrl(filePath);
                dto.setErrFileUrl(storageClient.formatUrl(downloadUrl));
            }

            dto.setSuccessCount(successDtoList.size());
            dto.setErrorCount(errDtoList.size());
            return dto;
        }
        catch (BusinessException be) {
            log.error("导入专享价失败", be);
            throw be;
        }
        catch (Exception e) {
            log.error("导入专享价失败", e);
            throw new BusinessException("导入专享价失败");
        }
    }

    private void checkProduct(List<ApiMemberProductDto> productDtoList, Long shopId) {
        /*
         * 验证商品信息
         */
        // 过滤出正常数据，查询商品信息
        List<ApiMemberProductDto> normalList = productDtoList.stream().filter(c -> StrUtil.isBlank(c.getMsg())).collect(Collectors.toList());
        // 没有正常的商品 直接返回
        if (CollectionUtils.isEmpty(normalList)) {
            return;
        }

        ProductSkuQueryReq req = new ProductSkuQueryReq();
        List<Long> skuAutoIdList = new ArrayList<>();
        normalList.stream().map(ApiMemberProductDto::getSkuAutoId).mapToLong(Long::parseLong).forEach(skuAutoIdList::add);
        req.setSkuAutoIds(skuAutoIdList);

        // 获取skuMap
        List<ProductSkuMergeDto> productSkuList = sellerProductRemoteService.queryProductSkuMergeBySkuAutoIds(skuAutoIdList);
        Map<Long, ProductSkuMergeDto> skuMap =
                Optional.ofNullable(productSkuList)
                        .orElse(Collections.emptyList())
                        .stream().collect(Collectors.toMap(ProductSkuMergeDto::getSkuAutoId, Function.identity(), (k1, k2) -> k1));
        normalList.forEach(importSku -> {
            ProductSkuMergeDto dbSku = skuMap.get(Long.parseLong(importSku.getSkuAutoId()));
            if (dbSku == null) {
                importSku.setMsg("规格不存在");
                return;
            }

            if (!dbSku.getProductId().equals(Long.parseLong(importSku.getProductId()))) {
                importSku.setMsg("商品id和规格id不匹配");
                return;
            }

            if (!dbSku.getShopId().equals(shopId)) {
                importSku.setMsg("无权操作");
                return;
            }

            importSku.setProductName(dbSku.getProductName());
            importSku.setSkuId(dbSku.getSkuId());
            importSku.setSkuName(
                    Optional.ofNullable(dbSku.getSpec1Value()).orElse("")
                            + Optional.ofNullable(dbSku.getSpec2Value()).orElse("")
                            + Optional.ofNullable(dbSku.getSpec3Value()).orElse(""));
            importSku.setMallPrice(dbSku.getSalePrice() + "");
        });
    }

    private void verifyMerchant(List<ApiMemberProductDto> productDtoList) {
        // 拿到没有异常的数据
        List<ApiMemberProductDto> normalProductList = productDtoList.stream().filter(c -> StrUtil.isBlank(c.getMsg())).collect(Collectors.toList());
        if (CollUtil.isEmpty(normalProductList)) {
            return;
        }

        // 获取商家账户信息
        List<String> userNameList = normalProductList.stream().map(ApiMemberProductDto::getUserName).distinct().collect(Collectors.toList());
        // 查询商家完整信息
        List<MemberResp> memberList = sellerMemberRemoteService.queryMemberListByNames(userNameList);
        Map<String, MemberResp> memberMap = Optional.ofNullable(memberList)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(MemberResp::getUserName, Function.identity(), (k1, k2) -> k1));

        normalProductList.forEach(product -> {
            // 已经有异常了 直接返回
            if (StrUtil.isNotEmpty(product.getMsg())) {
                return;
            }

            MemberResp member = memberMap.get(product.getUserName());
            if (member == null) {
                product.setMsg(SellerConstant.MEMBER_NOT_EXIST);
                return;
            }

            product.setMemberId(String.valueOf(member.getId()));
        });
    }


}
