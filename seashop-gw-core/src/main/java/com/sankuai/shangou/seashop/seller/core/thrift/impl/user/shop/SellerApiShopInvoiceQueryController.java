package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.seller.common.remote.user.SellerShopInvoiceRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiQueryShopInvoiceReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.shop.ApiQueryShopInvoiceResp;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopInvoiceReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopInvoiceResp;

/**
 * @author： liweisong
 * @create： 2023/11/29 8:59
 */
@RestController
@RequestMapping("/sellerApi/apiShopInvoice")
public class SellerApiShopInvoiceQueryController {

    @Resource
    private SellerShopInvoiceRemoteService sellerShopInvoiceRemoteService;

    @PostMapping(value = "/queryShopInvoice", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiQueryShopInvoiceResp> queryShopInvoice(@RequestBody ApiQueryShopInvoiceReq queryShopInvoiceReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("queryShopInvoice", queryShopInvoiceReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.checkParameter();
            QueryShopInvoiceResp queryShopInvoiceResp = sellerShopInvoiceRemoteService.queryShopInvoice(JsonUtil.copy(req, QueryShopInvoiceReq.class));
            return JsonUtil.copy(queryShopInvoiceResp, ApiQueryShopInvoiceResp.class);
        });
    }
}
