# web服务端口号
server:
  port: 8080

spring:
  application:
    name: himall-order
  profiles:
    #本地环境，区别与开发环境
    active: develop_local
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  redis:
    database: 1
  cloud:
    nacos:
      server-addr: ${NACOS_SERVER:124.71.221.117:8848}
      username: ${NACOS_USERNAME:dev}
      password: ${NACOS_PASSWORD:dev@hishop}
      config:
        namespace: ${spring.profiles.active}
        group: 1.0.0
      discovery:
        namespace: ${spring.profiles.active}
        group: 1.0.0
  config:
    import:
      - optional:nacos:himall-common.yml
      - optional:nacos:${spring.application.name}.yml