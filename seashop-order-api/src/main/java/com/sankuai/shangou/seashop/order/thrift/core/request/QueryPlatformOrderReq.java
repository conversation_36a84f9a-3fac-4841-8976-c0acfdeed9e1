package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderQueryFromEnum;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "平台搜索订单入参")
public class QueryPlatformOrderReq extends BasePageReq {

    /**
     * 订单ID
     */
    @FieldDoc(description = "订单ID")
    private String orderId;

    /**
     * 订单ID集合
     */
    @FieldDoc(description = "订单ID集合")
    private List<String> orderIdList;
    /**
     * 商家手机号码
     */
    @FieldDoc(description = "商家手机号码")
    private String userPhone;
    /**
     * 用户昵称/账号
     */
    @FieldDoc(description = "用户昵称/账号")
    private String userName;
    @FieldDoc(description = "店铺名称")
    private String shopName;
    /**
     * 订单来源（多选，PC商城、小程序、牵牛花）
     */
    //@FieldDoc(description = "订单来源（0:PC商城、1:牵牛花）")
    //private Integer orderSource;
    /**
     * 付款方式（多选，支付宝扫码、微信小程序、企业网银、个人网银）
     */
    @FieldDoc(description = "付款方式（多选，1：支付宝扫码；3：微信小程序；5：企业网银；6：个人网银）")
    private List<Integer> paymentTypeList;
    /**
     * 发票类型（全部、普通发票、电子普通发票、增值税发票）
     */
    @FieldDoc(description = "发票类型（null:全部、1:普通发票、2:电子发票、3:增值税发票）")
    private Integer invoiceType;
    @FieldDoc(description = "搜索关键字，支持 商品名称，订单编号，规格ID，商品ID")
    private String searchKey;
    @FieldDoc(description = "订单状态。-1:所有订单；1:待付款；2：待发货；3：待收货；4：已关闭；5：已完成；6：支付中")
    private Integer orderStatus;
    /**
     * 支付单号，本系统生成的支付单号
     */
    @FieldDoc(description = "支付单号，本系统生成的支付单号")
    private String payId;
    /**
     * 交易单号，汇付返回的支付渠道号
     */
    @FieldDoc(description = "交易单号，汇付返回的支付渠道号")
    private String tradeNo;
    @FieldDoc(description = "下单时间-开始")
    private Date orderStartTime;
    @FieldDoc(description = "下单时间-结束")
    private Date orderEndTime;
    /**
     * 完成时间-开始
     */
    @FieldDoc(description = "完成时间-开始")
    private Date finishStartTime;
    /**
     * 完成时间-结束
     */
    @FieldDoc(description = "完成时间-结束")
    private Date finishEndTime;
    @FieldDoc(description = "查询来源。1-商家小程序；2-商家PC端；3-卖家PC端；4-平台PC端")
    private OrderQueryFromEnum queryFrom;
    @FieldDoc(description = "是否有发票")
    private Boolean hasInvoice;
    @FieldDoc(description = "是否评价")
    private Boolean hasComment;
    /**
     * 订单端口：来自哪个终端的订单。0：pc端，2：小程序
     */
    @FieldDoc(description = "订单端口。0：pc端，2：小程序")
    private List<Integer> platformList;
    @FieldDoc(description = "收货人手机号")
    private String cellPhone;
    /**
     * 基于scroll查询时的数据保留时间
     */
    @FieldDoc(description = "基于scroll查询时的数据保留时间")
    private Long timeValueMinutes;
    //@FieldDoc(description = "订单来源")
    //private List<Integer> orderSourceList;
    @FieldDoc(description = "订单状态。1:待付款；2：待发货；3：待收货；4：已关闭；5：已完成；6：支付中")
    private List<Integer> orderStatusList;
    /**
     * 用户id
     */
    @FieldDoc(description = "用户id")
    private Long userId;


    //
    //public Integer getOrderSource() {
    //    return orderSource;
    //}
    //
    //
    //public void setOrderSource(Integer orderSource) {
    //    this.orderSource = orderSource;
    //}


    public Long getOrderStartTimeLong() {
        return this.date2Long(this.orderStartTime);
    }


    public void setOrderStartTimeLong(Long orderStartTime) {
        this.orderStartTime = this.long2Date(orderStartTime);
    }


    public Long getOrderEndTimeLong() {
        return this.date2Long(this.orderEndTime);
    }


    public void setOrderEndTimeLong(Long orderEndTime) {
        this.orderEndTime = this.long2Date(orderEndTime);
    }


    public Long getFinishStartTimeLong() {
        return this.date2Long(this.finishStartTime);
    }


    public void setFinishStartTimeLong(Long finishStartTime) {
        this.finishStartTime = this.long2Date(finishStartTime);
    }


    public Long getFinishEndTimeLong() {
        return this.date2Long(this.finishEndTime);
    }


    public void setFinishEndTimeLong(Long finishEndTime) {
        this.finishEndTime = this.long2Date(finishEndTime);
    }


    //
    //public List<Integer> getOrderSourceList() {
    //    return orderSourceList;
    //}
    //
    //
    //public void setOrderSourceList(List<Integer> orderSourceList) {
    //    this.orderSourceList = orderSourceList;
    //}


}
