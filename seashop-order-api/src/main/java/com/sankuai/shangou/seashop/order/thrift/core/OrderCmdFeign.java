package com.sankuai.shangou.seashop.order.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.*;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.BatchDeliverOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.ReBuyBySellerReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.SellerRemarkReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.CreateOrderResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.ErpOrderDetailResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * 主要应用于下单场景
 */
@FeignClient(name = "himall-order", contextId = "OrderCmdThriftService", path = "/himall-order/orderCmd", url = "${himall-order.dev.url:}")
public interface OrderCmdFeign {

    /**
     * 创建订单
     * 订单服务的这个方法的前提是上游服务已经校验过数据的有效性了，订单服务只根据参数数据创建订单，不计算不校验
     */
    @PostMapping(value = "/createOrder", consumes = "application/json")
    ResultDto<CreateOrderResp> createOrder(@RequestBody CreateOrderReq orderReq) throws TException;

    /**
     * 取消支付
     */
    @PostMapping(value = "/cancelPay", consumes = "application/json")
    ResultDto<BaseResp> cancelPay(@RequestBody CancelPayReq cancelPayReq) throws TException;

    /**
     * 取消订单
     */
    @PostMapping(value = "/cancelOrder", consumes = "application/json")
    ResultDto<BaseResp> cancelOrder(@RequestBody CancelOrderReq cancelOrderReq) throws TException;

    /**
     * 再次购买
     * 用于订单列表的再次购买按钮，或者用户取消支付重新加购的场景
     */
    @PostMapping(value = "/reBuy", consumes = "application/json")
    ResultDto<BaseResp> reBuy(@RequestBody ReBuyReq reBuyReq) throws TException;

    /**
     * 修改收货人
     * 用于支付中状态时，商家修改收货地址
     */
    @PostMapping(value = "/updateReceiver", consumes = "application/json")
    ResultDto<BaseResp> updateReceiver(@RequestBody UpdateReceiverReq updateReceiverReq) throws TException;

    /**
     * 供应商修改收货人
     * 供应商修改商家的收货信息，与商家自己改接口隔离，校验对象不同
     */
    @PostMapping(value = "/updateReceiverBySeller", consumes = "application/json")
    ResultDto<BaseResp> updateReceiverBySeller(@RequestBody SellerUpdateReceiverReq updateReceiverReq) throws TException;

    /**
     * 供应商改价
     * 待付款状态时，供应商可以对订单商品总价进行修改
     */
    @PostMapping(value = "/updateItemAmount", consumes = "application/json")
    ResultDto<BaseResp> updateItemAmount(@RequestBody UpdateItemAmountReq updateItemAmountReq) throws TException;

    /**
     * 供应商修改运费
     * 待付款状态时，供应商可以对订单运费进行修改
     */
    @PostMapping(value = "/updateFreight", consumes = "application/json")
    ResultDto<BaseResp> updateFreight(@RequestBody UpdateFreightReq updateFreightReq) throws TException;

    /**
     * 买家延长收货
     * 待收货状态时，买家可以延长收货时间，一个订单只能延长一次
     */
    @PostMapping(value = "/delayReceive", consumes = "application/json")
    ResultDto<BaseResp> delayReceive(@RequestBody DelayReceiveReq delayReceiveReq) throws TException;

    /**
     * 买家确认收货
     * 待收货状态时，买家确认收货
     */
    @PostMapping(value = "/confirmReceive", consumes = "application/json")
    ResultDto<BaseResp> confirmReceive(@RequestBody ConfirmReceiveReq confirmReceiveReq) throws TException;

    /**
     * erp确认收货
     * 待收货状态时，erp确认收货
     */
    @PostMapping(value = "/confirmErpReceive", consumes = "application/json")
    ResultDto<ErpOrderDetailResp> confirmErpReceive(@RequestBody ConfirmReceiveReq req) throws TException;

    /**
     * 订单发货
     * 供应商操作订单发货
     */
    @PostMapping(value = "/deliverOrder", consumes = "application/json")
    ResultDto<BaseResp> deliverOrder(@RequestBody DeliverOrderReq deliverOrderReq) throws TException;

    /**
     * 订单批量发货
     * 供应商操作订单批量发货
     */
    @PostMapping(value = "/batchDeliverOrder", consumes = "application/json")
    ResultDto<BaseResp> batchDeliverOrder(@RequestBody BatchDeliverOrderReq orderDeliveryReq) throws TException;

    /**
     * 供应商修改运单号
     */
    @PostMapping(value = "/updateExpress", consumes = "application/json")
    ResultDto<BaseResp> updateExpress(@RequestBody UpdateExpressReq updateExpressReq) throws TException;


    /**
     * 订单新增物流运单信息
     */
    @PostMapping(value = "/addExpress", consumes = "application/json")
    ResultDto<BaseResp> addExpress(@RequestBody UpdateExpressReq req) throws TException;


    /**
     * 供应商添加订单备注
     */
    @PostMapping(value = "/sellerRemark", consumes = "application/json")
    ResultDto<BaseResp> sellerRemark(@RequestBody SellerRemarkReq remarkReq) throws TException;

    /**
     * 供应商取消订单
     */
    @PostMapping(value = "/sellerCancelOrder", consumes = "application/json")
    ResultDto<BaseResp> sellerCancelOrder(@RequestBody CancelOrderReq cancelOrderReq) throws TException;

    /**
     * 供应商给用户重新加入购物车
     * 供应商取消订单后，可以选择将取消的订单重新加入用户购物车
     */
    @PostMapping(value = "/reBuyBySeller", consumes = "application/json")
    ResultDto<BaseResp> reBuyBySeller(@RequestBody ReBuyBySellerReq reBuyBySellerReq) throws TException;


    @PostMapping(value = "/initOrderES", consumes = "application/json")
    ResultDto<BaseResp> initOrderES() throws TException;

    @PostMapping(value = "/initOrderRefundES", consumes = "application/json")
    ResultDto<BaseResp> initOrderRefundES() throws TException;

    @PostMapping(value = "/initProductCommentES", consumes = "application/json")
    ResultDto<BaseResp> initProductCommentES() throws TException;

    @GetMapping(value = "/queryWxStatus")
    ResultDto<Boolean> queryWxStatus(@RequestParam String thirdTransactionNo);

    @PostMapping(value = "/confirmReceiptWx", consumes = "application/json")
    ResultDto<BaseResp> confirmReceiptWx(@RequestBody @Valid ConfirmReceiptWxReq req);
}
