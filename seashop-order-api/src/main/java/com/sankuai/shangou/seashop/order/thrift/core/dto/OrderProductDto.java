package com.sankuai.shangou.seashop.order.thrift.core.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "订单商品对象")
@ToString
@Data
public class OrderProductDto extends BaseThriftDto {

    @FieldDoc(description = "商品ID")
    private Long productId;
    @FieldDoc(description = "商品名称")
    private String productName;
    @FieldDoc(description = "商品SKUID")
    private String skuId;
    @FieldDoc(description = "数量")
    private Long quantity;

    @FieldDoc(description = "商品主图")
    private String mainImagePath;
    @FieldDoc(description = "sku库存")
    private Long skuStock;
    @FieldDoc(description = "原售价")
    private BigDecimal originSalePrice;
    @FieldDoc(description = "折扣售价")
    private BigDecimal discountSalePrice;
    @FieldDoc(description = "实际售价，基于专享价和阶梯价得到")
    private BigDecimal realSalePrice;
    @FieldDoc(description = "商品总金额")
    private BigDecimal totalAmount;
    @FieldDoc(description = "颜色")
    private String color;
    @FieldDoc(description = "尺码")
    private String size;
    @FieldDoc(description = "版本")
    private String version;
    @FieldDoc(description = "sku自增id")
    private Long skuAutoId;
    @FieldDoc(description = "商品类目id")
    private Long categoryId;
    /**
     * 最终售价。如果没有折扣，则=realSalePrice，如果有折扣，则=discountSalePrice
     */
    @FieldDoc(description = "最终售价。如果没有折扣，则=realSalePrice，如果有折扣，则=discountSalePrice")
    private BigDecimal finalSalePrice;
    // 折扣活动ID，trade服务才会处理优惠，所以在这边处理分摊
    @FieldDoc(description = "折扣活动ID")
    private Long discountActivityId;
    // 均摊后的满减金额
    @FieldDoc(description = "均摊后的满减金额")
    private BigDecimal splitReductionAmount;
    // 均摊后的折扣金额，折扣的均摊在计算折扣的时候就处理了
    @FieldDoc(description = "均摊后的折扣金额")
    private BigDecimal splitDiscountAmount;
    // 均摊后的优惠券金额
    @FieldDoc(description = "均摊后的优惠券金额")
    private BigDecimal splitCouponAmount;
    // 如果该商品需要均摊优惠券，即商品在优惠券适用范围内，则会赋值
    @FieldDoc(description = "优惠券ID")
    private Long couponId;
    // 满减活动ID
    @FieldDoc(description = "满减活动ID")
    private Long reductionActivityId;
    @FieldDoc(description = "sku")
    private String sku;


    public String getOriginSalePriceString() {
        return this.bigDecimal2String(this.originSalePrice);
    }


    public void setOriginSalePriceString(String originSalePrice) {
        this.originSalePrice = this.string2BigDecimal(originSalePrice);
    }


    public String getDiscountSalePriceString() {
        return this.bigDecimal2String(this.discountSalePrice);
    }


    public void setDiscountSalePriceString(String discountSalePrice) {
        this.discountSalePrice = this.string2BigDecimal(discountSalePrice);
    }


    public String getRealSalePriceString() {
        return this.bigDecimal2String(this.realSalePrice);
    }


    public void setRealSalePriceString(String realSalePrice) {
        this.realSalePrice = this.string2BigDecimal(realSalePrice);
    }


    public String getTotalAmountString() {
        return this.bigDecimal2String(this.totalAmount);
    }


    public void setTotalAmountString(String totalAmount) {
        this.totalAmount = this.string2BigDecimal(totalAmount);
    }


    public String getFinalSalePriceString() {
        return this.bigDecimal2String(this.finalSalePrice);
    }


    public void setFinalSalePriceString(String finalSalePrice) {
        this.finalSalePrice = this.string2BigDecimal(finalSalePrice);
    }


    public String getSplitReductionAmountString() {
        return this.bigDecimal2String(this.splitReductionAmount);
    }


    public void setSplitReductionAmountString(String splitReductionAmount) {
        this.splitReductionAmount = this.string2BigDecimal(splitReductionAmount);
    }


    public String getSplitDiscountAmountString() {
        return this.bigDecimal2String(this.splitDiscountAmount);
    }


    public void setSplitDiscountAmountString(String splitDiscountAmount) {
        this.splitDiscountAmount = this.string2BigDecimal(splitDiscountAmount);
    }


    public String getSplitCouponAmountString() {
        return this.bigDecimal2String(this.splitCouponAmount);
    }


    public void setSplitCouponAmountString(String splitCouponAmount) {
        this.splitCouponAmount = this.string2BigDecimal(splitCouponAmount);
    }


}
