package com.sankuai.shangou.seashop.user.account.service.impl;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.user.dao.account.repository.MemberLabelRepository;
import org.springframework.stereotype.Service;

import com.github.pagehelper.Page;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminProcess;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.user.account.service.LabelService;
import com.sankuai.shangou.seashop.user.common.enums.UserResultCodeEnum;
import com.sankuai.shangou.seashop.user.dao.account.domain.Label;
import com.sankuai.shangou.seashop.user.dao.account.model.LabelExtModel;
import com.sankuai.shangou.seashop.user.dao.account.repository.LabelRepository;
import com.sankuai.shangou.seashop.user.thrift.account.request.CmdLabelReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryLabelPageReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.LabelResp;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @description: 标签服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class LabelServiceImpl implements LabelService {

    @Resource
    private LabelRepository labelRepository;

    @Resource
    private MemberLabelRepository memberLabelRepository;

    @Override
    @ExaminProcess(actionName = "添加标签",
        processModel = ExaminModelEnum.USER, processType = ExaProEnum.INSERT,
        repository = "labelRepository", serviceMethod = "addLabel",
        dto = CmdLabelReq.class, entity = Label.class)
    public Long addLabel(CmdLabelReq cmdLabelPageReq) {
        checkLabelName(cmdLabelPageReq);
        Label label = new Label();
        label.setLabelName(cmdLabelPageReq.getLabelName());
        label.setCreateTime(new Date());
        label.setCreateUser(cmdLabelPageReq.getOperationUserId());
        label.setUpdateTime(new Date());
        label.setUpdateUser(cmdLabelPageReq.getOperationUserId());
        labelRepository.addLabel(label);
        return label.getId();
    }

    @Override
    @ExaminProcess(actionName = "编辑标签",
        processModel = ExaminModelEnum.USER, processType = ExaProEnum.MODIFY,
        repository = "labelRepository", serviceMethod = "editLabel",
        dto = CmdLabelReq.class, entity = Label.class)
    public Long editLabel(CmdLabelReq cmdLabelPageReq) {
        checkLabelName(cmdLabelPageReq);
        Label label = labelRepository.getOneById(cmdLabelPageReq.getLabelId());
        if (label == null) {
            throw new BusinessException(UserResultCodeEnum.LABEL_NOT_EXIST);
        }
        label.setLabelName(cmdLabelPageReq.getLabelName());
        label.setUpdateUser(cmdLabelPageReq.getOperationUserId());
        label.setUpdateTime(new Date());
        labelRepository.editLabel(label);
        return label.getId();
    }

    public void checkLabelName(CmdLabelReq cmdLabelReq) {
        List<Label> labelList = labelRepository.lambdaQuery()
            .eq(Label::getLabelName, cmdLabelReq.getLabelName())
            .ne(cmdLabelReq.getLabelId() != null && cmdLabelReq.getLabelId() > 0L, Label::getId, cmdLabelReq.getLabelId())
            .list();

        if (!CollUtil.isEmpty(labelList)) {
            throw new BusinessException(UserResultCodeEnum.LABEL_NAME_EXIST);
        }
    }

    @Override
    @ExaminProcess(actionName = "删除标签",
        processModel = ExaminModelEnum.USER, processType = ExaProEnum.MOVE,
        repository = "labelRepository", serviceMethod = "deleteLabel",
        dto = CmdLabelReq.class, entity = Label.class)
    public Long deleteLabel(CmdLabelReq cmdLabelPageReq) {
        // 开启事务 删除标签同时 删除绑定的会员标签
        TransactionHelper.doInTransaction(() -> {
            labelRepository.deleteLabel(cmdLabelPageReq.getLabelId());
            memberLabelRepository.deleteMemberLabelByLabelId(cmdLabelPageReq.getLabelId());
        });
        return cmdLabelPageReq.getLabelId();
    }

    @Override
    public BasePageResp<LabelResp> queryLabelPage(QueryLabelPageReq queryLabelPageReq) {
        Page<LabelExtModel> labels = labelRepository.queryLabelPage(queryLabelPageReq.getLabelName(), queryLabelPageReq.getPageNo(), queryLabelPageReq.getPageSize());
        return PageResultHelper.transfer(labels, LabelResp.class);
    }
}
