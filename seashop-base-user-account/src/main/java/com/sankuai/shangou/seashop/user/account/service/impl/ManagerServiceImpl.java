package com.sankuai.shangou.seashop.user.account.service.impl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;


import cn.hutool.core.util.ObjectUtil;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.user.dao.account.domain.Privilege;
import com.sankuai.shangou.seashop.user.dao.account.repository.PrivilegeRepository;
import com.sankuai.shangou.seashop.user.dao.account.repository.RolePrivilegeRepository;
import com.sankuai.shangou.seashop.user.thrift.account.dto.MenuDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryMenuAuthReq;

import com.sankuai.shangou.seashop.user.thrift.account.enums.MemberEnum;

import org.springframework.stereotype.Service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.log.assist.BaseLogAssist;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AesUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.user.account.dto.ShopMemberInfoBO;
import com.sankuai.shangou.seashop.user.account.log.ManagerLogBO;
import com.sankuai.shangou.seashop.user.account.service.ManagerService;
import com.sankuai.shangou.seashop.user.account.service.MemberContactService;
import com.sankuai.shangou.seashop.user.account.service.MemberService;
import com.sankuai.shangou.seashop.user.common.config.EncryptConfig;
import com.sankuai.shangou.seashop.user.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.user.common.enums.UserResultCodeEnum;
import com.sankuai.shangou.seashop.user.dao.account.domain.Manager;
import com.sankuai.shangou.seashop.user.dao.account.domain.Member;
import com.sankuai.shangou.seashop.user.dao.account.model.ManagerExtModel;
import com.sankuai.shangou.seashop.user.dao.account.model.ManagerQueryModel;
import com.sankuai.shangou.seashop.user.dao.account.repository.ManagerRepository;
import com.sankuai.shangou.seashop.user.dao.account.repository.MemberRepository;
import com.sankuai.shangou.seashop.user.dao.shop.domain.Shop;
import com.sankuai.shangou.seashop.user.dao.shop.repository.ShopRepository;
import com.sankuai.shangou.seashop.user.thrift.account.request.CmdManagerReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryManagerPageReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryManagerReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.EpManagerResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.ManagerResp;
import com.sankuai.shangou.seashop.user.thrift.shop.enums.ShopEnum;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class ManagerServiceImpl implements ManagerService {
    @Resource
    private ManagerRepository managerRepository;
    @Resource
    private MemberRepository memberRepository;
    @Resource
    private ShopRepository shopRepository;
    @Resource
    private BaseLogAssist baseLogAssist;
    @Resource
    private EncryptConfig encryptConfig;
    @Resource
    private MemberContactService memberContactService;
    @Resource
    private MemberService memberService;

    @Resource
    private PrivilegeRepository privilegeRepository;
    @Resource
    private RolePrivilegeRepository rolePrivilegeRepository;
    @Override
    public BasePageResp<ManagerResp> queryManagerPage(QueryManagerPageReq queryManagerPageReq) {
        Page<ManagerExtModel> managerPage = PageHelper.startPage(queryManagerPageReq.getPageNo(), queryManagerPageReq.getPageSize());
        ManagerQueryModel manager = new ManagerQueryModel();
        manager.setShopId(queryManagerPageReq.getShopId());
        manager.setManagerIds(queryManagerPageReq.getManagerIds());
        List<ManagerExtModel> managerList = managerRepository.selectExtList(manager);
        BasePageResp<ManagerResp> managerResp = PageResultHelper.transfer(managerPage, ManagerResp.class);
        if (CollUtil.isNotEmpty(managerResp.getData())) {
            //处理默认角色名
            managerResp.getData().forEach(v -> {
                //手机号码解密
                if (StrUtil.isNotBlank(manager.getCellphone())) {
                    manager.setCellphone(AesUtil.decrypt(manager.getCellphone(), encryptConfig.getAesSecret()));
                }
                if (Objects.equals(v.getRoleId(), CommonConstant.DEFAULT_LONG_ID)) {
                    v.setRoleName(CommonConstant.DEFAULT_ROLE_NAME);
                }
            });
        }
        return managerResp;
    }

    @Override
    public ManagerResp queryManager(QueryManagerReq copy) {
        Manager managerReq = new Manager();
        managerReq.setId(copy.getUserId());
        managerReq.setUserName(copy.getManagerName());
        managerReq.setShopId(copy.getOperationShopId());
        Manager manager = managerRepository.getByManager(managerReq);
        //手机号码解密
        if (manager != null && StrUtil.isNotBlank(manager.getCellphone())) {
            try {
                manager.setCellphone(AesUtil.decrypt(manager.getCellphone(), encryptConfig.getAesSecret()));
            } catch (Exception e) {
                log.error("手机号码解密失败", e);
            }
        }
        return JsonUtil.copy(manager, ManagerResp.class);
    }

    @Override
    public Long addManager(CmdManagerReq cmdManagerReq) {
//        判断用户名是否存在
//        检查入参
        cmdManagerReq.setId(null);
        checkReq(cmdManagerReq);

        String passwordSalt = RandomUtil.randomString(6);
        String password = this.encryptPassword(cmdManagerReq.getPassword(), passwordSalt);
        String orgPhone = cmdManagerReq.getCellphone();

        Manager manager = new Manager();
        //获取当前时间
        Date now = new Date();
        manager.setShopId(cmdManagerReq.getShopId());
        manager.setRoleId(cmdManagerReq.getRoleId());
        manager.setUserName(cmdManagerReq.getUserName());
        /*manager.setCellphone(AesUtil.encrypt(cmdManagerReq.getCellphone(), encryptConfig.getAesSecret()));*/
        manager.setCellphone(cmdManagerReq.getCellphone());
        manager.setRealName(cmdManagerReq.getRealName());
        manager.setRemark(cmdManagerReq.getRemark());
        manager.setPassword(password);
        manager.setPasswordSalt(passwordSalt);
        manager.setCreateTime(now);
        manager.setCreateUser(cmdManagerReq.getOperatorId());
        manager.setUpdateTime(now);
        manager.setUpdateUser(cmdManagerReq.getOperatorId());
        // 反向注册会员账号
//        if (cmdManagerReq.getShopId() > ShopEnum.PLATFORM) {
////            registerMemberIfNotExist(orgPhone,cmdManagerReq.getUserName());
//        }
        TransactionHelper.doInTransaction(() -> {
            managerRepository.insert(manager);
            baseLogAssist.recordLog(ExaminModelEnum.USER,
                    ExaProEnum.INSERT, "添加管理员",
                    cmdManagerReq.getOperationUserId(), cmdManagerReq.getOperationShopId(),
                    new ManagerLogBO(), new ManagerLogBO(cmdManagerReq));
        });
        return manager.getId();
    }

    /**
     * 加密密码
     *
     * @param password
     * @param passwordSalt
     * @return
     */
    private String encryptPassword(String password, String passwordSalt) {
        String newPassword = password + passwordSalt;
        return SecureUtil.sha256(newPassword);
    }

    private void checkReq(CmdManagerReq cmdManagerReq) {
        List<Manager> managerList = managerRepository.lambdaQuery()
                .eq(Manager::getUserName, cmdManagerReq.getUserName())
                .eq(cmdManagerReq.getId() != null, Manager::getId, cmdManagerReq.getId())
                .list();
        if (CollUtil.isNotEmpty(managerList)) {
            throw new BusinessException(UserResultCodeEnum.MANAGER_ALREADY_EXISTS);
        }
        checkPhone(cmdManagerReq);
    }

    //检查手机号
    public void checkPhone(CmdManagerReq cmdManagerReq) {
        if (StrUtil.isNotBlank(cmdManagerReq.getCellphone())) {
            //判断是否符合手机号格式
            if (!PhoneUtil.isMobile(cmdManagerReq.getCellphone())) {
                throw new BusinessException(UserResultCodeEnum.PHONE_FORMAT_ERROR);
            }
            //对比是不是同一个手机号码
            String encryCellphone = AesUtil.encrypt(cmdManagerReq.getCellphone(), encryptConfig.getAesSecret());
            List<Manager> managerList = managerRepository.lambdaQuery()
                    .eq(Manager::getCellphone, encryCellphone)
                    .ne(cmdManagerReq.getId() != null, Manager::getId, cmdManagerReq.getId())
                    .list();
            if (CollUtil.isNotEmpty(managerList)) {
                throw new BusinessException(UserResultCodeEnum.MANAGER_PHONE_ALREADY_EXISTS);
            }
        }
    }

    @Override
    public Long editManager(CmdManagerReq cmdManagerReq) {
        Manager manager = managerRepository.getById(cmdManagerReq.getId());
        if (manager == null) {
            throw new BusinessException(UserResultCodeEnum.NO_SUCH_USER);
        }
        checkPhone(cmdManagerReq);
        //记录日志
        ManagerLogBO old = new ManagerLogBO(manager);
        if (!StrUtil.isEmpty(cmdManagerReq.getPassword())) {
            manager.setPassword(this.encryptPassword(cmdManagerReq.getPassword(), manager.getPasswordSalt()));
        }
        /*manager.setCellphone(AesUtil.encrypt(cmdManagerReq.getCellphone(), encryptConfig.getAesSecret()));*/
        manager.setCellphone(cmdManagerReq.getCellphone());
        manager.setRoleId(cmdManagerReq.getRoleId());
        manager.setUserName(cmdManagerReq.getUserName());
        /*manager.setCellphone(AesUtil.encrypt(cmdManagerReq.getCellphone(), encryptConfig.getAesSecret()));*/
        manager.setCellphone(cmdManagerReq.getCellphone());
        managerRepository.updateById(manager);
        baseLogAssist.recordLog(ExaminModelEnum.USER,
                ExaProEnum.MODIFY, "编辑管理员",
                cmdManagerReq.getOperationUserId(), cmdManagerReq.getOperationShopId(),
                old, new ManagerLogBO(cmdManagerReq));

        // 反向注册会员账号
        String orgPhone = cmdManagerReq.getCellphone();
        if (cmdManagerReq.getShopId() > ShopEnum.PLATFORM) {
            registerMemberIfNotExist(orgPhone);
        }
        return cmdManagerReq.getId();
    }

    private void registerMemberIfNotExist(String orgPhone,String userName) {
        String encryptPhone = AesUtil.encrypt(orgPhone, encryptConfig.getAesSecret());
        Member member = memberRepository.getByPhone(encryptPhone);
        Member member1 = memberRepository.getByUserName(userName);
        if (Objects.isNull(member) && Objects.isNull(member1)) {
            memberService.autoRegister(orgPhone, userName, MemberEnum.PlatformType.PC);
        }
        else{
            throw new BusinessException("添加失败，手机号或者名称已存在");
        }
    }
    private void registerMemberIfNotExist(String orgPhone) {
        String encryptPhone = AesUtil.encrypt(orgPhone, encryptConfig.getAesSecret());
        Member member = memberRepository.getByPhone(encryptPhone);
        if (Objects.isNull(member)) {
            memberService.autoRegister(orgPhone, null, null, MemberEnum.PlatformType.PC);
        }
    }
    @Override
    public Long deleteManager(CmdManagerReq cmdManagerReq) {
        Manager manager = managerRepository.getById(cmdManagerReq.getId());
        ManagerLogBO managerLogBO = new ManagerLogBO(manager);

        managerRepository.removeById(manager);
        // 手动写日志
        baseLogAssist.recordLog(ExaminModelEnum.USER, ExaProEnum.MOVE, "删除管理员",
                cmdManagerReq.getOperationUserId(), cmdManagerReq.getOperationShopId(),
                managerLogBO, new ManagerLogBO(manager));
        return cmdManagerReq.getId();
    }

    @Override
    public Integer batchDeleteManager(CmdManagerReq cmdManagerReq) {
        if (!CollectionUtil.isEmpty(cmdManagerReq.getBatchId())) {
            return managerRepository.deleteBatchIds(cmdManagerReq.getBatchId());
        }
        return 0;
    }

    @Override
    public EpManagerResp queryEpManager(Integer id) {
        if (id == null) {
            return null;
        }
        Member member = new Member();
        //判断是否有这个商家
        Member realMember = memberRepository.queryMemberByEpAccountId(member);
        EpManagerResp epManagerResp = new EpManagerResp();
        Manager manager = null;
        if (realMember != null) {
            epManagerResp.setUserId(realMember.getId());
            epManagerResp.setRealName(realMember.getRealName());
            epManagerResp.setUserName(realMember.getUserName());
            epManagerResp.setWeiXinOpenId(realMember.getOpenId());
            epManagerResp.setUserFreeze(realMember.getDisabled());
            epManagerResp.setCellphone(memberContactService.queryPhone(realMember.getId()));
            manager = managerRepository.getByUserName(realMember.getUserName());
        }
        //判断是否有这个商家管理员
        if (manager == null) {
            return epManagerResp;
        }
        epManagerResp.setManagerId(manager.getId());
        epManagerResp.setManagerName(manager.getUserName());
        epManagerResp.setRoleId(manager.getRoleId());
        //判断是否有这个供应商
        Shop shop = shopRepository.getById(manager.getShopId());
        if (shop == null || !(ShopEnum.AuditStatus.Open.getCode().equals(shop.getShopStatus()) || ShopEnum.AuditStatus.Freeze.getCode().equals(shop.getShopStatus()))) {
            return epManagerResp;
        }
        epManagerResp.setShopId(shop.getId());
        epManagerResp.setShopName(shop.getShopName());
        epManagerResp.setShopFreeze(ShopEnum.AuditStatus.Freeze.getCode().equals(shop.getShopStatus()));
        return epManagerResp;
    }

    @Override
    public ShopMemberInfoBO queryEpManagerByShopId(Long id) {
        ShopMemberInfoBO memberInfoBO = new ShopMemberInfoBO();
        //查询这个店铺的超管
        Manager manager = managerRepository.lambdaQuery().eq(Manager::getShopId, id).eq(Manager::getRoleId, CommonConstant.DEFAULT_LONG_ID).one();
        if (manager == null) {
            throw new BusinessException(UserResultCodeEnum.SHOP_NOT_FIND);
        }
        //查出超管的商家信息
        Member member = memberRepository.queryMemberByUserName(manager.getUserName());
        if (member == null) {
            throw new BusinessException(UserResultCodeEnum.SHOP_NOT_FIND);
        }

        memberInfoBO.setMemberId(member.getId());
        memberInfoBO.setMemberName(member.getUserName());
        memberInfoBO.setAvatar(member.getPhoto());
        memberInfoBO.setRealName(member.getRealName());
        memberInfoBO.setPhone(memberContactService.queryPhone(member.getId()));
        memberInfoBO.setEmail(memberContactService.queryEmail(member.getId()));
        memberInfoBO.setShopId(id);
        memberInfoBO.setManagerId(manager.getId());
        return memberInfoBO;
    }

    @Override
    public EpManagerResp getEpManagerByName(Member member, Manager manager) {
        EpManagerResp epManagerResp = new EpManagerResp();
        if (member != null) {
            epManagerResp.setUserId(member.getId());
            epManagerResp.setRealName(member.getRealName());
            epManagerResp.setUserName(member.getUserName());
            epManagerResp.setWeiXinOpenId(member.getOpenId());
            epManagerResp.setUserFreeze(member.getDisabled());
            epManagerResp.setCellphone(AesUtil.decrypt(member.getCellPhone(), encryptConfig.getAesSecret()));
        }
        if (manager != null) {
            epManagerResp.setManagerId(manager.getId());
            epManagerResp.setManagerName(manager.getUserName());
            epManagerResp.setRoleId(manager.getRoleId());
            //判断是否有这个供应商
            Shop shop = shopRepository.getById(manager.getShopId());
            if (shop == null || !(ShopEnum.AuditStatus.Open.getCode().equals(shop.getShopStatus()) || ShopEnum.AuditStatus.Freeze.getCode().equals(shop.getShopStatus()))) {
                return epManagerResp;
            }
            epManagerResp.setShopId(shop.getId());
            epManagerResp.setShopName(shop.getShopName());
            epManagerResp.setShopFreeze(ShopEnum.AuditStatus.Freeze.getCode().equals(shop.getShopStatus()));
        }
        return epManagerResp;
    }

    @Override
    public List<MenuDto> queryMenuAuth(QueryMenuAuthReq req) {
        Manager manager = managerRepository.getById(req.getManagerId());
        AssertUtil.throwIfNull(manager, UserResultCodeEnum.USER_NOT_EXIST);

        // 获取菜单权限
        List<Privilege> privilegeList = getPrivilegeByRoleId(manager.getRoleId());

        List<MenuDto> menuList = privilegeList.stream().map(this::convertToMenuDto).collect(Collectors.toList());
        Map<Long, List<MenuDto>> menuMap = menuList.stream().collect(Collectors.groupingBy(MenuDto::getInnerParentId));
        return menuList.stream()
                .peek(menu -> setSubMenu(menu, menuMap))
                .filter(menu -> CommonConstant.DEFAULT_PARENT_ID.equals(menu.getInnerParentId()))
                .sorted(Comparator.comparing(MenuDto::getDisplaySequence))
                .collect(Collectors.toList());
    }

    private List<Privilege> getPrivilegeByRoleId(Long roleId) {
        // 如果是超管, 返回所有的权限
        if (CommonConstant.SUPER_ADMIN_ROLE_ID.equals(roleId)) {
            return privilegeRepository.list();
        }

        // 获取该用户拥有的权限的集合
        List<Long> authPrivilegeIds = rolePrivilegeRepository.getPrivilegeIdByRoleId(roleId);
        // 没有任何权限. 直接返回
        if (CollUtil.isEmpty(authPrivilegeIds)) {
            return Collections.emptyList();
        }

        // 提取具有权限的整个链路的权限，比如有第三级权限，需要把当前权限所在的父级权限也提取出来，用于构建权限树
        return getAllAuthPrivilegeList(authPrivilegeIds);
    }

    private List<Privilege> getAllAuthPrivilegeList(List<Long> authPrivilegeIds) {
        if (CollUtil.isEmpty(authPrivilegeIds)) {
            return Collections.emptyList();
        }

        // 读取所有的菜单权限
        List<Privilege> privilegeList = privilegeRepository.list();
        Map<Long, Privilege> privilegeMap = privilegeList.stream().collect(Collectors.toMap(Privilege::getId, Function.identity(), (k1, k2) -> k1));
        Map<Long, List<Privilege>> childPrivilegeIdMap = privilegeList.stream().collect(Collectors.groupingBy(Privilege::getParentId));

        Set<Privilege> allPrivilegeList = new HashSet<>();
        // 添加所有授权权限及其递归父级和子级菜单
        authPrivilegeIds.forEach(privilegeId -> {
            Privilege privilege = privilegeMap.get(privilegeId);
            if (privilege == null) {
                log.warn("权限不存在，id={}", privilegeId);
                return;
            }

            allPrivilegeList.add(privilege);
            addRecursiveParents(privilege, allPrivilegeList, privilegeMap);
            addRecursiveChildren(privilege, allPrivilegeList, childPrivilegeIdMap);
        });

        return new ArrayList<>(allPrivilegeList);
    }

    private void addRecursiveParents(Privilege privilege, Set<Privilege> allPrivilegeList, Map<Long, Privilege> privilegeMap) {
        Long parentId = privilege.getParentId();
        if (parentId == null || CommonConstant.DEFAULT_PARENT_ID.equals(parentId)) {
            return;
        }

        Privilege parent = privilegeMap.get(parentId);
        if (parent != null) {
            allPrivilegeList.add(parent);
            addRecursiveParents(parent, allPrivilegeList, privilegeMap);
        }
    }

    private void addRecursiveChildren(Privilege privilege, Set<Privilege> allPrivilegeList, Map<Long, List<Privilege>> childPrivilegeIdMap) {
        List<Privilege> children = childPrivilegeIdMap.get(privilege.getId());
        if (CollUtil.isEmpty(children)) {
            return;
        }

        for (Privilege child : children) {
            allPrivilegeList.add(child);
            addRecursiveChildren(child, allPrivilegeList, childPrivilegeIdMap);
        }
    }

    private void setSubMenu(MenuDto menu, Map<Long, List<MenuDto>> menuMap) {
        List<MenuDto> subMenus = menuMap.get(menu.getInnerId());
        // 根据 displaySequence 升序
        if (CollUtil.isNotEmpty(subMenus)) {
            subMenus.sort(Comparator.comparing(MenuDto::getDisplaySequence));
        }
        menu.setMenus(subMenus);
    }

    private MenuDto convertToMenuDto(Privilege privilege) {
        MenuDto menuDto = new MenuDto();
        menuDto.setId(privilege.getCode());
        menuDto.setName(privilege.getName());
        menuDto.setPath(privilege.getUrl());
        menuDto.setIcon(privilege.getIcon());
        menuDto.setIzTab(privilege.getIzTab());
        menuDto.setInnerId(privilege.getId());
        menuDto.setInnerParentId(privilege.getParentId());
        menuDto.setDisplaySequence(ObjectUtil.defaultIfNull(privilege.getDisplaySequence(), 0));
        return menuDto;
    }

}
