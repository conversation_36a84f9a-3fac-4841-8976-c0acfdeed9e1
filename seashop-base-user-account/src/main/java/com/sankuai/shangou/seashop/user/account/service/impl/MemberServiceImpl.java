package com.sankuai.shangou.seashop.user.account.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.constant.LoginConstant;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.log.assist.BaseLogAssist;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.base.boot.response.*;
import com.sankuai.shangou.seashop.base.boot.utils.AesUtil;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.base.core.service.SiteSettingService;
import com.sankuai.shangou.seashop.base.leaf.LeafService;
import com.sankuai.shangou.seashop.base.security.handler.BaseLoginService;
import com.sankuai.shangou.seashop.base.security.utils.FoxUtl;
import com.sankuai.shangou.seashop.base.thrift.core.MessageCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.ContactReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SmsBodyReq;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.order.thrift.core.OrderQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderItemInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.RefundEventEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderDetailResp;
import com.sankuai.shangou.seashop.product.thrift.core.ProductQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductPageResp;
import com.sankuai.shangou.seashop.user.account.log.MemberLogBO;
import com.sankuai.shangou.seashop.user.account.log.MemberLogListBO;
import com.sankuai.shangou.seashop.user.account.mq.model.CreateMemberMsg;
import com.sankuai.shangou.seashop.user.account.mq.model.OrderChangeMsg;
import com.sankuai.shangou.seashop.user.account.mq.model.OrderRefundMsg;
import com.sankuai.shangou.seashop.user.account.mq.model.UpdateMemberMsg;
import com.sankuai.shangou.seashop.user.account.service.MemberBuyCategoryService;
import com.sankuai.shangou.seashop.user.account.service.MemberContactService;
import com.sankuai.shangou.seashop.user.account.service.MemberLabelService;
import com.sankuai.shangou.seashop.user.account.service.MemberService;
import com.sankuai.shangou.seashop.user.account.service.register.MemberRegisterService;
import com.sankuai.shangou.seashop.user.account.service.register.RegisterStrategyContext;
import com.sankuai.shangou.seashop.user.account.service.register.RegisterTypeEnum;
import com.sankuai.shangou.seashop.user.common.config.EncryptConfig;
import com.sankuai.shangou.seashop.user.common.config.UserLionConfigClient;
import com.sankuai.shangou.seashop.user.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.user.common.constant.LeafConstant;
import com.sankuai.shangou.seashop.user.common.enums.UserResultCodeEnum;
import com.sankuai.shangou.seashop.user.common.enums.account.ContactUserTypeEnum;
import com.sankuai.shangou.seashop.user.dao.account.domain.*;
import com.sankuai.shangou.seashop.user.dao.account.model.QueryMemberListModel;
import com.sankuai.shangou.seashop.user.dao.account.repository.ManagerRepository;
import com.sankuai.shangou.seashop.user.dao.account.repository.MemberContactRepository;
import com.sankuai.shangou.seashop.user.dao.account.repository.MemberRepository;
import com.sankuai.shangou.seashop.user.dao.shop.repository.ShopRepository;
import com.sankuai.shangou.seashop.user.thrift.account.enums.MemberContactEnum;
import com.sankuai.shangou.seashop.user.thrift.account.enums.MemberEnum;
import com.sankuai.shangou.seashop.user.thrift.account.request.*;
import com.sankuai.shangou.seashop.user.thrift.account.response.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.shangou.seashop.base.boot.constant.LoginConstant.DEFAULT_PASSWORD;

/**
 * @description: 商家服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class MemberServiceImpl implements MemberService {
    @Resource
    private MemberRepository memberRepository;
    @Resource
    private MemberLabelService memberLabelService;
    @Resource
    private MemberContactRepository memberContactRepository;
    @Resource
    private MemberContactService memberContactService;
    @Resource
    private MemberBuyCategoryService memberBuyCategoryService;
    @Resource
    private MessageCMDFeign messageCMDFeign;
    @Resource
    private ShopRepository shopRepository;
    @Resource
    private ManagerRepository managerRepository;
    @Resource
    private EncryptConfig encryptConfig;
    @Resource
    private BaseLogAssist baseLogAssist;
    @Resource
    private UserLionConfigClient userLionConfigClient;
    @Resource
    private RegisterStrategyContext registerStrategyContext;

    @Resource
    private LeafService leafService;
    @Resource
    private OrderQueryFeign orderQueryFeign;
    @Resource
    private ProductQueryFeign productQueryFeign;
    @Resource
    private SiteSettingService siteSettingService;
    @Resource
    private BaseLoginService baseLoginService;

    private final static String DEFAULT_ENCRYPTION_MODE = "SHA-256";
    private final static String DEFAULT_USER_PHOTO_KEY = "defaultUserPhoto";

    @Override
    public BasePageResp<MemberResp> queryMemberPage(QueryMemberPageReq queryMemberPageReq) {
        QueryMemberListModel queryMemberListDto = JsonUtil.copy(queryMemberPageReq, QueryMemberListModel.class);
        if (StrUtil.isNotBlank(queryMemberListDto.getMobile())) {
            queryMemberListDto.setMobile(AesUtil.encrypt(queryMemberListDto.getMobile(), encryptConfig.getAesSecret()));
        }
        //如果排序字段不为空
        if (!CollectionUtil.isEmpty(queryMemberPageReq.getSortList())) {
            //遍历排序字段
            for (FieldSortReq fieldSortReq : queryMemberPageReq.getSortList()) {
                if (StrUtil.isEmpty(fieldSortReq.getSort())) {
                    continue;
                }

                //排序字段名加um.前缀
//                驼峰风格转化为下划线风格
                fieldSortReq.setSort(StrUtil.toUnderlineCase(fieldSortReq.getSort()));
                fieldSortReq.setSort("um." + fieldSortReq.getSort());
            }
//            过滤排序字段为空的数据
            queryMemberPageReq.setSortList(CollectionUtil.filter(queryMemberPageReq.getSortList(), fieldSortReq -> StrUtil.isNotBlank(fieldSortReq.getSort())));
            //设置排序字段
            queryMemberListDto.setSortField(MybatisUtil.getOrderSql(queryMemberPageReq.getSortList()));
        }
        Page<MemberExt> memberPage = PageHelper.startPage(queryMemberPageReq.getPageNo(), queryMemberPageReq.getPageSize());
        List<MemberExt> memberPageInfo = memberRepository.queryMemberPage(queryMemberListDto);
        dealExtCellPhone(memberPageInfo);
        return PageResultHelper.transfer(memberPage, MemberResp.class);
    }

    @Override
    public MemberResp queryMember(QueryMemberReq queryMemberReq) {
        Member member = new Member();
        member.setId(queryMemberReq.getId());
        member.setUserName(queryMemberReq.getMemberName());
        Member newMember = memberRepository.queryMember(member);
        if (newMember == null) {
            return null;
        }
        dealCellPhone(newMember);
//        查询邮箱
        MemberResp memberResp = JsonUtil.copy(newMember, MemberResp.class);
        memberResp.setEmail(memberContactService.queryEmail(queryMemberReq.getId()));
        return memberResp;
    }

    @Override
    public BaseResp updateOrderStatus(OrderChangeMsg body) {
        QueryOrderDetailReq queryOrderDetailReq = new QueryOrderDetailReq();
        queryOrderDetailReq.setOrderId(body.getOrderId());
        OrderDetailResp orderDetailResp = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.queryDetail(queryOrderDetailReq));
        //判断订单事件类型
        OrderMessageEventEnum messageEventEnum = OrderMessageEventEnum.valueOf(body.getOrderEventName());
        //获取商家ID
        Long userId = orderDetailResp.getOrderInfo().getUserId();
        //获取商家信息
        Member member = memberRepository.getById(userId);
//        根据订单状态发送处理商家信息
        switch (messageEventEnum) {
            case CREATE_ORDER: //待付款
//                增加下单数
                member.setOrderNumber(member.getOrderNumber() + 1);
                memberRepository.updateById(member);
                break;
            case PAY_SUCCESS: //代发货
//                增加消费金额
                member.setTotalAmount(NumberUtil.add(member.getTotalAmount(), orderDetailResp.getOrderInfo().getTotalAmount()));
//                增加净消费金额
                member.setNetAmount(NumberUtil.add(member.getNetAmount(), orderDetailResp.getOrderInfo().getTotalAmount()));
//                设置最后消费时间
                member.setLastConsumptionTime(orderDetailResp.getOrderInfo().getPayDate());
//                购买过类目加记录
//                获取订单详情
                List<OrderItemInfoDto> orderDetailList = orderDetailResp.getOrderInfo().getItemList();
//                判空
                if (CollectionUtil.isEmpty(orderDetailList)) {
                    break;
                }
//                遍历订单详情， 获取所有商品id
                List<Long> productIdList = orderDetailList.stream().map(OrderItemInfoDto::getProductId).map(Long::parseLong).collect(Collectors.toList());
//                获取所有商品
                QueryProductReq queryProductReq = new QueryProductReq();
                queryProductReq.setProductIds(productIdList);
                //查询所有数据
                queryProductReq.setPageSize(productIdList.size());
                queryProductReq.setPageSize(1);
                BasePageResp<ProductPageResp> pageResp = ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.queryProduct(queryProductReq));
                List<ProductPageResp> productCategoryList = pageResp.getData();
//                判空
                if (CollectionUtil.isEmpty(productCategoryList)) {
                    break;
                }
//                获取所有类目id
                List<Long> categoryIdList = new ArrayList<>();
                //获取这些类目的一级类目
                List<List<Long>> firstCategoryIdList = productCategoryList.stream().map(ProductPageResp::getCategoryIds).collect(Collectors.toList());
                firstCategoryIdList.forEach(v -> {
                    if (CollUtil.isNotEmpty(v)) {
                        categoryIdList.add(v.get(0));
                    }
                });
                TransactionHelper.doInTransaction(() -> {
                    memberRepository.updateById(member);
                    memberBuyCategoryService.addMemberBuyCategory(userId, categoryIdList);
                });
                break;
            default:
                break;
        }
        return BaseResp.of();
    }

    @Override
    public BaseResp updateRefundOrderStatus(OrderRefundMsg body) {
        QueryOrderDetailReq queryOrderDetailReq = new QueryOrderDetailReq();
        queryOrderDetailReq.setOrderId(body.getOrderId());
        OrderDetailResp orderDetailResp = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.queryDetail(queryOrderDetailReq));
        //判断订单状态
        RefundEventEnum refundEventEnum = RefundEventEnum.valueOf(body.getRefundEventName());
        //获取商家ID
        Long userId = orderDetailResp.getOrderInfo().getUserId();
        //获取商家信息
        Member member = memberRepository.getById(userId);
//        根据订单状态发送处理商家信息
        switch (refundEventEnum) {
            case REFUND_SUCCESS: //待付款
                member.setNetAmount(NumberUtil.sub(member.getNetAmount(), orderDetailResp.getOrderInfo().getTotalAmount()));
                break;
            default:
                break;
        }
        return BaseResp.of();
    }

    //    商家分组
    @Override
    public MemberGroupingResp queryMemberGrouping() {
        MemberGroupingResp memberGroupingResp = new MemberGroupingResp();
        //当前时间
        Date date = DateUtil.beginOfDay(new Date());
        //获取当前时间月份和日期
        String monthDay = DateUtil.format(date, "MM-dd");
        //获取本月月份
        String month = DateUtil.format(date, "MM");
        //获取下月月份
        String nextMonth = DateUtil.format(DateUtil.offsetMonth(date, 1), "MM");
        //一个月前的时间
        Date oneMonthAgo = DateUtils.addDays(date, -1 * 30);
        //三个月前的时间
        Date threeMonthAgo = DateUtils.addDays(date, -3 * 30);
        //六个月前的时间
        Date sixMonthAgo = DateUtils.addDays(date, -6 * 30);
        //九个月前的时间
        Date nineMonthAgo = DateUtils.addDays(date, -9 * 30);
        //十二个月前的时间
        Date twelveMonthAgo = DateUtils.addDays(date, -12 * 30);
        //二十四个月前的时间
        Date twentyFourMonthAgo = DateUtils.addDays(date, -24 * 30);
        //1个月活跃商家
        memberGroupingResp.setOneMonthActiveMerchant(Math.toIntExact(memberRepository.lambdaQuery().eq(Member::getWhetherLogOut, 0).between(Member::getLastConsumptionTime, oneMonthAgo, date).count()));
        //3个月活跃商家
        memberGroupingResp.setThreeMonthActiveMerchant(Math.toIntExact(memberRepository.lambdaQuery().eq(Member::getWhetherLogOut, 0).between(Member::getLastConsumptionTime, threeMonthAgo, oneMonthAgo).count()));
        //6个月活跃商家
        memberGroupingResp.setSixMonthActiveMerchant(Math.toIntExact(memberRepository.lambdaQuery().eq(Member::getWhetherLogOut, 0).between(Member::getLastConsumptionTime, sixMonthAgo, threeMonthAgo).count()));
        //算前面几个的总和
        memberGroupingResp.setActiveMerchant(memberGroupingResp.getOneMonthActiveMerchant() + memberGroupingResp.getThreeMonthActiveMerchant() + memberGroupingResp.getSixMonthActiveMerchant());
        //3个月沉睡商家
        memberGroupingResp.setThreeMonthSleepMerchant(Math.toIntExact(memberRepository.lambdaQuery().eq(Member::getWhetherLogOut, 0).le(Member::getLastConsumptionTime, threeMonthAgo).ge(Member::getLastConsumptionTime, sixMonthAgo).count()));
        //6个月沉睡商家
        memberGroupingResp.setSixMonthSleepMerchant(Math.toIntExact(memberRepository.lambdaQuery().eq(Member::getWhetherLogOut, 0).le(Member::getLastConsumptionTime, sixMonthAgo).ge(Member::getLastConsumptionTime, nineMonthAgo).count()));
        //9个月沉睡商家
        memberGroupingResp.setNineMonthSleepMerchant(Math.toIntExact(memberRepository.lambdaQuery().eq(Member::getWhetherLogOut, 0).le(Member::getLastConsumptionTime, nineMonthAgo).ge(Member::getLastConsumptionTime, twelveMonthAgo).count()));
        //12个月沉睡商家
        memberGroupingResp.setTwelveMonthSleepMerchant(Math.toIntExact(memberRepository.lambdaQuery().eq(Member::getWhetherLogOut, 0).le(Member::getLastConsumptionTime, twelveMonthAgo).ge(Member::getLastConsumptionTime, twentyFourMonthAgo).count()));
        //24个月沉睡商家
        memberGroupingResp.setTwentyFourMonthSleepMerchant(Math.toIntExact(memberRepository.lambdaQuery().eq(Member::getWhetherLogOut, 0).le(Member::getLastConsumptionTime, twentyFourMonthAgo).count()));
        //沉睡商家的总和
        memberGroupingResp.setSleepMerchant(memberGroupingResp.getThreeMonthSleepMerchant() + memberGroupingResp.getSixMonthSleepMerchant() + memberGroupingResp.getNineMonthSleepMerchant() + memberGroupingResp.getTwelveMonthSleepMerchant() + memberGroupingResp.getTwentyFourMonthSleepMerchant());
        //今天生日商家
        memberGroupingResp.setTodayBirthdayMerchant(Math.toIntExact(memberRepository.query().eq("whether_log_out", 0).eq("date_format(birth_day, '%m-%d')", monthDay).count()));
        //本月生日商家
        memberGroupingResp.setMonthBirthdayMerchant(Math.toIntExact(memberRepository.query().eq("whether_log_out", 0).eq("date_format(birth_day, '%m')", month).count()));
        //下月生日商家
        memberGroupingResp.setNextMonthBirthdayMerchant(Math.toIntExact(memberRepository.query().eq("whether_log_out", 0).eq("date_format(birth_day, '%m')", nextMonth).count()));
        //生日商家的总和
        memberGroupingResp.setBirthdayMerchant(memberGroupingResp.getTodayBirthdayMerchant() + memberGroupingResp.getMonthBirthdayMerchant() + memberGroupingResp.getNextMonthBirthdayMerchant());
        //总商家
        memberGroupingResp.setTotalMerchant(Math.toIntExact(memberRepository.lambdaQuery().eq(Member::getOrderNumber, 0).count()));
        return memberGroupingResp;
    }

    private void dealExtCellPhone(List<MemberExt> memberPageInfo) {
        //list判空
        if (CollUtil.isEmpty(memberPageInfo)) {
            return;
        }
//            遍历list
        for (Member member : memberPageInfo) {
            //判断cellphone不为空切不为纯数字字符
            dealCellPhone(member);
        }
    }

    private void dealCellPhone(List<Member> memberPageInfo) {
        //list判空
        if (CollUtil.isEmpty(memberPageInfo)) {
            return;
        }
//            遍历list
        for (Member member : memberPageInfo) {
            //判断cellphone不为空切不为纯数字字符
            dealCellPhone(member);
        }
    }

    @Override
    public void dealCellPhone(Member member) {
        if (member == null || StrUtil.isEmpty(member.getCellPhone())) {
            return;
        }
        //解密失败返回原文
        try {
            member.setCellPhone(AesUtil.decrypt(member.getCellPhone(), encryptConfig.getAesSecret()));
        } catch (Exception e) {
            log.error("解密手机号失败,账户：{}", JsonUtil.toJsonString(member), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long freezeMember(CmdMemberReq cmdMemberReq) {
        //判断userId是否有店铺
        List<Member> shopIds = shopRepository.getOpenShopIdsByUserIds(Collections.singletonList(cmdMemberReq.getId()));
        if (CollUtil.isNotEmpty(shopIds)) {
            throw new BusinessException(UserResultCodeEnum.USER_HAS_SHOP_MEMBER);
        }
        Member member = memberRepository.getById(cmdMemberReq.getId());
        member.setDisabled(true);
        memberRepository.editMember(member);
        baseLogAssist.recordLog(ExaminModelEnum.USER, ExaProEnum.MODIFY, "冻结商家", cmdMemberReq.getOperationUserId(), cmdMemberReq.getOperationShopId(), new MemberLogBO(member), new MemberLogBO(member));

        // 修改缓存
        baseLoginService.disable(RoleEnum.MEMBER, member.getId());
        return cmdMemberReq.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long thawingMember(CmdMemberReq cmdMemberReq) {
        Member member = memberRepository.getById(cmdMemberReq.getId());
        member.setDisabled(false);
        memberRepository.editMember(member);
        baseLogAssist.recordLog(ExaminModelEnum.USER, ExaProEnum.MODIFY, "解冻商家", cmdMemberReq.getOperationUserId(), cmdMemberReq.getOperationShopId(), new MemberLogBO(member), new MemberLogBO(member));
        return cmdMemberReq.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long changePassword(CmdMemberReq cmdMemberReq) {
        Member member = memberRepository.getById(cmdMemberReq.getId());
        TransactionHelper.doInTransaction(() -> {
            String newPassword = cmdMemberReq.getPassword() + member.getPasswordSalt();
            member.setPassword(SecureUtil.sha256(newPassword));
            memberRepository.updateById(member);
            baseLogAssist.recordLog(ExaminModelEnum.USER, ExaProEnum.MODIFY, "修改商家密码", cmdMemberReq.getOperationUserId(), cmdMemberReq.getOperationShopId(), new MemberLogBO(member), new MemberLogBO(member));
            //        判断是否有店铺管理员身份
            Manager manager = managerRepository.getByUserName(member.getUserName());
            if (manager != null) {
                String managerNewPassword = cmdMemberReq.getPassword() + manager.getPasswordSalt();
                manager.setPassword(SecureUtil.sha256(managerNewPassword));
                managerRepository.updateById(manager);
            }
        });
        return cmdMemberReq.getId();
    }

    @Override
    public Long unbindPhone(CmdMemberReq cmdMemberReq) {
        Member realMember = memberRepository.getById(cmdMemberReq.getId());
        String cellPhone = AesUtil.decrypt(realMember.getCellPhone(), encryptConfig.getAesSecret());
        Member member = new Member();
        member.setId(cmdMemberReq.getId());
        member.setCellPhone("");
        memberRepository.editMember(member);
        MemberContact memberContact = new MemberContact();
        memberContact.setUserId(cmdMemberReq.getId());
        memberContact.setServiceProvider(MemberContactEnum.Provider.SMS.getValueDesc());
        memberContactRepository.deleteMemberContact(memberContact);
        baseLogAssist.recordLog(ExaminModelEnum.USER, ExaProEnum.MODIFY, "解绑商家手机号", cmdMemberReq.getOperationUserId(), cmdMemberReq.getOperationShopId(), new MemberLogBO(realMember.getId(), cellPhone), new MemberLogBO(realMember.getId()));
        return cmdMemberReq.getId();
    }

    @Override
    public Long batchDelete(BatchCmdMemberReq batchCmdMemberReq) {
        if (CollectionUtil.isEmpty(batchCmdMemberReq.getIds())) {
            return null;
        }

        TransactionHelper.doInTransaction(() -> {
            //判断userId是否有店铺
            List<Member> shopIds = shopRepository.getShopIdsByUserIds(batchCmdMemberReq.getIds());
            if (CollUtil.isNotEmpty(shopIds)) {
                throw new BusinessException(UserResultCodeEnum.USER_HAS_SHOP);
            }
            //判断userId是否有管理员
            List<Member> managerList = managerRepository.getManagerIdsByUserIds(batchCmdMemberReq.getIds());
            if (CollUtil.isNotEmpty(managerList)) {
                String managerNames = managerList.stream().map(Member::getUserName).collect(Collectors.joining(","));
                throw new BusinessException(UserResultCodeEnum.USER_HAS_MANAGER.getCode(), StrUtil.format(UserResultCodeEnum.USER_HAS_MANAGER.getMsg(), managerNames));
            }
            memberRepository.lambdaUpdate().set(Member::getDisabled, true).set(Member::getWhetherLogOut, true).in(Member::getId, batchCmdMemberReq.getIds()).update();
            //手动记录日志
            baseLogAssist.recordLog(ExaminModelEnum.USER, ExaProEnum.MODIFY, "注销商家", batchCmdMemberReq.getOperationUserId(), batchCmdMemberReq.getOperationShopId(), new MemberLogListBO(batchCmdMemberReq), new MemberLogListBO(batchCmdMemberReq));
        });

        // 把用户禁用 后续可以考虑异步或者批量清空redis
        batchCmdMemberReq.getIds().forEach(memberId -> {
            try {
                baseLoginService.delete(RoleEnum.MEMBER, memberId);
            } catch (Exception e) {
                log.warn("注销用户失败，memberId:{}", memberId, e);
            }
        });
        return (long) batchCmdMemberReq.getIds().size();
    }

    @Override
    public MemberLabelRespList getMemberLabel(QueryMemberReq queryMemberReq) {
        MemberLabel memberLabel = new MemberLabel();
        memberLabel.setMemId(queryMemberReq.getId());
        List<MemberLabel> memberLabelList = memberLabelService.getMemberLabel(memberLabel);
        MemberLabelRespList memberLabelRespList = new MemberLabelRespList();
        memberLabelRespList.setMemberLabelRespList(JsonUtil.copyList(memberLabelList, MemberLabelResp.class));
        return memberLabelRespList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long setMemberLabel(CmdMemberReq cmdMemberReq) {
        memberLabelService.setMemberLabel(cmdMemberReq);
        return cmdMemberReq.getId();
    }

    @Override
    public MemberListResp queryMemberList(QueryMemberListReq queryMemberReq) {
        QueryMemberListModel queryMemberListDto = JsonUtil.copy(queryMemberReq, QueryMemberListModel.class);
        List<Member> memberList = memberRepository.getMemberList(queryMemberListDto);
        dealCellPhone(memberList);
        List<MemberResp> memberRespList = JsonUtil.copyList(memberList, MemberResp.class);
        return new MemberListResp(memberRespList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insert(CreateMemberMsg createMemberMsg) {
        //如果手机号不为空或者不符合手机号规则，需要获取完整手机号
        String phone = createMemberMsg.getPhone();
        if (StrUtil.isNotEmpty(createMemberMsg.getPhone()) || PhoneUtil.isMobile(phone)) {
//TODO       phone = epAccountService.getBizAccPhoneById(createMemberMsg.getId());
//            手机号不符合规则则为空
            phone = PhoneUtil.isMobile(phone) ? phone : null;
        }
        Member member = new Member();
        Date date = new Date(createMemberMsg.getCreateTime());
        member.setCreateTime(date);
        member.setUpdateTime(date);
        member.setUserName(createMemberMsg.getLogin());
        member.setNick(createMemberMsg.getLogin());
        member.setLastLoginDate(date);
        member.setCellPhone(AesUtil.encrypt(phone, encryptConfig.getAesSecret()));
        member.setPlatform(MemberEnum.PlatformType.PC.getCode());
        member.setEncryptionMode(DEFAULT_ENCRYPTION_MODE);
        memberRepository.insert(member);
        //判断是否有手机号
        if (StrUtil.isNotBlank(phone)) {
            BindContactCmdReq bindContactCmdReq = new BindContactCmdReq();
            bindContactCmdReq.setId(member.getId());
            bindContactCmdReq.setUsertype(MemberContactEnum.Provider.SMS.getValue());
            bindContactCmdReq.setContact(createMemberMsg.getPhone());
            memberContactService.bindContact(bindContactCmdReq);
        }
        sendRegisterSms(createMemberMsg.getLogin(), createMemberMsg.getPhone(), createMemberMsg.getName());
    }

    /**
     * 发送注册成功短信
     *
     * @param userName
     * @param phone
     * @param nickName
     */
    private void sendRegisterSms(String userName, String phone, String nickName) {
        //发送短信问题不应影响注册流程
        try {
            //发送注册成功短信
            Map<String, String> map = new HashMap<>();
            map.put("userName", userName);
            map.put("password", "******");
            map.put("cellPhone", phone);
            map.put("nickName", nickName);
            SmsBodyReq smsBodyReq = new SmsBodyReq();
            smsBodyReq.setTemplateId(userLionConfigClient.getRegisterSuccessNotice());
            smsBodyReq.setParam(JsonUtil.toJsonString(map));
            smsBodyReq.setRequestId(leafService.generateNoBySnowFlake(LeafConstant.KEY_USER_NO));
            // 设置收信人
            ContactReq contactReq = new ContactReq();
            contactReq.setMobile(phone);
            smsBodyReq.setContactList(Collections.singletonList(contactReq));
            messageCMDFeign.sendSms(smsBodyReq);
        } catch (Exception e) {
            log.error("发送注册成功短信失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MemberResp insert(AddMemberReq addMemberReq) {
        Member member = new Member();
        Date date = new Date();
        member.setCreateTime(date);
        member.setUpdateTime(date);
        member.setUserName(addMemberReq.getUserName());
        member.setNick(addMemberReq.getNickName());
        member.setLastLoginDate(date);
        member.setCellPhone(AesUtil.encrypt(addMemberReq.getMobile(), encryptConfig.getAesSecret()));
        member.setPlatform(MemberEnum.PlatformType.PC.getCode());
        member.setEncryptionMode(DEFAULT_ENCRYPTION_MODE);
        member.setPasswordSalt(RandomUtil.randomString(6));
        member.setPassword(SecureUtil.sha256(addMemberReq.getPassword() + member.getPasswordSalt()));

//TODO    BizAccountTO contextBO = epAccountService.getInfoByLogin(addMemberReq.getUserName());
//        member.setEpAccountId(contextBO.getId());
        memberRepository.insert(member);
        dealCellPhone(member);
        return JsonUtil.copy(member, MemberResp.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    /*@ExaminProcess(processModel = ExaminModelEnum.USER, actionName = "编辑商家", processType = ExaProEnum.MODIFY,
        serviceMethod = "update", dto = UpdateMemberReq.class, entity = Member.class)*/ public MemberResp update(UpdateMemberReq addMemberReq) {
        Date date = new Date();
        Member member = memberRepository.getById(addMemberReq.getId());
        if (member == null) {
            log.error("商家不存在");
            return null;
        }
//        if (addMemberReq.getUserName() != null) {
//            member.setUserName(addMemberReq.getUserName());
//        }
        if (addMemberReq.getMobile() != null) {
            member.setCellPhone(AesUtil.encrypt(addMemberReq.getMobile(), encryptConfig.getAesSecret()));
        }
        if (addMemberReq.getNick() != null) {
            member.setNick(addMemberReq.getNick());
        }
        if (addMemberReq.getRealName() != null) {
            member.setRealName(addMemberReq.getRealName());
        }
        if (addMemberReq.getSex() != null) {
            member.setSex(addMemberReq.getSex());
        }
        if (addMemberReq.getPhoto() != null) {
            member.setPhoto(addMemberReq.getPhoto());
        }
        if (addMemberReq.getQq() != null) {
            member.setQq(addMemberReq.getQq());
        }
        if (addMemberReq.getOccupation() != null) {
            member.setOccupation(addMemberReq.getOccupation());
        }
        if (addMemberReq.getBirthDay() != null) {
            member.setBirthDay(addMemberReq.getBirthDay());
        }
        if (StringUtils.isNotBlank(addMemberReq.getOpenId()) && !StringUtils.isNotBlank(member.getOpenId())) {
            member.setOpenId(addMemberReq.getOpenId());
        }
        if (StringUtils.isNotBlank(addMemberReq.getUnionId()) && !StringUtils.isNotBlank(member.getUnionId())) {
            member.setUnionId(addMemberReq.getUnionId());
        }
        member.setUpdateTime(date);
        memberRepository.updateById(member);
        dealCellPhone(member);
        return JsonUtil.copy(member, MemberResp.class);
    }

    @Override
    public MemberResp autoRegister(String phone, String openId, String unionId, MemberEnum.PlatformType platformType) {
        Member member = new Member();

        // 账号默认使用手机号
        // 账号默认使用手机号
        String userName = phone;
        // 查询下账号有没有被使用（不包含已经注销的）
        Long count = memberRepository.countByUserName(userName);
        if (count > 0) {
            userName = userName + "_" + (count + 1);
        }

        // 查询默认头像
        String photo = siteSettingService.querySettingsValueByKey(DEFAULT_USER_PHOTO_KEY);

        String salt = FoxUtl.getRandomString(4);
        Date date = new Date();
        member.setCreateTime(date);
        member.setUpdateTime(date);
        member.setPasswordSalt(salt);
        //默认密码
        // since 2025-7-25,code of below is commented
        // String password = DEFAULT_PASSWORD + salt;
        // String sha256Password = SecureUtil.sha256(password);
        // member.setPassword(sha256Password);
        member.setUserName(userName);
        //昵称默认前缀
        member.setNick(LoginConstant.DEFAULT_USER_NAME_PREFIX + userName);
        member.setLastLoginDate(date);
        if (StrUtil.isNotBlank(phone)) {
            member.setCellPhone(AesUtil.encrypt(phone, encryptConfig.getAesSecret()));
        }
        member.setPlatform(platformType.getCode());
        member.setEncryptionMode(DEFAULT_ENCRYPTION_MODE);
        member.setOpenId(openId);
        member.setUnionId(unionId);
        member.setWhetherLogOut(false);
        member.setPhoto(photo);

        // 开启事务 保存用户信息
        boolean sendMsg = TransactionHelper.doInTransaction(() -> {
            int rest = memberRepository.insert(member);
            if (rest > 0 && StrUtil.isNotBlank(phone)) {
                MemberContact memberContact = memberContactBuilder(member.getId(), phone, date);
                memberContactRepository.save(memberContact);
                return true;
            }
            return false;
        });

        // 发送注册成功短信
        if (sendMsg) {
            sendRegisterSms(member.getUserName(), phone, member.getNick());
        }
        return JsonUtil.copy(member, MemberResp.class);
    }

    @Override
    public MemberResp autoRegister(String phone, String userName, MemberEnum.PlatformType platformType) {
        Member member = new Member();

        // 账号默认使用手机号
        // 账号默认使用手机号
        // 查询下账号有没有被使用（不包含已经注销的）
        Long count = memberRepository.countByUserName(userName);
        if (count > 0) {
            userName = userName + "_" + (count + 1);
        }

        // 查询默认头像
        String photo = siteSettingService.querySettingsValueByKey(DEFAULT_USER_PHOTO_KEY);

        String salt = FoxUtl.getRandomString(4);
        Date date = new Date();
        member.setCreateTime(date);
        member.setUpdateTime(date);
        member.setPasswordSalt(salt);
        //默认密码
        String password = DEFAULT_PASSWORD + salt;
        String sha256Password = SecureUtil.sha256(password);
        member.setPassword(sha256Password);
        member.setUserName(userName);
        //昵称默认前缀
        member.setNick(LoginConstant.DEFAULT_USER_NAME_PREFIX + userName);
        member.setLastLoginDate(date);
        if (StrUtil.isNotBlank(phone)) {
            member.setCellPhone(AesUtil.encrypt(phone, encryptConfig.getAesSecret()));
        }
        member.setPlatform(platformType.getCode());
        member.setEncryptionMode(DEFAULT_ENCRYPTION_MODE);
        member.setWhetherLogOut(false);
        member.setPhoto(photo);

        // 开启事务 保存用户信息
        boolean sendMsg = TransactionHelper.doInTransaction(() -> {
            int rest = memberRepository.insert(member);
            if (rest > 0 && StrUtil.isNotBlank(phone)) {
                MemberContact memberContact = memberContactBuilder(member.getId(), phone, date);
                memberContactRepository.save(memberContact);
                return true;
            }
            return false;
        });
        return JsonUtil.copy(member, MemberResp.class);
    }

    private MemberContact memberContactBuilder(Long userId, String contact, Date date) {
        MemberContact memberContact = new MemberContact();
        memberContact.setUserId(userId);
        memberContact.setContact(contact);
        memberContact.setUserType(ContactUserTypeEnum.PHONE.getCode());
        memberContact.setServiceProvider(MemberContactEnum.Provider.SMS.getValueDesc());
        memberContact.setCreateTime(date);
        memberContact.setUpdateTime(date);
        return memberContact;
    }

    @Override
    public MemberResp autoRegister(String openId, String unionId) {
        AssertUtil.throwIfTrue(StrUtil.isBlank(openId) || StrUtil.isBlank(unionId), "小程序授权 openId 及 unionId不能为空！");
        return autoRegister(null, openId, unionId, MemberEnum.PlatformType.WeiXinSmallProg);
    }

    @Override
    public MemberResp autoRegister(String phone) {
        AssertUtil.throwIfTrue(StrUtil.isBlank(phone), "自动注册,手机号不能为空！");
        return autoRegister(phone, null, null, MemberEnum.PlatformType.WeiXin);
    }

    @Override
    public BaseResp sendEmailMsg(SendEmailMsgReq apiSendEmailMsgReq) {
        //分页查询联系方式
        MemberContactQueryPageReq memberContactQueryPageReq = new MemberContactQueryPageReq();
        memberContactQueryPageReq.setLabelId(apiSendEmailMsgReq.getLabelId());
        memberContactQueryPageReq.setHasCount(true);
        memberContactQueryPageReq.setServiceProvider(CommonConstant.EMAIL);
        memberContactQueryPageReq.setPageNo(CommonConstant.DEFAULT_PAGE_NO);
        memberContactQueryPageReq.setPageSize(CommonConstant.BATCH_QUERY_SIZE);
        Page<MemberContact> memberContactPage = memberContactService.getMemberContactByLabelId(memberContactQueryPageReq);
        //获取总页数
        Integer totalPage = memberContactPage.getPages();
        //遍历所有页
        for (int i = 1; i <= totalPage; i++) {
            //设置不在查询总数
            memberContactQueryPageReq.setHasCount(false);
            //获取当前页的数据
            List<MemberContact> memberContactList = memberContactPage.getResult();
            //所有邮箱地址
            List<String> emailList = new ArrayList<>();
            //遍历当前页的数据
            for (MemberContact memberContact : memberContactList) {
                //获取所有邮箱地址
                String email = memberContact.getContact();
                emailList.add(email);
            }
            //发送邮件
            //messageCMDFeign.sendEmail(apiSendEmailMsgReq.getTitle(), apiSendEmailMsgReq.getContent(), emailList);
            //获取下一页
            memberContactQueryPageReq.setPageNo(i + 1);
            memberContactPage = memberContactService.getMemberContactByLabelId(memberContactQueryPageReq);
        }

        return BaseResp.of();
    }

    @Override
    public BaseResp batchAddMemberLabel(BatchCmdMemberLabelReq cmdMemberReq) {
        return memberLabelService.batchAddMemberLabel(cmdMemberReq);
    }

    @Override
    public BaseResp updateLoginTime(Long id) {
        Member member = memberRepository.getById(id);
        member.setLastLoginDate(new Date());
        memberRepository.updateById(member);
        return BaseResp.of();
    }

    //登出
    @Override
    public BaseResp logout(BaseIdReq baseIdReq) {
//        epAccountService.logoutByBizAccount(baseIdReq.getId().intValue());
        return BaseResp.of();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMember(UpdateMemberMsg updateMemberMsg) {
        Member member = new Member();
        Member newMember = memberRepository.queryMember(member);
        if (newMember == null) {
            return;
        }
//        判断是修改手机
        if (Objects.equals(updateMemberMsg.getModifyType(), CommonConstant.CHANGE_MOBILE_TYPE)) {
            //查询现在手机号
            String phone = memberContactService.queryPhone(newMember.getId());
            if (StrUtil.isEmpty(phone) || !phone.equals(updateMemberMsg.getNewValue())) {
                BindContactCmdReq bindContactCmdReq = new BindContactCmdReq();
                bindContactCmdReq.setId(newMember.getId());
                bindContactCmdReq.setContact(updateMemberMsg.getNewValue());
                bindContactCmdReq.setUsertype(MemberContactEnum.Provider.SMS.getValue());
                memberContactService.changeContact(bindContactCmdReq);
                newMember.setCellPhone(AesUtil.encrypt(bindContactCmdReq.getContact(), encryptConfig.getAesSecret()));
                memberRepository.updateById(newMember);
                //判断是否需要修改管理员联系方式
                Manager manager = managerRepository.getByUserName(member.getUserName());
                if (manager != null) {
                    //修改用户的cellphone
                    manager.setCellphone(AesUtil.encrypt(bindContactCmdReq.getContact(), encryptConfig.getAesSecret()));
                    managerRepository.updateById(manager);
                }
            }
        }
    }

    @Override
    public void freezeMemberByShopId(Long id, Boolean freeze) {
        Manager manager = managerRepository.getByShopId(id);
        Member member = memberRepository.getByUserName(manager.getUserName());
        member.setDisabled(freeze);
        memberRepository.updateById(member);
//        epAccountService.batchEnableBizAccountReq(Collections.singletonList(member.getEpAccountId()),freeze);
    }

    @Override
    public UserIdListResp queryUserId(QueryUserIdReq req) {
        List<Long> userIdList = memberRepository.queryUserIdByMobile(req.getMobile());

        UserIdListResp resp = new UserIdListResp();
        resp.setUserIdList(userIdList);
        return resp;
    }

    @Override
    public BasePageResp<MemberResp> queryMemberPageAndLabel(QueryMemberPageReq queryMemberPageReq) {
        BasePageResp<MemberResp> memberRespBasePageResp = queryMemberPage(queryMemberPageReq);
        //判空
        if (memberRespBasePageResp.getTotalCount() > 0) {
            List<MemberResp> memberRespList = memberRespBasePageResp.getData();
//            批量遍历memberRespList一次500条
            for (int i = 0; i < memberRespList.size(); i += 500) {

                List<MemberResp> subList = memberRespList.subList(i, Math.min(i + 500, memberRespList.size()));
                memberLabelService.setMemberLabelByUserIdList(subList);
            }
        }
        return memberRespBasePageResp;
    }

    @Override
    public EpManagerResp checkAndCreateUser(Integer id) {
//        //查询用户
//        BizAccountTO bizAccountTO = epAccountService.getInfoByEpId(id);
//        if (bizAccountTO == null || StrUtil.isEmpty(bizAccountTO.getPhone())){
//            return null;
//        }
//        //查询用户手机号
//        String phone = epAccountService.getBizAccPhoneById(id);
//        //判断手机号是否存在
//        if (StrUtil.isEmpty(phone)){
//            return null;
//        }
//        //根据手机号查询商家
//        Long userId = memberContactService.queryUserIdByPhone(phone);
//        if (userId != null){
//            return null;
//        }
//        //走ep注册逻辑
//        CreateMemberMsg msg = new CreateMemberMsg();
//        msg.setId(id);
//        msg.setLogin(bizAccountTO.getLogin());
//        msg.setName(bizAccountTO.getName());
//        msg.setCreateTime(bizAccountTO.getCreateTime());
//        msg.setPhone(phone);
//        insertEpAccount(msg);
//        return managerService.queryEpManager(id);
        return null;
    }

    @Override
    public ResetPasswordResp resetPassword(ResetPasswordReq req) {
        Member member = memberRepository.getByPhone(req.getPhone());
        AssertUtil.throwIfNull(member, "账号不存在!");
        // 调用修改密码的方法
        CmdMemberReq changePasswordParam = new CmdMemberReq();
        changePasswordParam.setId(member.getId());
        changePasswordParam.setPassword(req.getNewPassword());
        changePasswordParam.setOperationUserId(0L);
        resetPassword(member, changePasswordParam);
        return ResetPasswordResp.builder().whetherTransfer(Boolean.FALSE).newPassword(DEFAULT_PASSWORD).build();
    }

    @Override
    public ResetPasswordResp modifyPassword(ModifyPasswordReq req) {
        Member member = memberRepository.getById(req.getId());
        AssertUtil.throwIfNull(member, "账号不存在!");
//        检查密码
        if (StringUtils.isNotBlank(member.getPassword())) {
            // 如果用户有密码 则校验旧密码
            String oldPassword = req.getOldPassword() + member.getPasswordSalt();
            if (!SecureUtil.sha256(oldPassword).equals(member.getPassword())) {
                throw new BusinessException("旧密码错误");
            }
        }
        // 调用修改密码的方法
        CmdMemberReq changePasswordParam = new CmdMemberReq();
        changePasswordParam.setId(member.getId());
        changePasswordParam.setPassword(req.getNewPassword());
        changePasswordParam.setOperationUserId(0L);
        resetPassword(member, changePasswordParam);
        return ResetPasswordResp.builder().whetherTransfer(Boolean.FALSE).newPassword(DEFAULT_PASSWORD).build();
    }

    @Override
    public LoginResp register(RegisterReq req) {
        MemberRegisterService memberRegisterService = registerStrategyContext.getRegisterStrategy(RegisterTypeEnum.nameOf(req.getRegisterType()));
        return memberRegisterService.registerMember(req);
    }

    @Override
    public void updateWxmp(Member member) {
        memberRepository.updateById(member);
    }

    public Long resetPassword(Member member, CmdMemberReq cmdMemberReq) {
        TransactionHelper.doInTransaction(() -> {
            String newPassword = cmdMemberReq.getPassword() + member.getPasswordSalt();
            member.setPassword(SecureUtil.sha256(newPassword));
            memberRepository.updateById(member);
            baseLogAssist.recordLog(ExaminModelEnum.USER, ExaProEnum.MODIFY, "重置商家密码", cmdMemberReq.getOperationUserId(), cmdMemberReq.getOperationShopId(), new MemberLogBO(member), new MemberLogBO(member));

            //判断是否有店铺管理员身份
            Manager manager = managerRepository.getByUserName(member.getUserName());
            if (manager != null) {
                String managerNewPassword = cmdMemberReq.getPassword() + manager.getPasswordSalt();
                manager.setPassword(SecureUtil.sha256(managerNewPassword));
                managerRepository.updateById(manager);
            }
        });
        return cmdMemberReq.getId();
    }
}
