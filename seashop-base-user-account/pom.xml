<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hishop.himall</groupId>
        <artifactId>himall-base</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>seashop-base-user-account</artifactId>
    <packaging>jar</packaging>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-dao</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-user-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-trade-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-order-api</artifactId>
        </dependency>
        <!--用户系统EP start-->
<!--        <dependency>-->
<!--            <groupId>com.sankuai.epassport</groupId>-->
<!--            <artifactId>epassport-service-client</artifactId>-->
<!--            <version>2.0.31</version>-->
<!--            <scope>compile</scope>-->
<!--        </dependency>-->
        <!--用户系统EP end-->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hishop-xxl-job-client-boot-starter</artifactId>
                    <groupId>com.hishop.starter</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-security</artifactId>
        </dependency>

        <!--消息中间件mafka end-->
    </dependencies>

</project>
