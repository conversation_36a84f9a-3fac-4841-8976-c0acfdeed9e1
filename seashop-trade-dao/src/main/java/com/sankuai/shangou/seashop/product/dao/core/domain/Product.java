package com.sankuai.shangou.seashop.product.dao.core.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * <p>
 * 商品表
 * </p>
 *
 * <AUTHOR> @since 2024-01-22
 */
public class Product implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品唯一表示(美团Id组件获取)
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 店铺ID
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 类目ID
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 类目路径
     */
    @TableField("category_path")
    private String categoryPath;
    /**
     * 品牌ID
     */
    @TableField("brand_id")
    private Long brandId;

    /**
     * 商品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 商品编号
     */
    @TableField("product_code")
    private String productCode;

    /**
     * 广告词
     */
    @TableField("short_description")
    private String shortDescription;

    /**
     * 销售状态 1-销售中 2-仓库中 3-草稿箱
     */
    @TableField("sale_status")
    private Integer saleStatus;

    /**
     * 审核状态 1-待审核 2-销售中 3-未通过 4-违规下架
     */
    @TableField("audit_status")
    private Integer auditStatus;

    /**
     * 添加日期
     */
    @TableField("added_date")
    private Date addedDate;

    /**
     * 显示顺序
     */
    @TableField("display_sequence")
    private Long displaySequence;

    /**
     * 市场价
     */
    @TableField("market_price")
    private BigDecimal marketPrice;

    /**
     * 最小销售价
     */
    @TableField("min_sale_price")
    private BigDecimal minSalePrice;

    /**
     * 是否有SKU
     */
    @TableField("has_sku")
    private Boolean hasSku;

    /**
     * 销售量
     */
    @TableField("sale_counts")
    private Long saleCounts;

    /**
     * 运费模板ID
     */
    @TableField("freight_template_id")
    private Long freightTemplateId;

    /**
     * 重量
     */
    @TableField("weight")
    private BigDecimal weight;

    /**
     * 体积
     */
    @TableField("volume")
    private BigDecimal volume;

    /**
     * 数量
     */
    @TableField("quantity")
    private Integer quantity;

    /**
     * 计量单位
     */
    @TableField("measure_unit")
    private String measureUnit;

    /**
     * 是否已删除
     */
    @TableField("whether_delete")
    private Boolean whetherDelete;

    /**
     * 删除版本号 为0表示未删除
     */
    @TableField("delete_version")
    private Long deleteVersion;

    /**
     * 最大购买数
     */
    @TableField("max_buy_count")
    private Integer maxBuyCount;

    /**
     * 是否开启阶梯价格
     */
    @TableField("whether_open_ladder")
    private Boolean whetherOpenLadder;

    /**
     * 颜色别名
     */
    @TableField("spec1_alias")
    private String spec1Alias;

    /**
     * 尺码别名
     */
    @TableField("spec2_alias")
    private String spec2Alias;

    /**
     * 版本别名
     */
    @TableField("spec3_alias")
    private String spec3Alias;

    /**
     * 规格名称id的集合, 逗号隔开
     */
    @TableField("spec_name_ids")
    private String specNameIds;

    /**
     * 商家商品序号
     */
    @TableField("shop_display_sequence")
    private Integer shopDisplaySequence;

    /**
     * 虚拟销量
     */
    @TableField("virtual_sale_counts")
    private Long virtualSaleCounts;

    /**
     * 商品主图
     */
    @TableField("image_path")
    private String imagePath;

    /**
     * 商品主图视频
     */
    @TableField("video_path")
    private String videoPath;

    /**
     * 提交审核时间
     */
    @TableField("submit_audit_time")
    private Date submitAuditTime;

    /**
     * 商品审核时间
     */
    @TableField("check_time")
    private Date checkTime;

    /**
     * 倍数起购量
     */
    @TableField("multiple_count")
    private Integer multipleCount;

    /**
     * 是否新商品
     */
    @TableField("whether_new_product")
    private Boolean whetherNewProduct;

    /**
     * 来源 1-商城 2-牵牛花 3-易久批
     */
    @TableField("source")
    private Integer source;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建人Id
     */
    @TableField("create_user")
    private Long createUser;

    /**
     * 更新人Id
     */
    @TableField("update_user")
    private Long updateUser;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryPath() {
        return categoryPath;
    }

    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }

    public Long getBrandId() {
        return brandId;
    }

    public void setBrandId(Long brandId) {
        this.brandId = brandId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getShortDescription() {
        return shortDescription;
    }

    public void setShortDescription(String shortDescription) {
        this.shortDescription = shortDescription;
    }

    public Integer getSaleStatus() {
        return saleStatus;
    }

    public void setSaleStatus(Integer saleStatus) {
        this.saleStatus = saleStatus;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Date getAddedDate() {
        return addedDate;
    }

    public void setAddedDate(Date addedDate) {
        this.addedDate = addedDate;
    }

    public Long getDisplaySequence() {
        return displaySequence;
    }

    public void setDisplaySequence(Long displaySequence) {
        this.displaySequence = displaySequence;
    }

    public BigDecimal getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(BigDecimal marketPrice) {
        this.marketPrice = marketPrice;
    }

    public BigDecimal getMinSalePrice() {
        return minSalePrice;
    }

    public void setMinSalePrice(BigDecimal minSalePrice) {
        this.minSalePrice = minSalePrice;
    }

    public Boolean getHasSku() {
        return hasSku;
    }

    public void setHasSku(Boolean hasSku) {
        this.hasSku = hasSku;
    }

    public Long getSaleCounts() {
        return saleCounts;
    }

    public void setSaleCounts(Long saleCounts) {
        this.saleCounts = saleCounts;
    }

    public Long getFreightTemplateId() {
        return freightTemplateId;
    }

    public void setFreightTemplateId(Long freightTemplateId) {
        this.freightTemplateId = freightTemplateId;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getMeasureUnit() {
        return measureUnit;
    }

    public void setMeasureUnit(String measureUnit) {
        this.measureUnit = measureUnit;
    }

    public Boolean getWhetherDelete() {
        return whetherDelete;
    }

    public void setWhetherDelete(Boolean whetherDelete) {
        this.whetherDelete = whetherDelete;
    }

    public Long getDeleteVersion() {
        return deleteVersion;
    }

    public void setDeleteVersion(Long deleteVersion) {
        this.deleteVersion = deleteVersion;
    }

    public Integer getMaxBuyCount() {
        return maxBuyCount;
    }

    public void setMaxBuyCount(Integer maxBuyCount) {
        this.maxBuyCount = maxBuyCount;
    }

    public Boolean getWhetherOpenLadder() {
        return whetherOpenLadder;
    }

    public void setWhetherOpenLadder(Boolean whetherOpenLadder) {
        this.whetherOpenLadder = whetherOpenLadder;
    }

    public String getSpec1Alias() {
        return spec1Alias;
    }

    public void setSpec1Alias(String spec1Alias) {
        this.spec1Alias = spec1Alias;
    }

    public String getSpec2Alias() {
        return spec2Alias;
    }

    public void setSpec2Alias(String spec2Alias) {
        this.spec2Alias = spec2Alias;
    }

    public String getSpec3Alias() {
        return spec3Alias;
    }

    public void setSpec3Alias(String spec3Alias) {
        this.spec3Alias = spec3Alias;
    }

    public Integer getShopDisplaySequence() {
        return shopDisplaySequence;
    }

    public void setShopDisplaySequence(Integer shopDisplaySequence) {
        this.shopDisplaySequence = shopDisplaySequence;
    }

    public Long getVirtualSaleCounts() {
        return virtualSaleCounts;
    }

    public void setVirtualSaleCounts(Long virtualSaleCounts) {
        this.virtualSaleCounts = virtualSaleCounts;
    }

    public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }

    public String getVideoPath() {
        return videoPath;
    }

    public void setVideoPath(String videoPath) {
        this.videoPath = videoPath;
    }

    public Date getSubmitAuditTime() {
        return submitAuditTime;
    }

    public void setSubmitAuditTime(Date submitAuditTime) {
        this.submitAuditTime = submitAuditTime;
    }

    public Date getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(Date checkTime) {
        this.checkTime = checkTime;
    }

    public Integer getMultipleCount() {
        return multipleCount;
    }

    public void setMultipleCount(Integer multipleCount) {
        this.multipleCount = multipleCount;
    }

    public Boolean getWhetherNewProduct() {
        return whetherNewProduct;
    }

    public void setWhetherNewProduct(Boolean whetherNewProduct) {
        this.whetherNewProduct = whetherNewProduct;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public String getSpecNameIds() {
        return specNameIds;
    }

    public void setSpecNameIds(String specNameIds) {
        this.specNameIds = specNameIds;
    }

    @Override
    public String toString() {
        return "Product{" +
        "id=" + id +
        ", productId=" + productId +
        ", shopId=" + shopId +
        ", categoryId=" + categoryId +
        ", categoryPath=" + categoryPath +
        ", brandId=" + brandId +
        ", productName=" + productName +
        ", productCode=" + productCode +
        ", shortDescription=" + shortDescription +
        ", saleStatus=" + saleStatus +
        ", auditStatus=" + auditStatus +
        ", addedDate=" + addedDate +
        ", displaySequence=" + displaySequence +
        ", marketPrice=" + marketPrice +
        ", minSalePrice=" + minSalePrice +
        ", hasSku=" + hasSku +
        ", saleCounts=" + saleCounts +
        ", freightTemplateId=" + freightTemplateId +
        ", weight=" + weight +
        ", volume=" + volume +
        ", quantity=" + quantity +
        ", measureUnit=" + measureUnit +
        ", whetherDelete=" + whetherDelete +
        ", deleteVersion=" + deleteVersion +
        ", maxBuyCount=" + maxBuyCount +
        ", whetherOpenLadder=" + whetherOpenLadder +
        ", spec1Alias=" + spec1Alias +
        ", spec2Alias=" + spec2Alias +
        ", spec3Alias=" + spec3Alias +
        ", shopDisplaySequence=" + shopDisplaySequence +
        ", virtualSaleCounts=" + virtualSaleCounts +
        ", imagePath=" + imagePath +
        ", videoPath=" + videoPath +
        ", submitAuditTime=" + submitAuditTime +
        ", checkTime=" + checkTime +
        ", multipleCount=" + multipleCount +
        ", whetherNewProduct=" + whetherNewProduct +
        ", source=" + source +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", createUser=" + createUser +
        ", updateUser=" + updateUser +
        "}";
    }
}
