package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.product.dao.core.domain.Category;
import com.sankuai.shangou.seashop.product.dao.core.mapper.CategoryMapper;
import com.sankuai.shangou.seashop.product.dao.core.mapper.ext.CategoryExtMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/09 15:46
 */
@Repository
@Slf4j
public class CategoryRepository extends ServiceImpl<CategoryMapper, Category> {
    @Resource
    private CategoryExtMapper categoryExtMapper;


    /**
     * 查询分类名称map
     *
     * @param categoryIdList 分类id的集合
     * @return 分类名称map
     */
    public Map<Long, String> getCategoryNameMap(List<Long> categoryIdList) {
        if (CollectionUtils.isEmpty(categoryIdList)) {
            return MapUtils.EMPTY_MAP;
        }

        List<Category> categoryList = listCategoryByIds(categoryIdList);

        return categoryList.stream().collect(Collectors.toMap(Category::getId, Category::getName,
                (k1, k2) -> k2));
    }

    /**
     * 查询一级分类
     *
     * @return
     */
    public List<Category> queryFirst(Long parentCategoryId) {
        return baseMapper.selectList(
                new LambdaQueryWrapper<Category>()
                        .eq(Category::getParentCategoryId, parentCategoryId)
                        .eq(Category::getWhetherDelete, Boolean.FALSE));
        // .net那边没排序，java版本展示顺序跟.net不一样测试都提了个bug
//                        .orderByAsc(Category::getDisplaySequence)
//                        .orderByAsc(Category::getId));
    }

    /**
     * 根据类目id查询类目集合
     *
     * @param categoryIds 类目id的集合
     * @return 类目map
     */
    public List<Category> listCategoryByIds(List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Collections.emptyList();
        }

        categoryIds = categoryIds.stream().distinct().collect(Collectors.toList());
        return MybatisUtil.queryBatch(ids ->
                        baseMapper.selectList(new LambdaQueryWrapper<Category>().in(Category::getId, ids).eq(Category::getWhetherDelete, Boolean.FALSE)),
                categoryIds.stream().distinct().collect(Collectors.toList()));
    }

    /**
     * 根据上级id和名称查询分类
     *
     * @param pid      上级id
     * @param nameList 名称集合
     * @return 分类集合
     */
    public List<Category> listCategoryByPidAndName(Long pid, List<String> nameList) {
        if (CollectionUtils.isEmpty(nameList)) {
            return Collections.emptyList();
        }

        return MybatisUtil.queryBatch(names -> list(new LambdaQueryWrapper<Category>()
                .eq(Category::getParentCategoryId, pid)
                .in(Category::getName, names)
                .eq(Category::getWhetherDelete, Boolean.FALSE)), nameList);
    }

    /**
     * 根据上级id和名称查询分类
     *
     * @param pid  上级id
     * @param name 名称
     * @return 分类
     */
    public Category getCategoryByPidAndName(Long pid, String name) {
        return getOne(new LambdaQueryWrapper<Category>()
                .eq(Category::getParentCategoryId, pid)
                .eq(Category::getName, name)
                .eq(Category::getWhetherDelete, Boolean.FALSE)
                .last("limit 1"));
    }

    public void logicRemoveByIds(List<Long> childIds) {
        if (CollectionUtils.isEmpty(childIds)) {
            return;
        }

        Category updateCategory = new Category();
        updateCategory.setWhetherDelete(Boolean.TRUE);
        MybatisUtil.executeBatch(ids -> update(updateCategory, new LambdaQueryWrapper<Category>().in(Category::getId, ids)), childIds);
    }

    public List<Category> listByParentIds(List<Long> parentIds) {
        if (CollectionUtils.isEmpty(parentIds)) {
            return Collections.EMPTY_LIST;
        }

        return MybatisUtil.queryBatch(ids ->
                        list(new LambdaQueryWrapper<Category>().in(Category::getParentCategoryId, ids).eq(Category::getWhetherDelete, Boolean.FALSE)),
                parentIds);
    }

    public Long getMaxDisplaySequence(Long parentCategoryId) {
        Category category = getOne(new LambdaQueryWrapper<Category>()
                .eq(Category::getParentCategoryId, parentCategoryId)
                .eq(Category::getWhetherDelete, Boolean.FALSE)
                .orderByDesc(Category::getDisplaySequence)
                .last("limit 1"));
        return Optional.ofNullable(category).map(Category::getDisplaySequence).orElse(null);
    }

    public void updateShowStatus(List<Long> categoryIds, Boolean newShowStatus) {
        if (CollectionUtils.isEmpty(categoryIds) || null == newShowStatus) {
            return;
        }

        Category updateCategory = new Category();
        updateCategory.setWhetherShow(newShowStatus);
        MybatisUtil.executeBatch(ids -> update(updateCategory, new LambdaQueryWrapper<Category>().in(Category::getId, ids)), categoryIds);
    }

    public List<Category> getAllCategoryPath(List<Long> categoryIds) {
       return categoryExtMapper.selectAllCategoryPath(categoryIds);
    }

    public List<Category> getThreeDepthByPath(String path) {
        LambdaQueryWrapper<Category> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.likeRight(Category::getPath, path);
        queryWrapper.eq(Category::getDepth,3);
        queryWrapper.eq(Category::getWhetherDelete,false);
        return baseMapper.selectList(queryWrapper);
    }
}
