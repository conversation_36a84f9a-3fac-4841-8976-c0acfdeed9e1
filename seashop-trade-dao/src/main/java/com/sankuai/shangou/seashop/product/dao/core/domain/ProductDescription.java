package com.sankuai.shangou.seashop.product.dao.core.domain;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 商品详情表
 * </p>
 *
 * <AUTHOR> @since 2024-03-20
 */
public class ProductDescription implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id")
    private Long id;

    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 审核原因
     */
    @TableField("audit_reason")
    private String auditReason;

    /**
     * PC端详情
     */
    @TableField("description")
    private String description;

    /**
     * 关联顶部版式
     */
    @TableField("description_prefix_id")
    private Long descriptionPrefixId;

    /**
     * 关联底部版式
     */
    @TableField("description_suffix_id")
    private Long descriptionSuffixId;

    /**
     * 移动端描述
     */
    @TableField("mobile_description")
    private String mobileDescription;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否已删除
     */
    @TableField("deleted")
    private Boolean deleted;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getAuditReason() {
        return auditReason;
    }

    public void setAuditReason(String auditReason) {
        this.auditReason = auditReason;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getDescriptionPrefixId() {
        return descriptionPrefixId;
    }

    public void setDescriptionPrefixId(Long descriptionPrefixId) {
        this.descriptionPrefixId = descriptionPrefixId;
    }

    public Long getDescriptionSuffixId() {
        return descriptionSuffixId;
    }

    public void setDescriptionSuffixId(Long descriptionSuffixId) {
        this.descriptionSuffixId = descriptionSuffixId;
    }


    public String getMobileDescription() {
        return mobileDescription;
    }

    public void setMobileDescription(String mobileDescription) {
        this.mobileDescription = mobileDescription;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Override
    public String toString() {
        return "ProductDescription{" +
        "id=" + id +
        ", productId=" + productId +
        ", auditReason=" + auditReason +
        ", description=" + description +
        ", descriptionPrefixId=" + descriptionPrefixId +
        ", descriptionSuffixId=" + descriptionSuffixId +
        ", mobileDescription=" + mobileDescription +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", deleted=" + deleted +
        "}";
    }
}
