<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hishop.himall</groupId>
        <artifactId>himall-base</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <packaging>jar</packaging>

    <artifactId>seashop-base-user-auth</artifactId>
    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>


    <dependencies>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-user-account</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-user-shop</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hishop-xxl-job-client-boot-starter</artifactId>
                    <groupId>com.hishop.starter</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
</project>