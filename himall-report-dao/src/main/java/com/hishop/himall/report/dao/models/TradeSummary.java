package com.hishop.himall.report.dao.models;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;

@Getter
@Setter
public class TradeSummary {
    private LocalDate date;

    private Integer visitsUsers;
    private Integer visitsCount;

    private BigDecimal orderAmount;
    private Integer orderOrders;
    private Integer orderUsers;

    private BigDecimal paymentAmount;
    private Integer paymentQuantity;
    private Integer paymentOrders;
    private Integer paymentUsers;


    /**
     * @return 客单价
     */
    public BigDecimal getUnitPrice() {
        if (paymentUsers == null || paymentUsers==0) {
            return BigDecimal.ZERO;
        }
        if (paymentAmount == null) {
            return BigDecimal.ZERO;
        }
        return paymentAmount.divide(BigDecimal.valueOf(paymentUsers), 4, RoundingMode.HALF_UP);
    }

    /**
     * 订单-访客转化率
     */
    public BigDecimal getOrderVisitsRate() {
        if (visitsUsers == null || visitsUsers==0) {
            return BigDecimal.ZERO;
        }
        if(orderUsers == null || orderUsers == 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf( orderUsers).divide(BigDecimal.valueOf(visitsUsers), 4, RoundingMode.HALF_UP);
    }

    /**
     * 支付-下单转化率
     *
     * @return
     */
    public BigDecimal getPaymentOrderRate() {
        if (orderUsers == null || orderUsers == 0) {
            return BigDecimal.ZERO;
        }
        if(paymentUsers == null || paymentUsers == 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(paymentUsers).divide(BigDecimal.valueOf(orderUsers), 4, RoundingMode.HALF_UP);
    }

    /**
     * 支付-访客转化率
     */
    public BigDecimal getPaymentVisitsRate() {
        if (visitsUsers == null || visitsUsers==0) {
            return BigDecimal.ZERO;
        }
        if(paymentUsers == null || paymentUsers == 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(paymentUsers).divide(BigDecimal.valueOf(visitsUsers), 4, RoundingMode.HALF_UP);
    }
}
