# web服务端口号
server:
  port: 8080

spring:
  application:
    name: himall-trade
  profiles:
    active: develop_local
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  redis:
    database: 1
  cloud:
    nacos:
      server-addr: ${NACOS_SERVER:124.71.221.117:8848}
      username: ${NACOS_USERNAME:dev}
      password: ${NACOS_PASSWORD:dev@hishop}
      config:
        namespace: ${spring.profiles.active}
        group: 1.0.0
      discovery:
        namespace: ${spring.profiles.active}
        group: 1.0.0
  config:
    import:
      - optional:nacos:himall-common.yml
      - optional:nacos:${spring.application.name}.yml
  dynamic:
    tp:
      enabled: true
      enabled-collect: false
      executors:
        - thread-pool-name: tradeAsyncExecutor
          thread-pool-alias-name: trade.async
          core-pool-size: 10
          maximum-pool-size: 20
          keep-alive-time: 60000
          queue-capacity: 500000
          thread-name-prefix: trade-asyncApi
          task-wrapper-names:
            - ttl
            - mdc
        - thread-pool-name: esBuildOrderExecutor
          thread-pool-alias-name: esBuild.order
          core-pool-size: 10
          maximum-pool-size: 20
          keep-alive-time: 60000
          queue-capacity: 500000
          thread-name-prefix: es-build-order
          task-wrapper-names:
            - ttl
            - mdc
        - thread-pool-name: esBuildRefundExecutor
          thread-pool-alias-name: esBuild.refund
          core-pool-size: 10
          maximum-pool-size: 20
          keep-alive-time: 60000
          queue-capacity: 500000
          thread-name-prefix: es-build-refund
          task-wrapper-names:
            - ttl
            - mdc
nfs:
  cos:
    enabled: true
    access-key-id: AKIDvf9NL3UhfNoztWsxoPjpleNtBE4g290a
    access-key-secret: NWP1Vs7B6oHTO2Xu8FDyUkjSu52j9H8i
    bucketName: meituan-1254179351
    region: ap-chongqing
    domain: https://meituan-1254179351.cos.ap-chongging.nygcloud. con/tester
    prefix: tester/



