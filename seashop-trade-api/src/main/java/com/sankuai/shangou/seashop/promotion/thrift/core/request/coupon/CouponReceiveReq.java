package com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

/**
 * @author: lhx
 * @date: 2023/11/9/009
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "优惠券领用请求对象")
public class CouponReceiveReq extends BaseParamReq {

    @Schema(description = "优惠券ID", required = true)
    private Long couponId;

    @Schema(description = "用户ID", required = true)
    private Long userId;

    @Schema(description = "用户名", required = true)
    private String userName;

    @Schema(description = "店铺ID", required = true)
    private Long shopId;

    @Schema(description = "名称", required = true)
    private String shopName;

    public void checkParameter() {
        if (this.couponId == null) {
            throw new InvalidParamException("couponId不能为空");
        }
        if (this.userId == null) {
            throw new InvalidParamException("userId不能为空");
        }
        if (StringUtils.isBlank(this.userName)) {
            throw new InvalidParamException("userName不能为空");
        }
        if (this.shopId == null) {
            throw new InvalidParamException("shopId不能为空");
        }
        if (StringUtils.isBlank(this.shopName)) {
            throw new InvalidParamException("shopName不能为空");
        }
    }


}
