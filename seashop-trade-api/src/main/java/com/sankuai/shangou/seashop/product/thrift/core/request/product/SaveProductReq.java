package com.sankuai.shangou.seashop.product.thrift.core.request.product;

import cn.hutool.core.collection.CollUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductSourceEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.SkuUpdateKeyEnum;
import com.sankuai.shangou.seashop.product.thrift.core.helper.ParameterHelper;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.dto.SaveProductLadderPriceDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.dto.SaveProductSkuDto;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/13 19:16
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "发布商品入参")
public class SaveProductReq extends BaseParamReq {

    @Schema(description = "商品id 新增时不填/填0")
    private Long productId;

    @Schema(description = "类目id", required = true)
    private Long categoryId;

    @Schema(description = "品牌id", required = true)
    private Long brandId;

    @Schema(description = "商品名称(限100字)", required = true)
    private String productName;

    @Schema(description = "广告词")
    private String shortDescription;

    @Schema(description = "是否开启阶梯价", required = true)
    private Boolean whetherOpenLadder;

    @Schema(description = "阶梯价")
    private List<SaveProductLadderPriceDto> ladderPriceList;

    @Schema(description = "商城价(可不传,多个规格的最小商城价,会获取sku集合中的最小商城价)")
    private BigDecimal minSalePrice;

    @Schema(description = "市场价")
    private BigDecimal marketPrice;

    @Schema(description = "库存(可不传,会计算sku集合中的库存之和)")
    private Long stock;

    @Schema(description = "商品货号(限100个字符)(同一店铺不能重复)", required = true)
    private String productCode;

    @Schema(description = "计量单位")
    private String measureUnit;

    @Schema(description = "限购数", required = true)
    private Integer maxBuyCount;

    @Schema(description = "倍数起购量", required = true)
    private Integer multipleCount;

    @Schema(description = "警戒库存(可不传, 会获取第一个规格的安全库存)")
    private Long safeStock;

    @Schema(description = "店铺分类", required = true)
    private List<Long> shopCategoryIdList;

    @Schema(description = "是否开启规格", required = true)
    private Boolean hasSku;

    @Schema(description = "sku集合 单规格有一条默认值")
    private List<SaveProductSkuDto> skuList;

    @Schema(description = "运费模板", required = true)
    private Long freightTemplateId;

    @Schema(description = "重量(根据运费模板确定是否必填)")
    private BigDecimal weight;

    @Schema(description = "体积(根据运费模板确定是否必填)")
    private BigDecimal volume;

    @Schema(description = "主图集合(最多五张)", required = true)
    private List<String> imageList;

    @Schema(description = "主图视频")
    private String videoPath;

    @Schema(description = "PC端描述", required = true)
    private String description;

    @Schema(description = "移动端描述", required = true)
    private String mobileDescription;

    @Schema(description = "顶部版式Id")
    private Long descriptionPrefixId;

    @Schema(description = "底部版式Id")
    private Long descriptionSuffixId;

    @Schema(description = "是否保存到草稿")
    private Boolean draftFlag;

    @Schema(description = "店铺id", required = true)
    private Long shopId;

    @Schema(description = "变动来源(默认商城) 1-商城 2-牵牛花 3-易久批")
    private Integer changeSource;

    @Schema(description = "sku更新key")
    private SkuUpdateKeyEnum skuUpdateKey;

    @Schema(description = "运费模板定价方式 0:按件数，1：按重量，2：按体积计算")
    private Integer freightTemplateIdMethod;

    public void checkParameter(boolean partSave) {
        initDefault();

        // 如果是部分保存,只需要校验基本的属性
        checkCoreParam();

        if (!partSave) {
            checkFirstStep();
            checkSecondStep();
        }

        // 保存草状态设置默认值
        draftFlag = ObjectUtils.defaultIfNull(draftFlag, partSave);
    }

    public void checkCoreParam() {
        // 部分保存只需要保证基本的属性不为空即可
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(productCode), "商品货号不能为空");
        // 检验货号的格式
        if (StringUtils.isNotEmpty(productCode)) {
            AssertUtil.throwInvalidParamIfTrue(!ParameterHelper.checkProductCode(productCode), "货号格式不正确, 只能为数字、字母、-");
        }
        if (StringUtils.isNotEmpty(measureUnit)) {
            AssertUtil.throwInvalidParamIfTrue(!ParameterHelper.checkMeasureUnit(measureUnit), "计量单位格式不正确, 不能包含数字");
        }
        if (marketPrice != null) {
            AssertUtil.throwInvalidParamIfTrue(!ParameterHelper.checkMarketPrice(marketPrice), String.format("市场价范围为%s-%s", ParameterConstant.MIN_MARKET_PRICE, ParameterConstant.MAX_MARKET_PRICE));
        }
        if (maxBuyCount != null) {
            AssertUtil.throwInvalidParamIfTrue(!ParameterHelper.checkMaxBuyCount(maxBuyCount), String.format("限购数范围为%s-%s", ParameterConstant.MIN_MAX_BUY_COUNT, ParameterConstant.MAX_MAX_BUY_COUNT));
        }
        if (multipleCount != null) {
            AssertUtil.throwInvalidParamIfTrue(!ParameterHelper.checkMultipleCount(multipleCount), String.format("倍数起购量范围为%s-%s", ParameterConstant.MIN_MULTIPLE_COUNT, ParameterConstant.MAX_MULTIPLE_COUNT));
        }

        AssertUtil.throwInvalidParamIfNull(shopId, "shopId 不能为空");
        if (CollectionUtils.isNotEmpty(skuList)) {
            skuList.forEach(sku -> {
                // 如果开启了阶梯价，则销售价无效
                if (Boolean.TRUE.equals(whetherOpenLadder)) {
                    sku.setSalePrice(null);
                }

                sku.checkCoreParam(skuUpdateKey);
            });
        }
    }

    public void initDefault() {
        changeSource = ObjectUtils.defaultIfNull(changeSource, ProductSourceEnum.MALL.getCode());
        skuUpdateKey = ObjectUtils.defaultIfNull(skuUpdateKey, SkuUpdateKeyEnum.SKU_ID);
    }

    private void checkFirstStep() {
        AssertUtil.throwInvalidParamIfTrue(categoryId != null && categoryId <= 0, "请选择平台类目");
        AssertUtil.throwInvalidParamIfTrue(brandId != null && brandId <= 0, "请选择品牌");
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(productName), "商品名称不能为空");
        AssertUtil.throwInvalidParamIfTrue(productName.length() > ParameterConstant.PRODUCT_NAME_LENGTH,
                String.format("商品名称不能超过%s字", ParameterConstant.PRODUCT_NAME_LENGTH));
        AssertUtil.throwInvalidParamIfNull(whetherOpenLadder, "请选择是否开启阶梯价");
        if (whetherOpenLadder) {
            AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(ladderPriceList), "阶梯价不能为空");
            AssertUtil.throwInvalidParamIfTrue(ladderPriceList.size() > ParameterConstant.MAX_LADDER_PRICE_SIZE,
                    "阶梯价不能超过" + ParameterConstant.MAX_LADDER_PRICE_SIZE + "个");
            ladderPriceList.forEach(item -> item.checkParameter());
        }
        // 市场价默认0
        marketPrice = ObjectUtils.defaultIfNull(marketPrice, BigDecimal.ZERO);
        AssertUtil.throwInvalidParamIfNull(hasSku, "请选择是否开启规格");

        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(productCode), "商品货号不能为空");
        AssertUtil.throwInvalidParamIfTrue(productCode.length() > ParameterConstant.PRODUCT_CODE_LENGTH,
                String.format("商品货号不能超过%s字", ParameterConstant.PRODUCT_CODE_LENGTH));
        AssertUtil.throwInvalidParamIfNull(maxBuyCount, "限购数不能为空");
        AssertUtil.throwInvalidParamIfTrue(maxBuyCount < ParameterConstant.ZERO, "限购数不能为负数");
        AssertUtil.throwInvalidParamIfNull(multipleCount, "倍数起购量不能为空");
        AssertUtil.throwInvalidParamIfTrue(multipleCount < ParameterConstant.PRODUCT_MULTIPLE_COUNT_MIN,
                String.format("倍数起购量不能小于%s", ParameterConstant.PRODUCT_MULTIPLE_COUNT_MIN));
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(shopCategoryIdList), "请选择店铺分类");
        AssertUtil.throwInvalidParamIfTrue(freightTemplateId == null || freightTemplateId <= 0, "请选择运费模板");
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(skuList), "请至少添加一个商品规格");
        AssertUtil.throwInvalidParamIfNull(ProductSourceEnum.getByCode(changeSource), "未知的变动来源");
    }

    private void checkSecondStep() {
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(imageList), "请至少上传一张商品主图");
        AssertUtil.throwInvalidParamIfTrue(imageList.size() > 5, "商品主图不能超过5张");
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(description), "PC端描述不能为空");
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "店铺id不能为空");
    }


}
