package com.sankuai.shangou.seashop.product.thrift.core.request.product;

import java.util.Date;
import java.util.List;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductStatusEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/11/16 12:43
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "查询商品入参")
public class QueryProductReq extends BasePageReq {

    @Schema(description = "商品id")
    private Long productId;

    @Schema(description = "规格自增id")
    private Long skuAutoId;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品编码")
    private String productCode;

    @Schema(description = "店铺分类id")
    private Long shopCategoryId;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "警戒库存")
    private Boolean whetherBelowSafeStock;

    @Schema(description = "商品状态 1-销售中 2-仓库中 3-违规下架 4-草稿箱")
    private ProductStatusEnum status;

    @Schema(description = "商品状态列表")
    private List<ProductStatusEnum> inStatus;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "店铺id")
    private Long shopId;

    @Schema(description = "类目路径 | 隔开")
    private String categoryPath;

    @Schema(description = "排序字段")
    private List<FieldSortReq> sortList;

    @Schema(description = "商品id列表")
    private List<Long> productIds;

    @Schema(description = "类目id列表")
    private List<Long> categoryIds;

    @Schema(description = "运费模板id")
    private Long freightTemplateId;

    @Schema(description = "来源 1-商城 2-牵牛花 3-易久批")
    private Integer source;

    @Schema(description = "是否开启阶梯价")
    private Boolean whetherOpenLadder;

    @Schema(description = "排除商品id列表")
    private List<Long> excludeProductIds;

    @Schema(description = "是否有库存")
    private Boolean hasStock;

    @Schema(description = "滚动id")
    private String scrollId;

    @Schema(description = "是否使用滚动查询(默认不使用)")
    private Boolean useScroll;

    @Schema(description = "滚动查询保留时间(分钟)")
    private Integer keepAliveMinutes;

    @Schema(description = "类目id")
    private Long categoryId;

    @Schema(description = "规格名称id列表")
    private List<Long> specNameIds;

    @Schema(description = "是否需要h5链接")
    private Boolean needH5Url;

    @Schema(description = "父类目id")
    private Long parentCategoryId;

    @Override
    public void checkParameter() {
        if (useScroll == null || !useScroll) {
            AssertUtil.throwInvalidParamIfTrue((long) getPageNo() * getPageSize() > ParameterConstant.ES_SEARCH_LIMIT, "查询失败, 请输入更精准的查询条件");
        }
    }


}
