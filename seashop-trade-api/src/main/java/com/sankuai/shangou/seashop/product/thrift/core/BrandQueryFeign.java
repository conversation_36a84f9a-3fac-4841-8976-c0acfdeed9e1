package com.sankuai.shangou.seashop.product.thrift.core;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.BatchQueryBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryNotApplyBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryRecommendBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.BrandListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.NotApplyBrandGroupResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.NotApplyBrandResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto.BrandDto;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/11/01 16:12
 * @description:品牌查询服务
 */
@FeignClient(name = "himall-trade", contextId = "BrandQueryFeign", path = "/himall-trade/brand", url = "${himall-trade.dev.url:}")
public interface BrandQueryFeign {

    /**
     * 查询品牌
     */
    @PostMapping(value = "/queryBrandForPage", consumes = "application/json")
    ResultDto<BasePageResp<BrandDto>> queryBrandForPage(@RequestBody QueryBrandReq request) throws TException;

    /**
     * 查询品牌详情
     */
    @PostMapping(value = "/queryBrandDetail", consumes = "application/json")
    ResultDto<BrandDto> queryBrandDetail(@RequestBody BaseIdReq request) throws TException;

    /**
     * 批量查询品牌列表
     */
    @PostMapping(value = "/queryBrandList", consumes = "application/json")
    ResultDto<BrandListResp> queryBrandList(@RequestBody BatchQueryBrandReq request) throws TException;

    /**
     * 查询没有申请记录的品牌列表
     */
    @PostMapping(value = "/queryNotApplyBrand", consumes = "application/json")
    ResultDto<NotApplyBrandResp> queryNotApplyBrand(@RequestBody QueryNotApplyBrandReq request) throws TException;

    /**
     * 查询没有申请记录的品牌分组
     */
    @PostMapping(value = "/queryNotApplyBrandGroup", consumes = "application/json")
    ResultDto<NotApplyBrandGroupResp> queryNotApplyBrandGroup(@RequestBody QueryNotApplyBrandReq request) throws TException;

    /**
     * 查询推荐品牌
     */
    @PostMapping(value = "/queryRecommendBrand", consumes = "application/json")
    ResultDto<BrandListResp> queryRecommendBrand(@RequestBody QueryRecommendBrandReq request) throws TException;

}
