package com.sankuai.shangou.seashop.product.thrift.core.response.product.model;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/13 19:16
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "商品详情")
public class ProductDetailDto extends BaseThriftDto {

    @Schema(description = "商品id")
    private Long productId;

    @Schema(description = "类目id")
    private Long categoryId;

    @Schema(description = "类目名称")
    private String categoryName;

    @Schema(description = "类目全路径名称")
    private String fullCategoryName;

    @Schema(description = "类目全路径id")
    private List<Long> fullCategoryIds;

    @Schema(description = "品牌id")
    private Long brandId;

    @Schema(description = "商品名称(限100字)")
    private String productName;

    @Schema(description = "广告词")
    private String shortDescription;

    @Schema(description = "是否开启阶梯价")
    private Boolean whetherOpenLadder;

    @Schema(description = "阶梯价")
    private List<LadderPriceDto> ladderPriceList;

    @Schema(description = "商城价(多个规格的最小商城价,会获取sku集合中的最小商城价)")
    private BigDecimal minSalePrice;

    @Schema(description = "市场价")
    private BigDecimal marketPrice;

    @Schema(description = "库存(会计算sku集合中的库存之和)")
    private Long stock;

    @Schema(description = "商品货号(限100个字符)(同一店铺不能重复)")
    private String productCode;

    @Schema(description = "计量单位")
    private String measureUnit;

    @Schema(description = "限购数")
    private Integer maxBuyCount;

    @Schema(description = "倍数起购量")
    private Integer multipleCount;

    @Schema(description = "警戒库存(会获取第一个规格的安全库存)")
    private Long safeStock;

    @Schema(description = "店铺分类")
    private List<Long> shopCategoryIdList;

    @Schema(description = "是否开启规格")
    private Boolean hasSku;

    @Schema(description = "sku集合 单规格有一条默认值")
    private List<ProductDetailSkuDto> skuList;

    @Schema(description = "运费模板")
    private Long freightTemplateId;

    @Schema(description = "重量(根据运费模板确定是否有值)")
    private BigDecimal weight;

    @Schema(description = "体积(根据运费模板确定是否有值)")
    private BigDecimal volume;

    @Schema(description = "主图集合(最多五张)")
    private List<String> imageList;

    @Schema(description = "主图视频")
    private String videoPath;

    @Schema(description = "PC端描述")
    private String description;

    @Schema(description = "移动端描述")
    private String mobileDescription;

    @Schema(description = "顶部版式Id")
    private Long descriptionPrefixId;

    @Schema(description = "底部版式Id")
    private Long descriptionSuffixId;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "来源 1-商城 2-牵牛花 3-易久批")
    private Integer source;

    @Schema(description = "来源描述")
    private String sourceDesc;

    @Schema(description = "商品状态 1-销售中 2-仓库中 3-草稿箱 4-违规下架")
    private Integer status;

    @Schema(description = "商品状态描述")
    private String statusDesc;

    @Schema(description = "最高售价")
    private BigDecimal maxSalePrice;

    @Schema(description = "售价范围")
    private String salePriceRange;

    @Schema(description = "销售量")
    private Long saleCounts;

    @Schema(description = "虚拟销量")
    private Long virtualSaleCounts;

    @Schema(description = "品牌logo")
    private String brandLogo;

    @Schema(description = "第一个规格的自增id")
    private Long skuAutoId;

    @Schema(description = "规格1 别名")
    private String spec1Alias;

    @Schema(description = "规格2 别名")
    private String spec2Alias;

    @Schema(description = "规格3 别名")
    private String spec3Alias;


    public String getSpec1Alias() {
        return spec1Alias;
    }

    public void setSpec1Alias(String spec1Alias) {
        this.spec1Alias = spec1Alias;
    }

    public String getSpec2Alias() {
        return spec2Alias;
    }

    public void setSpec2Alias(String spec2Alias) {
        this.spec2Alias = spec2Alias;
    }

    public String getSpec3Alias() {
        return spec3Alias;
    }

    public void setSpec3Alias(String spec3Alias) {
        this.spec3Alias = spec3Alias;
    }
}
