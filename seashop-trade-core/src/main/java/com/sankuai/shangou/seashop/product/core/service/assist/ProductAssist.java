package com.sankuai.shangou.seashop.product.core.service.assist;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.lock.DistributedLockService;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.product.common.es.model.product.EsProductModel;
import com.sankuai.shangou.seashop.product.common.es.model.product.EsProductParam;
import com.sankuai.shangou.seashop.product.common.es.service.EsProductService;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerProcessor;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.TransactionEventPublisher;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.event.SendProductChangeEvent;
import com.sankuai.shangou.seashop.product.core.service.converter.EsProductConverter;
import com.sankuai.shangou.seashop.product.core.service.hepler.CompareHelper;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductLadderPriceBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductPageBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductQueryBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductDescription;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductDescriptionTemplate;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductImage;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductImageAudit;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductShopCategory;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductDescriptionRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductDescriptionTemplateRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductImageAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductImageRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductShopCategoryRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuRepository;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductSourceEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.result.ProductResultEnum;
import com.sankuai.shangou.seashop.product.thrift.core.event.product.type.ProductChangeType;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/12 10:14
 */
@Component
@Slf4j
public class ProductAssist {

    @Resource
    private ProductRepository productRepository;
    @Resource
    private ProductImageRepository productImageRepository;
    @Resource
    private ProductImageAuditRepository productImageAuditRepository;
    @Resource
    private ProductShopCategoryRepository productShopCategoryRepository;
    @Resource
    private ProductHandlerProcessor productHandlerProcessor;
    @Resource
    private EsProductService esProductService;
    @Resource
    private ProductExtAssist productExtAssist;
    @Resource
    private ProductDescriptionRepository productDescriptionRepository;
    @Resource
    private SkuRepository skuRepository;
    @Resource
    private DistributedLockService distributedLockService;
    @Resource
    private LadderPriceAssist ladderPriceAssist;
    @Resource
    private ProductDescriptionTemplateRepository productDescriptionTemplateRepository;
    @Resource
    private TransactionEventPublisher transactionEventPublisher;

    /**
     * 从es中查询商品列表
     *
     * @param pageParam 分页参数
     * @param queryBo   查询参数
     * @return 商品列表
     */
    public BasePageResp<ProductPageBo> pageEsProduct(BasePageParam pageParam, ProductQueryBo queryBo) {
        BasePageResp<EsProductModel> esProductResult = esProductService.page(pageParam, JsonUtil.copy(queryBo, EsProductParam.class), queryBo.getSortList());
        productExtAssist.coverProductData(Optional.ofNullable(esProductResult).map(BasePageResp::getData).orElse(null));
        BasePageResp<ProductPageBo> resp = PageResultHelper.transfer(esProductResult, model -> EsProductConverter.esModelToBo(model));
        List<ProductPageBo> data = resp.getData();
        productExtAssist.fillProductExtData(queryBo, data, false);
        return resp;
    }

    /**
     * 从es中查询商品列表
     *
     * @param queryBo 查询参数
     * @return 商品列表
     */
    public List<ProductPageBo> listEsProduct(ProductQueryBo queryBo) {
        List<EsProductModel> esProductList = esProductService.list(JsonUtil.copy(queryBo, EsProductParam.class));
        List<ProductPageBo> productList = esProductList.stream().map(EsProductConverter::esModelToBo).collect(Collectors.toList());
        productExtAssist.fillProductExtData(queryBo, productList, false);
        return productList;
    }

    /**
     * 查询商品id的集合
     *
     * @param queryBo 查询参数
     * @return 商品id集合
     */
    public List<Long> listProductIds(ProductQueryBo queryBo) {
        List<EsProductModel> esProductList = esProductService.list(JsonUtil.copy(queryBo, EsProductParam.class));
        List<Long> productIds = esProductList.stream().map(EsProductModel::getProductId).collect(Collectors.toList());
        return productIds;
    }

    /**
     * 检查商品权限
     *
     * @param productId          商品id
     * @param shopId             商铺id
     * @param ignoreIfShopIdNull 当shopId为空是是否忽略鉴权
     * @return 商品
     */
    public Product checkProductAuth(Long productId, Long shopId, boolean ignoreIfShopIdNull) {
        Product product = productRepository.getByProductId(productId);
        AssertUtil.throwIfTrue(product == null || product.getWhetherDelete(), ProductResultEnum.PRODUCT_NOT_EXIST);
        if (ignoreIfShopIdNull && shopId == null) {
            return product;
        }
        AssertUtil.throwIfTrue(!product.getShopId().equals(shopId), ProductResultEnum.NO_PRODUCT_AUTH);
        return product;
    }

    /**
     * 检验店铺权限并查询商品列表
     *
     * @param productIdList      商品id列表
     * @param shopId             商铺id
     * @param ignoreIfShopIdNull 当shopId为空是是否忽略鉴权
     * @return 商品列表
     */
    public List<Product> checkShopAndListProduct(List<Long> productIdList, Long shopId, boolean ignoreIfShopIdNull) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return Collections.emptyList();
        }
        List<Product> productList = productRepository.listByProductIds(productIdList);
        if (ignoreIfShopIdNull && shopId == null) {
            return productList;
        }
        long count = productList.stream().filter(product -> !product.getShopId().equals(shopId)).count();
        AssertUtil.throwIfTrue(count > 0, ProductResultEnum.NO_PRODUCT_AUTH);
        return productList;
    }

    /**
     * 检验店铺权限并查询商品列表
     *
     * @param productIdList 商品id列表
     * @param shopId        商铺id
     * @return 商品列表
     */
    public List<Product> checkShopAndListProduct(List<Long> productIdList, Long shopId) {
        return checkShopAndListProduct(productIdList, shopId, Boolean.TRUE);
    }

    /**
     * 检测货号是否重复
     *
     * @param shopId      商铺id
     * @param productCode 货号
     * @param productId   商品id
     */
    public void checkProductCode(Long shopId, String productCode, Long productId) {
        int count = countProductCount(shopId, productCode, productId);
        AssertUtil.throwIfTrue(count > 0, ProductResultEnum.PRODUCT_CODE_EXIST);
    }

    /**
     * 统计货号数量
     *
     * @param shopId      商铺id
     * @param productCode 货号
     * @param productId   商品id
     * @return 货号数量
     */
    public int countProductCount(Long shopId, String productCode, Long productId) {
        return Math.toIntExact(productRepository.count(new LambdaQueryWrapper<Product>()
            .eq(Product::getShopId, shopId)
            .eq(Product::getProductCode, productCode)
            .eq(Product::getWhetherDelete, Boolean.FALSE)
            .ne(productId != null, Product::getProductId, productId)));
    }

    /**
     * 获取商品图片列表
     *
     * @param productId 商品id
     * @return 图片列表
     */
    public List<String> getProductImgList(Long productId) {
        return productImageRepository.listObjs(new LambdaQueryWrapper<ProductImage>()
                .eq(ProductImage::getProductId, productId).select(ProductImage::getImageUrl), String::valueOf);
    }

    /**
     * 获取商品图片审核列表
     *
     * @param productId 商品id
     * @return 图片列表
     */
    public List<String> getProductImgAuditList(Long productId) {
        return productImageAuditRepository.listObjs(new LambdaQueryWrapper<ProductImageAudit>()
                .eq(ProductImageAudit::getProductId, productId).select(ProductImageAudit::getImageUrl), String::valueOf);
    }

    /**
     * 获取商品店铺分类id列表
     *
     * @param productId 商品id
     * @return 店铺分类id列表
     */
    public List<Long> getShopCategoryIds(Long productId) {
        return productShopCategoryRepository.listObjs(new LambdaQueryWrapper<ProductShopCategory>()
                .eq(ProductShopCategory::getProductId, productId).select(ProductShopCategory::getShopCategoryId), item -> Long.parseLong(String.valueOf(item)));
    }

    /**
     * 获取审核原因
     *
     * @param productId 商品id
     * @return 审核原因
     */
    public String getAuditReason(Long productId) {
        ProductDescription desc = productDescriptionRepository.getProductDescriptionByProductId(productId);
        return desc == null ? null : desc.getAuditReason();
    }

    /**
     * 获取审核原因
     *
     * @param product 商品
     * @return 审核原因
     */
    public String getAuditReason(Product product) {
        List<String> auditReasonList = new ArrayList<>();
        String auditReason = getAuditReason(product.getProductId());
        if (StringUtils.isNotEmpty(auditReason)) {
            auditReasonList.add(auditReason);
        }
        return StrUtil.join(";", auditReasonList);
    }

    /**
     * 根据商品id获取商品信息
     *
     * @param productIdList 商品的集合
     * @return 商品信息
     */
    public Map<Long, Product> getProductMap(List<Long> productIdList) {
        return productRepository.getProductMap(productIdList);
    }

    /**
     * 计算最低销售价格
     * 获取阶梯价和sku价格的最小值
     */
    public BigDecimal calculateMinSalePrice(Boolean openLadderPrice, Long productId) {
        if (openLadderPrice != null && openLadderPrice) {
            return ladderPriceAssist.getMinLadderPrice(productId);
        }

        return skuRepository.getMinSalePrice(productId);
    }

    /**
     * 计算最高销售价格
     * 获取阶梯价和sku价格的最大值
     */
    public BigDecimal calculateMaxSalePrice(Long productId) {
        BigDecimal maxLadderPrice = ladderPriceAssist.getMaxLadderPrice(productId);
        if (maxLadderPrice != null) {
            return maxLadderPrice;
        }

        return skuRepository.getMaxSalePrice(productId);
    }

    /**
     * 提取最低售价
     *
     * @param productBo 商品对象
     * @return 最低售价
     */
    public BigDecimal getMinSalePrice(ProductBo productBo) {
        // 开启了阶梯价
        if (productBo.getWhetherOpenLadder() != null && productBo.getWhetherOpenLadder()) {
            List<ProductLadderPriceBo> ladderPriceList = Optional.ofNullable(productBo.getLadderPriceList()).orElse(Collections.emptyList());
            return ladderPriceList.stream().map(ProductLadderPriceBo::getPrice).filter(Objects::nonNull).min(BigDecimal::compareTo).orElse(null);
        }

        // 获取sku里面的最小值
        List<ProductSkuBo> skuList = Optional.ofNullable(productBo.getSkuList()).orElse(Collections.emptyList());
        return skuList.stream().map(ProductSkuBo::getSalePrice).filter(Objects::nonNull).min(BigDecimal::compareTo).orElse(null);
    }

    /**
     * 获取最高售价
     *
     * @param productBo 商品对象
     * @return 最高售价
     */
    public BigDecimal getMaxSalePrice(ProductBo productBo) {
        List<ProductSkuBo> skuList = productBo.getSkuList();
        List<ProductLadderPriceBo> ladderPriceList = productBo.getLadderPriceList();

        List<BigDecimal> priceList = new ArrayList<>();

        // 获取sku里面的最大值
        if (CollectionUtils.isNotEmpty(skuList)) {
            priceList.addAll(skuList.stream().map(ProductSkuBo::getSalePrice).filter(salePrice -> salePrice != null).collect(Collectors.toList()));
        }

        // 获取阶梯价里面的最大值
        if (CollectionUtils.isNotEmpty(ladderPriceList)) {
            priceList.addAll(ladderPriceList.stream().map(ProductLadderPriceBo::getPrice).filter(salePrice -> salePrice != null).collect(Collectors.toList()));
        }
        return priceList.stream().max(BigDecimal::compareTo).orElse(null);
    }

    /**
     * 提交审批
     *
     * @param productId 商品id
     */
    public void submitAudit(Long productId) {
        ProductContext context = ProductContext.builder().productId(productId).build();

        // 先查询详情
        productHandlerProcessor.handle(ProductHandlerType.QUERY_PRODUCT_DETAIL, context);

        // 再提交风控
        context.setAuditProductBo(context.getOldProductBo());
        context.setShopId(context.getOldProductBo().getShopId());
        context.setNeedAudit(Boolean.TRUE);
        productHandlerProcessor.handle(ProductHandlerType.SUBMIT_PRODUCT_AUDIT, context);
    }

    /**
     * 获取价格范围
     *
     * @param minSalePrice 最小售价
     * @param maxSalePrice 最大售价
     * @return 价格范围字符串
     */
    public String getSalePriceRange(BigDecimal minSalePrice, BigDecimal maxSalePrice) {
        if (minSalePrice != null && maxSalePrice != null && !minSalePrice.equals(maxSalePrice)) {
            return String.format("%s-%s", minSalePrice, maxSalePrice);
        }
        return minSalePrice == null ? null : minSalePrice.toString();
    }

    /**
     * 比较图片是否发生了变动
     * 思路: 将两组图片按照相同规则排序后转换出字符串比较
     *
     * @param newImgList 新的图片列表
     * @param oldImgList 旧的图片列表
     * @return true: 没有变动 false: 发生了变动
     */
    public boolean compareImgList(List<String> newImgList, List<String> oldImgList) {
        if (!org.springframework.util.CollectionUtils.isEmpty(newImgList)) {
            newImgList.sort(String::compareTo);
        }

        if (!org.springframework.util.CollectionUtils.isEmpty(oldImgList)) {
            oldImgList.sort(String::compareTo);
        }
        return CompareHelper.compareSame(newImgList, oldImgList, String::toString);
    }

    public Long getValidDescriptionTemplateId(Long templateId) {
        if (templateId == null || templateId <= 0) {
            return null;
        }

        ProductDescriptionTemplate template = productDescriptionTemplateRepository.getById(templateId);
        return template == null ? null : template.getId();
    }

    public void sendProductChangeEvent(Long productId, ProductSourceEnum source, ProductChangeType changeType) {
        log.info("sendProductChangeEvent, productId: {}, source: {}, changeType: {}", productId, source, changeType);
        Product product = productRepository.getByProductId(productId);
        if (product == null) {
            log.info("sendProductChangeEvent, 商品不存在: {}", productId);
            return;
        }

        TransactionHelper.doInTransaction(() -> {
            SendProductChangeEvent event = SendProductChangeEvent
                    .build(product.getProductId(), product.getShopId(), source, changeType);
            transactionEventPublisher.publish(event);
        });
        log.info("sendProductChangeEvent, productId: {}, source: {}, changeType: {}, send success", productId, source, changeType);
    }
}
