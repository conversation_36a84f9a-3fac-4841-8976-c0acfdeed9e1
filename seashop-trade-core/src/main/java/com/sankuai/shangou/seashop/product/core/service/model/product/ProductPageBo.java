package com.sankuai.shangou.seashop.product.core.service.model.product;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/16 13:56
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductPageBo {

    /**
     * 商品Id
     */
    private Long productId;

    /**
     * 序号
     */
    private Long displaySequence;

    /**
     * 商品图片
     */
    private String imagePath;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 最小销售价
     */
    private BigDecimal minSalePrice;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 商品状态 1-销售中 2-仓库中 3-草稿箱 4-违规下架
     */
    private Integer status;

    /**
     * 状态
     */
    private String statusDesc;

    /**
     * 货号
     */
    private String productCode;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 库存
     */
    private Long stock;

    /**
     * 限购
     */
    private Integer maxBuyCount;

    /**
     * 实际销量
     */
    private Long saleCounts;

    /**
     * 虚拟销量
     */
    private Long virtualSaleCounts;

    /**
     * 发布时间
     */
    private Date addedDate;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 审核时间
     */
    private Date checkTime;

    /**
     * 店铺分类集合
     */
    private List<Long> shopCategoryIds;

    /**
     * 店铺分类名称集合 逗号隔开
     */
    private String shopCategoryNames;

    /**
     * 销售状态编码
     */
    public Integer saleStatusCode;

    /**
     * 销售状态描述
     */
    public String saleStatusDesc;

    /**
     * 审核状态编码
     */
    public Integer auditStatusCode;

    /**
     * 审核状态描述
     */
    private String auditStatusDesc;

    /**
     * 关联商品id的集合
     */
    private List<Long> relationProductIds;

    /**
     * 是否低于安全库存
     */
    private boolean whetherBelowSafeStock;

    /**
     * 市场价格
     */
    private BigDecimal marketPrice;

    /**
     * 规格1 别名
     */
    private String spec1Alias;

    /**
     * 规格2 别名
     */
    private String spec2Alias;

    /**
     * 规格3 别名
     */
    private String spec3Alias;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 商品单位
     */
    private String measureUnit;

    /**
     * 是否开启阶梯价
     */
    private Boolean whetherOpenLadder;

    /**
     * 倍数起购量
     */
    private Integer multipleCount;

    /**
     * 是否有sku
     */
    private Boolean hasSku;

    /**
     * 商品分类id集合
     */
    private List<Long> categoryIds;

    /**
     * 商品分类名称集合
     */
    private List<String> categoryNames;

    /**
     * 完整的类目名称
     */
    private String fullCategoryName;

    /**
     * 运费模板id
     */
    private Long freightTemplateId;

    /**
     * 运费模板名称
     */
    private String freightTemplateName;

    /**
     * 审核备注
     */
    private String auditReason;

    /**
     * 来源 1-商城 2-牵牛花 3-易久批
     */
    private Integer source;

    /**
     * 来源描述
     */
    private String sourceDesc;

    /**
     * 商家排序序号
     */
    private Integer shopDisplaySequence;

    /**
     * 最大售价
     */
    private BigDecimal maxSalePrice;

    /**
     * 价格区间
     */
    private String salePriceRange;

    /**
     * 是否删除
     */
    private Boolean whetherDelete;

    /**
     * 商品总库存
     */
    private Long totalStock;

    /**
     * 规格id的集合
     */
    private List<String> skuIds;

    /**
     * 广告词
     */
    private String shortDescription;

    /**
     * h5链接
     */
    private String h5Url;

    
    private Integer valuationMethod;


    /**
     * 图片集合
     */
    private List<String> imageList;

    /**
     * 上架时间
     */
    private Date onsaleTime;

}
