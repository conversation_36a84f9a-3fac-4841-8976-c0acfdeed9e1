package com.sankuai.shangou.seashop.product.core.service;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.product.core.service.model.BindDescriptionTemplateBo;
import com.sankuai.shangou.seashop.product.core.service.model.erp.ErpProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.erp.ErpQueryProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductPageBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductQueryBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductRichTextBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuMergeCombinationBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuQueryBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuUpdatePriceBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductUpdateVirtualSaleCountsBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductViolationOffSaleBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.QueryProductRichTextBo;
import com.sankuai.shangou.seashop.product.dao.core.model.ShopSaleCountsDto;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductSourceEnum;
import com.sankuai.shangou.seashop.product.thrift.core.event.product.type.ProductChangeType;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.*;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.CountProductTemplateResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.MStatisticalProductResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductPageResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductSkuStockResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.RecommendProductsResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.SaveProductResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.StatisticalProductResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.LadderPriceDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductBasicDto;

/**
 * <AUTHOR>
 * @date 2023/11/06 11:57
 */
public interface ProductService {

    /**
     * 发布/编辑商品
     *
     * @param productBo    商品信息
     * @param changeSource 变更来源
     * @param changeType   变更类型
     * @param partSave     是否是部分保存
     * @param skipRisk     审核自动通过
     * @return 保存的商品信息
     */
    SaveProductResp saveProduct(ProductBo productBo,
                                ProductSourceEnum changeSource,
                                ProductChangeType changeType,
                                boolean partSave,
                                boolean skipRisk);

    /**
     * 根据模板id判断是否有商品使用过该运费模板
     *
     * @param productTemplateReq
     * @return
     */
    Boolean queryProductByTemplateId(ProductTemplateReq productTemplateReq);

    /**
     * 根据模板id查询关联商品数
     *
     * @param template
     * @return
     */
    List<CountProductTemplateResp> queryProductCountByTemplateId(CountProductTemplateReq template);

    /**
     * 根据商品ID查询商品阶梯价
     *
     * @param productIdList
     * @return
     */
    List<LadderPriceDto> getLadderPriceBoList(List<Long> productIdList);

    /**
     * 分页查询商品列表
     *
     * @param pageParam 分页参数
     * @param queryBo   查询参数
     * @return 商品列表
     */
    BasePageResp<ProductPageBo> pageProduct(BasePageParam pageParam, ProductQueryBo queryBo);

    /**
     * 根据商品id查询商品列表
     *
     * @param productIds 商品id的集合
     * @param shopId     店铺id
     * @return 商品列表
     */
    List<ProductPageBo> queryProductById(List<Long> productIds, Long shopId);

    /**
     * 查询商品详情
     *
     * @param productId 商品id(美团自增组件生成的id, 非自增id)
     * @param shopId    店铺id
     * @return 商品详情
     */
    ProductBo queryProductDetail(Long productId, Long shopId);

    /**
     * 批量上下架商品
     *
     * @param onOffSaleParam 上下架参数
     */
    void batchOnOffSaleProduct(ProductOnOffSaleReq onOffSaleParam);

    /**
     * 批量删除商品
     *
     * @param deleteParam 删除参数
     */
    void batchDeleteProduct(ProductDeleteReq deleteParam);

    /**
     * 批量保存商品平台序号
     *
     * @param updateSequenceReq 保存序号入参
     */
    void batchSaveProductSequence(ProductUpdateSequenceReq updateSequenceReq);

    /**
     * 批量保存商品商家序号
     *
     * @param shopSequenceReq 店铺序号入参
     */
    void batchSaveProductShopSequence(ProductUpdateSequenceReq shopSequenceReq);

    /**
     * 批量关联版式
     *
     * @param bindBo 关联版式入参
     */
    void batchBindDescriptionTemplate(BindDescriptionTemplateBo bindBo);

    /**
     * 关联推荐商品
     *
     * @param bindBo 关联推荐商品入参
     */
    void bindRecommendProduct(BindRecommendProductReq bindBo);

    /**
     * 批量关联运费模板
     *
     * @param bindBo 批量关联运费模板bo
     */
    void batchBindFreightTemplate(BindFreightTemplateReq bindBo);

    /**
     * 批量更新商品价格
     *
     * @param priceBoList 商品价格更新入参
     * @param shopId      店铺id
     */
    void batchUpdateProductPrice(List<ProductSkuUpdatePriceBo> priceBoList, Long shopId);

    /**
     * 批量更新商品安全库存
     *
     * @param updateReq 更新安全库存入参
     */
    void batchUpdateSafeStock(ProductUpdateSafeStockReq updateReq);

    /**
     * 批量更新商品库存
     *
     * @param stockReq 规格集合
     */
    void batchUpdateStock(ProductUpdateStockReq stockReq);

    /**
     * 批量更新商品虚拟销量
     *
     * @param updateBo 商品虚拟销量更新入参
     */
    void batchUpdateVirtualSales(ProductUpdateVirtualSaleCountsBo updateBo);

    /**
     * 批量违规下架商品(平台端)
     *
     * @param violationReq 批量违规下架商品入参
     */
    void batchViolationOffSale(ProductViolationReq violationReq);

    /**
     * 批量违规下架商品
     *
     * @param violationProductList 批量违规下架商品列表
     */
    void batchViolationOffSale(List<ProductViolationOffSaleBo> violationProductList);

    /**
     * 下架所有供应商下的商品
     *
     * @param shopId 店铺id
     */
    void offSaleAllProduct(Long shopId);

    /**
     * 查询商品sku product 聚合数据
     *
     * @param queryBo 查询参数
     * @return 商品sku 聚合列表
     */
    ProductSkuMergeCombinationBo queryProductSkuMerge(ProductSkuQueryBo queryBo);

    /**
     * 查询商品信息(用于ERP)
     *
     * @param pageParam 分页参数
     * @param queryBo   查询参数
     * @return 商品信息
     */
    BasePageResp<ErpProductBo> queryProductForErp(BasePageParam pageParam, ErpQueryProductBo queryBo);

    /**
     * 查询推荐商品id的集合
     *
     * @param id 商品id
     * @return 推荐商品id的集合
     */
    List<Long> queryRecommendProductIds(Long id);

    /**
     * 供应商首页统计商品信息
     *
     * @param shopId
     * @return
     */
    StatisticalProductResp queryStatisticalProduct(Long shopId);

    /**
     * 平台首页统计商品信息
     *
     * @return
     */
    MStatisticalProductResp queryMStatisticalProduct();

    /**
     * 查询符合条件的商品id
     *
     * @param queryBo 查询参数
     * @return 商品id集合
     */
    List<Long> queryProductIds(ProductQueryBo queryBo);

    /**
     * 商家中心商品推荐
     *
     * @param req
     * @return
     */
    RecommendProductsResp queryRecommendProducts(RecommendProductsReq req);

    /**
     * 根据店铺和skuId列表批量查询库存
     *
     * @param skuAutoIds skuAutoId列表
     * @param skuCodes   skuCode列表
     * @param shopId     店铺id
     * @return 库存列表
     */
    List<ProductSkuStockResp> queryBatchStocks(List<Long> skuAutoIds,
                                               List<String> skuCodes, Long shopId);

    /**
     * 分页查询店铺库存
     *
     * @param pageParam 分页参数
     * @param shopId    店铺id
     * @return 店铺库存列表
     */
    BasePageResp<ProductSkuStockResp> queryShopStocksPage(BasePageParam pageParam, Long shopId);

    /**
     * 查询商品富文本信息
     *
     * @param queryBo 查询参数
     * @return 商品富文本信息
     */
    ProductRichTextBo queryProductRichText(QueryProductRichTextBo queryBo);

    /**
     * 根据商品id查询商品基础信息
     *
     * @param req 查询入参
     * @return 商品基础信息
     */
    List<ProductBasicDto> queryProductBasic(QueryProductBasicReq req);

    /**
     * 生成商品货号
     *
     * @return 商品货号
     */
    String generateProductCode();

    /**
     * 更新商品的销量
     *
     * @param req
     */
    void addSales(AddSaleCountReq req);

    /**
     * 查询店铺销量汇总(用于店铺es构建)
     *
     * @param shopId 店铺id
     */
    ShopSaleCountsDto queryShopSaleCounts(Long shopId);

    /**
     * 分页查询商品基本信息
     *
     * @param req 分页参数
     * @return 商品基本信息
     */
    BasePageResp<ProductBasicDto> pageProductBasic(QueryProductBasicReq req);

    /**
     * 查询mysql商品的集合(主要用于被删除的商品查询)
     *
     * @param request
     * @return
     */
    List<ProductPageResp> queryProductByIds(QueryProductByIdsReq request);

    /**
     * 清空滚动Id
     *
     * @param scrollId
     */
    void clearScrollId(String scrollId);

    /**
     * 查询商品sku product 聚合数据(无营销)
     */
    ProductSkuMergeCombinationBo queryProductSkuMergeNoneMarket(ProductSkuQueryReq queryBo);
}
