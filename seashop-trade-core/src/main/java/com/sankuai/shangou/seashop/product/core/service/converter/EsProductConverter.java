package com.sankuai.shangou.seashop.product.core.service.converter;

import org.apache.commons.collections4.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.common.es.model.product.EsProductModel;
import com.sankuai.shangou.seashop.product.core.service.hepler.ProductStatusHelper;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductEsBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductPageBo;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductSourceEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductStatusEnum;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2023/12/26 16:42
 */
public class EsProductConverter {


    /**
     * es商品转换为bo
     *
     * @param model es商品
     * @return 商品bo
     */
    public static ProductPageBo esModelToBo(EsProductModel model) {
        ProductPageBo bo = JsonUtil.copy(model, ProductPageBo.class);
        // 状态转换
        ProductStatusEnum productStatus = ProductStatusHelper.getProductStatus(model.getSaleStatus(), model.getAuditStatus());
        if (productStatus != null) {
            bo.setStatus(productStatus.getCode());
            bo.setStatusDesc(productStatus.getDesc());
        }
        bo.setSaleStatusCode(model.getSaleStatus());
        bo.setSaleStatusDesc(ProductEnum.SaleStatusEnum.getDescByCode(model.getSaleStatus()));
        bo.setAuditStatusCode(model.getAuditStatus());
        bo.setAuditStatusDesc(ProductEnum.AuditStatusEnum.getDescByCode(model.getAuditStatus()));
        bo.setImagePath(model.getMainImagePath());
        if (model.getAddedTime() != null) {
            bo.setAddedDate(DateUtil.date(model.getAddedTime()));
        }
        if (model.getOnsaleTime() != null) {
            bo.setCheckTime(DateUtil.date(model.getOnsaleTime()));
        }
        if (model.getCreateTimeStamp() != null) {
            bo.setCreateTime(DateUtil.date(model.getCreateTimeStamp()));
        }
        if (model.getUpdateTimeStamp() != null) {
            bo.setUpdateTime(DateUtil.date(model.getUpdateTimeStamp()));
        }
        if (model.getOnsaleTime() != null) {
            bo.setOnsaleTime(DateUtil.date(model.getOnsaleTime()));
        }
        if (StrUtil.isNotEmpty(model.getAllImagePath())) {
            bo.setImageList(StrUtil.split(model.getAllImagePath(), ","));
        }
        bo.setSourceDesc(ProductSourceEnum.getDescByCode(model.getSource()));
        return bo;
    }

    /**
     * bo转换为es商品
     *
     * @return es商品
     */
    public static EsProductModel boToEsModel(ProductEsBo bo) {
        EsProductModel model = new EsProductModel();
        model.setProductId(bo.getProductId());
        model.setProductCode(bo.getProductCode());
        model.setProductName(bo.getProductName());
        model.setSaleCount(bo.getSaleCounts());
        model.setShopId(bo.getShopId());
        model.setBrandId(bo.getBrandId());
        model.setCategoryId(bo.getCategoryId());
        model.setCategoryPath(bo.getCategoryPath());
        model.setMarketPrice(bo.getMarketPrice());
        model.setMinSalePrice(bo.getMinSalePrice());
        model.setMainImagePath(bo.getImagePath());
        model.setFreightTemplateId(bo.getFreightTemplateId());
        model.setWhetherBelowSafeStock(bo.getWhetherBelowSafeStock());
        model.setDisplaySequence(bo.getDisplaySequence());
        model.setShopDisplaySequence(bo.getShopDisplaySequence());
        model.setWhetherDelete(bo.getWhetherDelete());
        model.setSaleCounts(bo.getSaleCounts());
        model.setVirtualSaleCounts(bo.getVirtualSaleCounts());
        model.setMaxBuyCount(bo.getMaxBuyCount());
        model.setShopCategoryIds(bo.getShopCategoryIds());
        model.setSpec1Alias(bo.getSpec1Alias());
        model.setSpec2Alias(bo.getSpec2Alias());
        model.setSpec3Alias(bo.getSpec3Alias());
        model.setMeasureUnit(bo.getMeasureUnit());
        model.setWhetherOpenLadder(bo.getWhetherOpenLadder());
        model.setMultipleCount(bo.getMultipleCount());
        model.setHasSku(bo.getHasSku());
        model.setAuditReason(bo.getAuditReason());
        model.setSource(bo.getSource());
        model.setSkuAutoIds(bo.getSkuAutoIds());
        model.setSkuCodes(bo.getSkuCodes());
        model.setMaxSalePrice(bo.getMaxSalePrice());
        model.setSkuIds(bo.getSkuIds());
        model.setShortDescription(bo.getShortDescription());
        model.setSpecNameIds(bo.getSpecNameIds());

        if (bo.getCheckTime() != null) {
            model.setOnsaleTime(bo.getCheckTime().getTime());
        }
        if (bo.getAddedDate() != null) {
            model.setAddedTime(bo.getAddedDate().getTime());
        }
        if (bo.getSubmitAuditTime() != null) {
            model.setSubmitAuditTime(bo.getSubmitAuditTime().getTime());
        }
        if (bo.getSaleStatus() != null) {
            model.setSaleStatus(bo.getSaleStatus().getCode());
        }
        if (bo.getAuditStatus() != null) {
            model.setAuditStatus(bo.getAuditStatus().getCode());
        }
        if (CollectionUtils.isNotEmpty(bo.getImgList())) {
            model.setAllImagePath(String.join(",", bo.getImgList()));
        }
        if (bo.getVirtualSaleCounts() != null && bo.getSaleCounts() != null) {
            model.setTotalSaleCounts(bo.getVirtualSaleCounts() + bo.getSaleCounts());
        }
        if (bo.getCreateTime() != null) {
            model.setCreateTimeStamp(bo.getCreateTime().getTime());
        }
        if (bo.getUpdateTime() != null) {
            model.setUpdateTimeStamp(bo.getUpdateTime().getTime());
        }
        return model;
    }

}
