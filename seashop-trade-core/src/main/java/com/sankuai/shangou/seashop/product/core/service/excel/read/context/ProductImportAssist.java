package com.sankuai.shangou.seashop.product.core.service.excel.read.context;

import java.util.Map;

import org.apache.commons.collections.MapUtils;

import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.eimport.context.ImportContextHolder;

/**
 * <AUTHOR>
 * @date 2023/12/05 17:54
 */
public class ProductImportAssist {

    public static Long getShopIdOrThrow() {
        ProductImportContext productImportContext = ImportContextHolder.get();
        AssertUtil.throwIfTrue(productImportContext == null || productImportContext.getShopId() == null, "店铺ID不能为空");
        return productImportContext.getShopId();
    }

    public static Map<String, String> getRemoteImgMapping() {
        ProductImportContext productImportContext = ImportContextHolder.get();
        if (productImportContext == null || productImportContext.getRemoteMapping() == null) {
            return MapUtils.EMPTY_MAP;
        }
        return productImportContext.getRemoteMapping();
    }

    public static Integer getSaleStatus() {
        ProductImportContext productImportContext = ImportContextHolder.get();

        return productImportContext == null ? null : productImportContext.getSaleStatus();
    }

    public static Long getOperationUserId() {
        ProductImportContext productImportContext = ImportContextHolder.get();
        return productImportContext == null ? null : productImportContext.getOperationUserId();
    }

    public static Long getOperationShopId() {
        ProductImportContext productImportContext = ImportContextHolder.get();
        return productImportContext == null ? null : productImportContext.getOperationShopId();
    }

}
