package com.sankuai.shangou.seashop.product.core.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.eimport.ImportResult;
import com.sankuai.shangou.seashop.base.eimport.context.ImportContextHolder;
import com.sankuai.shangou.seashop.base.eimport.input.ImportWay;
import com.sankuai.shangou.seashop.base.s3plus.S3plusStorageService;
import com.sankuai.shangou.seashop.base.utils.CompressUtil;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.core.service.ProductImportService;
import com.sankuai.shangou.seashop.product.core.service.excel.read.BizTypeEnum;
import com.sankuai.shangou.seashop.product.core.service.excel.read.context.ProductImportContext;
import com.sankuai.shangou.seashop.product.core.service.excel.read.dto.PlatformProductImportDto;
import com.sankuai.shangou.seashop.product.core.service.excel.read.dto.PriceImportDto;
import com.sankuai.shangou.seashop.product.core.service.excel.read.dto.ProductImportDto;
import com.sankuai.shangou.seashop.product.core.service.excel.read.dto.ProductOffSaleDto;
import com.sankuai.shangou.seashop.product.core.service.excel.read.dto.ProductUpdateImportDto;
import com.sankuai.shangou.seashop.product.core.service.excel.read.dto.ProductViolationOffSaleDto;
import com.sankuai.shangou.seashop.product.core.service.excel.read.dto.StockImportDto;
import com.sankuai.shangou.seashop.product.core.service.excel.read.listener.PlatformProductImportListener;
import com.sankuai.shangou.seashop.product.core.service.excel.read.listener.PriceImportListener;
import com.sankuai.shangou.seashop.product.core.service.excel.read.listener.ProductImportListener;
import com.sankuai.shangou.seashop.product.core.service.excel.read.listener.ProductOffSaleListener;
import com.sankuai.shangou.seashop.product.core.service.excel.read.listener.ProductUpdateImportListener;
import com.sankuai.shangou.seashop.product.core.service.excel.read.listener.ProductViolationOffSaleListener;
import com.sankuai.shangou.seashop.product.core.service.excel.read.listener.StockImportListener;
import com.sankuai.shangou.seashop.product.core.service.hepler.S3Helper;
import com.sankuai.shangou.seashop.product.core.service.hepler.VenusHelper;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductImportBo;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/05 18:05
 */
@Service
@Slf4j
@SuppressWarnings("all")
public class ProductImportServiceImpl implements ProductImportService {

    @Resource
    private ImportWay importWay;
    @Resource
    private S3plusStorageService s3plusStorageService;
    @Resource
    private VenusHelper venusHelper;
    @Resource
    private S3Helper s3Helper;

    @Override
    public ImportResult importStock(ProductImportBo importBo) {
        importBo.setFilePath(s3Helper.getFullPath(importBo.getFilePath()));
        ImportContextHolder.set(JsonUtil.copy(importBo, ProductImportContext.class));
        return importWay.importData(BizTypeEnum.STOCK_IMPORT, importBo.getFilePath(), StockImportDto.class, new StockImportListener(), CommonConstant.EXCEL_IMPORT_MAX_SIZE);
    }

    @Override
    public ImportResult importOffSale(ProductImportBo importBo) {
        importBo.setFilePath(s3Helper.getFullPath(importBo.getFilePath()));
        ImportContextHolder.set(JsonUtil.copy(importBo, ProductImportContext.class));
        return importWay.importData(BizTypeEnum.OFF_SHELF_IMPORT, importBo.getFilePath(), ProductOffSaleDto.class, new ProductOffSaleListener(), CommonConstant.EXCEL_IMPORT_MAX_SIZE);
    }

    @Override
    public ImportResult importViolationOffSale(ProductImportBo importBo) {
        importBo.setFilePath(s3Helper.getFullPath(importBo.getFilePath()));
        ImportContextHolder.set(JsonUtil.copy(importBo, ProductImportContext.class));
        return importWay.importData(BizTypeEnum.VIOLATION_OFF_SALE_IMPORT, importBo.getFilePath(), ProductViolationOffSaleDto.class, new ProductViolationOffSaleListener(), CommonConstant.EXCEL_IMPORT_MAX_SIZE);
    }

    @Override
    public ImportResult importProduct(ProductImportBo importBo) {
        importBo.setFilePath(s3Helper.getFullPath(importBo.getFilePath()));
        String filePath = importBo.getFilePath();
        if (isExcelPath(filePath)) {
            ProductImportContext context = JsonUtil.copy(importBo, ProductImportContext.class);
            ImportContextHolder.set(context);
            return importWay.importData(BizTypeEnum.PRODUCT_IMPORT, filePath, ProductImportDto.class, new ProductImportListener());
        }

        FileInputStream fileInputStream = null;
        String workSpace = CommonConstant.PRODUCT_WORK_SPACE + File.separator + getImportUnzipRandomDir();
        try (
                InputStream inputStream = s3plusStorageService.readExcelFromOnlineUrl(filePath);
        ) {
            File file = new File(workSpace + File.separator + RandomUtils.nextLong() + CommonConstant.ZIP_SUFFIX);
            FileUtils.copyInputStreamToFile(inputStream, file);
            checkFileSize(file, CommonConstant.ZIP_IMPORT_MAX_SIZE, "ZIP压缩包");

            String destPath = workSpace + File.separator + RandomUtils.nextLong();
            File unzipFile = CompressUtil.decompress(file.getAbsolutePath(), destPath);
            // 提取unZipFile 文件下名称为的文件
            File excelFile = FileUtils.getFile(unzipFile, CommonConstant.PRODUCT_EXCEL_NAME);
            AssertUtil.throwIfTrue(excelFile == null || !excelFile.exists(), "未找到导入excel文件");
            checkFileSize(excelFile, CommonConstant.EXCEL_IMPORT_MAX_SIZE, "EXCEL文件");

            File imgFolder = FileUtils.getFile(unzipFile, CommonConstant.PRODUCT_IMAGE_FOLDER);
            // 提取imgFolder 下的所有图片 目前只提取 .jpg, .jpeg, .png 后缀的图片
            Map<String, String> remoteImgMapping = getRemoteImageMapping(imgFolder);

            // 将shopId 和图片映射关系放入上下文
            ProductImportContext context = JsonUtil.copy(importBo, ProductImportContext.class);
            context.setRemoteMapping(remoteImgMapping);
            ImportContextHolder.set(context);
            // 读取excel
            fileInputStream = new FileInputStream(excelFile);
            return importWay.importData(BizTypeEnum.PRODUCT_IMPORT, fileInputStream, ProductImportDto.class, new ProductImportListener());
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
            log.error("【导入商品】失败, filePath:{}", filePath, e);
            throw new BusinessException("导入失败");
        }
        finally {
            IoUtil.close(fileInputStream);
            try {
                FileUtils.deleteDirectory(new File(workSpace));
            }
            catch (Exception e) {
                log.error("【导入商品】删除临时文件失败, workSpace:{}", workSpace, e);
            }
        }
    }

    @Override
    public ImportResult importPrice(ProductImportBo importBo) {
        importBo.setFilePath(s3Helper.getFullPath(importBo.getFilePath()));
        ImportContextHolder.set(JsonUtil.copy(importBo, ProductImportContext.class));
        return importWay.importData(BizTypeEnum.PRICE_IMPORT, importBo.getFilePath(), PriceImportDto.class, new PriceImportListener(), CommonConstant.EXCEL_IMPORT_MAX_SIZE);
    }

    @Override
    public ImportResult importProductUpdate(ProductImportBo importBo) {
        importBo.setFilePath(s3Helper.getFullPath(importBo.getFilePath()));
        String filePath = importBo.getFilePath();
        if (isExcelPath(filePath)) {
            ProductImportContext context = JsonUtil.copy(importBo, ProductImportContext.class);
            ImportContextHolder.set(context);
            return importWay.importData(BizTypeEnum.PRODUCT_UPDATE_IMPORT, filePath, ProductUpdateImportDto.class, new ProductUpdateImportListener());
        }

        FileInputStream fileInputStream = null;
        String workSpace = CommonConstant.PRODUCT_UPDATE_WORK_SPACE + File.separator + getImportUnzipRandomDir();
        try (
                InputStream inputStream = s3plusStorageService.readExcelFromOnlineUrl(filePath);
        ) {
            File file = new File(workSpace + File.separator + RandomUtils.nextLong() + CommonConstant.ZIP_SUFFIX);
            FileUtils.copyInputStreamToFile(inputStream, file);
            checkFileSize(file, CommonConstant.ZIP_IMPORT_MAX_SIZE, "ZIP压缩包");

            String destPath = workSpace + File.separator + RandomUtils.nextLong();
            File unzipFile = CompressUtil.decompress(file.getAbsolutePath(), destPath);
            // 提取unZipFile 文件下名称为的文件
            File excelFile = FileUtils.getFile(unzipFile, CommonConstant.PRODUCT_UPDATE_EXCEL_NAME);
            AssertUtil.throwIfTrue(excelFile == null || !excelFile.exists(), "未找到导入excel文件");
            checkFileSize(excelFile, CommonConstant.EXCEL_IMPORT_MAX_SIZE, "EXCEL文件");

            File imgFolder = FileUtils.getFile(unzipFile, CommonConstant.PRODUCT_UPDATE_IMAGE_FOLDER);
            // 提取imgFolder 下的所有图片 目前只提取 .jpg, .jpeg, .png 后缀的图片
            Map<String, String> remoteImgMapping = getRemoteImageMapping(imgFolder);

            // 将shopId 和图片映射关系放入上下文
            ProductImportContext context = JsonUtil.copy(importBo, ProductImportContext.class);
            context.setRemoteMapping(remoteImgMapping);
            ImportContextHolder.set(context);
            // 读取excel
            fileInputStream = new FileInputStream(excelFile);
            return importWay.importData(BizTypeEnum.PRODUCT_UPDATE_IMPORT, fileInputStream, ProductUpdateImportDto.class, new ProductUpdateImportListener());
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
            log.error("【导入商品】失败, filePath:{}", filePath, e);
            throw new BusinessException("导入失败");
        }
        finally {
            IoUtil.close(fileInputStream);
            try {
                FileUtils.deleteDirectory(new File(workSpace));
            }
            catch (Exception e) {
                log.error("【导入商品】删除临时文件失败, workSpace:{}", workSpace, e);
            }
        }
    }

    @Override
    public ImportResult platformImportProduct(ProductImportBo importBo) {
        importBo.setFilePath(s3Helper.getFullPath(importBo.getFilePath()));
        String filePath = importBo.getFilePath();
        if (isExcelPath(filePath)) {
            ProductImportContext context = JsonUtil.copy(importBo, ProductImportContext.class);
            ImportContextHolder.set(context);
            return importWay.importData(BizTypeEnum.PLATFORM_PRODUCT_IMPORT, filePath, PlatformProductImportDto.class, new PlatformProductImportListener());
        }

        FileInputStream fileInputStream = null;
        String workSpace = CommonConstant.PRODUCT_WORK_SPACE + File.separator + getImportUnzipRandomDir();
        try (
                InputStream inputStream = s3plusStorageService.readExcelFromOnlineUrl(filePath);
        ) {
            File file = new File(workSpace + File.separator + RandomUtils.nextLong() + CommonConstant.ZIP_SUFFIX);
            FileUtils.copyInputStreamToFile(inputStream, file);
            checkFileSize(file, CommonConstant.ZIP_IMPORT_MAX_SIZE, "ZIP压缩包");

            String destPath = workSpace + File.separator + RandomUtils.nextLong();
            File unzipFile = CompressUtil.decompress(file.getAbsolutePath(), destPath);
            // 提取unZipFile 文件下名称为的文件
            File excelFile = FileUtils.getFile(unzipFile, CommonConstant.PRODUCT_EXCEL_NAME);
            AssertUtil.throwIfTrue(excelFile == null || !excelFile.exists(), "未找到导入excel文件");
            checkFileSize(excelFile, CommonConstant.EXCEL_IMPORT_MAX_SIZE, "EXCEL文件");

            File imgFolder = FileUtils.getFile(unzipFile, CommonConstant.PRODUCT_IMAGE_FOLDER);
            // 提取imgFolder 下的所有图片 目前只提取 .jpg, .jpeg, .png 后缀的图片
            Map<String, String> remoteImgMapping = getRemoteImageMapping(imgFolder);

            // 将shopId 和图片映射关系放入上下文
            ProductImportContext context = JsonUtil.copy(importBo, ProductImportContext.class);
            context.setRemoteMapping(remoteImgMapping);
            ImportContextHolder.set(context);
            // 读取excel
            fileInputStream = new FileInputStream(excelFile);
            return importWay.importData(BizTypeEnum.PLATFORM_PRODUCT_IMPORT, fileInputStream, PlatformProductImportDto.class, new PlatformProductImportListener());
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
            log.error("【导入商品】失败, filePath:{}", filePath, e);
            throw new BusinessException("导入失败");
        }
        finally {
            IoUtil.close(fileInputStream);
            try {
                FileUtils.deleteDirectory(new File(workSpace));
            }
            catch (Exception e) {
                log.error("【导入商品】删除临时文件失败, workSpace:{}", workSpace, e);
            }
        }
    }

    /**
     * 获取远程图片映射
     *
     * @param imgFolder 图片文件夹
     * @return 映射
     */
    private Map<String, String> getRemoteImageMapping(File imgFolder) {
        if (imgFolder == null || !imgFolder.exists() || !imgFolder.isDirectory()) {
            return Collections.EMPTY_MAP;
        }

        // 提取imgFolder 下的所有图片 目前只提取 .jpg, .jpeg, .png 后缀的图片
        List<File> imgFiles = FileUtils.listFiles(imgFolder, CommonConstant.IMAGE_SUFFIX, false).stream().collect(Collectors.toList());
        String images = imgFiles.stream().map(File::getName).collect(Collectors.joining(StrUtil.COMMA));
        log.info("导入文件名称: {}, 图片数量: {}", images, imgFiles.size());
        return venusHelper.uploadImages(imgFiles);
    }

    /**
     * 获取导入解压随机目录
     *
     * @return 随机目录
     */
    private String getImportUnzipRandomDir() {
        return System.currentTimeMillis() + StrUtil.UNDERLINE + IdUtil.fastSimpleUUID();
    }

    /**
     * 检查文件大小
     *
     * @param file     文件
     * @param maxSize  最大大小(单位byte)
     * @param fileName 文件名
     */
    private void checkFileSize(File file, long maxSize, String fileName) {
        if (file.length() > maxSize) {
            throw new BusinessException(String.format("【%s】不能超过%s", fileName, FileUtils.byteCountToDisplaySize(maxSize)));
        }
    }

    /**
     * 判断是否是excel 文件
     *
     * @param filePath
     * @return
     */
    private boolean isExcelPath(String filePath) {
        return filePath.endsWith(".xlsx") || filePath.endsWith(".xls");
    }
}
