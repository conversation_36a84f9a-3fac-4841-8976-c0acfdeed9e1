package com.sankuai.shangou.seashop.trade.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.boot.utils.PageUtil;
import com.sankuai.shangou.seashop.base.lock.DistributeLock;
import com.sankuai.shangou.seashop.base.utils.SquirrelUtil;
import com.sankuai.shangou.seashop.order.thrift.core.response.ProductCommentSummaryResp;
import com.sankuai.shangou.seashop.product.core.remote.ProductRemoteService;
import com.sankuai.shangou.seashop.product.core.service.model.CategoryBo;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import com.sankuai.shangou.seashop.promotion.core.remote.PromotionRemoteService;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.FlashSaleStatusEnum;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.EffectiveFlashSaleQueryResp;
import com.sankuai.shangou.seashop.trade.common.config.EsIndexProps;
import com.sankuai.shangou.seashop.trade.common.constant.CacheConst;
import com.sankuai.shangou.seashop.trade.common.constant.CommonConst;
import com.sankuai.shangou.seashop.trade.common.constant.EsConst;
import com.sankuai.shangou.seashop.trade.common.constant.LockConst;
import com.sankuai.shangou.seashop.trade.common.enums.AuditStatusEnum;
import com.sankuai.shangou.seashop.trade.common.enums.ProductSortFieldMappingEnum;
import com.sankuai.shangou.seashop.trade.common.es.EagleService;
import com.sankuai.shangou.seashop.trade.common.es.model.EagleQueryResult;
import com.sankuai.shangou.seashop.trade.common.remote.*;
import com.sankuai.shangou.seashop.trade.common.remote.model.product.*;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteDiscountBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteDiscountRuleBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteReductionBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteShopUserPromotionBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.shop.CalculateFreightBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.shop.CalculateFreightProductBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.shop.CalculateFreightShopBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.shop.ShopFreightBo;
import com.sankuai.shangou.seashop.trade.common.util.ThreadPoolUtil;
import com.sankuai.shangou.seashop.trade.core.service.TradeProductService;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.ShoppingCartAssist;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.ProductHandlerType;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.ProductBaseContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.TradeProductHandlerProcessor;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductBaseInfoBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.promotion.PromotionAssist;
import com.sankuai.shangou.seashop.trade.core.service.model.AddonProductContext;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.product.*;
import com.sankuai.shangou.seashop.trade.core.service.model.promotion.ShopAddonActivityParamBo;
import com.sankuai.shangou.seashop.trade.dao.core.domain.ShoppingCart;
import com.sankuai.shangou.seashop.trade.dao.core.repository.ShoppingCartRepository;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.TradeProductDto;
import com.sankuai.shangou.seashop.trade.thrift.core.enums.AddonProductPromotionTabEnum;
import com.sankuai.shangou.seashop.trade.thrift.core.request.CalculateFreightReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.ProductIdsReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.MallShopProductResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.product.CalculateFreightResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.product.QueryProductByIdListResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopResp;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MatchQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.MultiBucketsAggregation;
import org.elasticsearch.search.aggregations.bucket.nested.Nested;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 交易商品相关接口实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TradeProductServiceImpl implements TradeProductService {

    @Resource
    private ProductRemoteService productRemoteService;
    @Resource
    private EagleService eagleService;
    @Resource
    private PromotionRemoteService promotionRemoteService;
    @Resource
    private SquirrelUtil squirrelUtil;
    @Resource
    private UserFavoriteRemoteService userFavoriteRemoteService;
    @Resource
    private PromotionAssist promotionAssist;
    @Resource
    private TradeProductHandlerProcessor tradeProductHandlerProcessor;
    @Resource
    private ShopRemoteService shopRemoteService;
    @Resource
    private FreightRemoteService freightRemoteService;
    @Resource
    private FreightTemplateService freightTemplateService;
    @Resource
    private Order2TradeRemoteService order2TradeRemoteService;
    @Resource
    private ShoppingCartAssist shoppingCartAssist;
    @Resource
    private ShoppingCartRepository shoppingCartRepository;

    @Resource
    private EsIndexProps esIndexProps;

    /**
     * json copy 需要忽略的字段
     */
    private static final String[] IGNORE_FIELDS = new String[]{"addedDate", "submitAuditTime", "saleStatus", "auditStatus", "riskAuditStatus"};


    /**
     * 加锁的目的是保证并发或者时序错误的情况下，都能从数据库获取到此时最新的商品信息
     *
     * @param productId void
     * <AUTHOR>
     */
    @DistributeLock(keyPattern = LockConst.LOCK_ES_PRODUCT_UPDATE_PATTERN, scenes =
            LockConst.SCENE_ES_PRODUCT_UPDATE,
            waitLock = true,
            keyValues = {"{0}"})
    @Override
    public void build(Long productId) {
        // 反查获取最新的商品信息，反查的目的是获取此时最新的商品数据
        ProductDetailBo productDto = productRemoteService.queryProductDetail(productId);
        if (productDto != null && !productDto.getWhetherDelete()) {
            // 构建ES存储对象
            Map<String/*docId*/, Map<String/*字段名称*/, Object/*字段值*/>> paramMap = new HashMap<>(2);
            // 构建需要保存的业务数据
            Map<String/*字段名称*/, Object/*字段值*/> doc = buildIndexData(productDto);
            paramMap.put(String.valueOf(productDto.getProductId()), doc);
            // 调用ES部分更新
            eagleService.partUpdate(EsConst.INDEX_TRADE_PRODUCT, paramMap);
        } else {
            eagleService.batchDelete(EsConst.INDEX_TRADE_PRODUCT, Collections.singletonList(String.valueOf(productId)));
        }
    }

    /**
     * 构建索引业务数据
     *
     * <AUTHOR>
     * java.util.Map<java.lang.String,java.lang.Object>
     */
    private Map<String/*字段名称*/, Object/*字段值*/> buildIndexData(ProductDetailBo productDto) {
        // 基础的一些直接拷贝
        EsTradeProductBo tradeProductBo = JsonUtil.copy(productDto, EsTradeProductBo.class, IGNORE_FIELDS);
        // 字段不同的进行设置
        tradeProductBo.setMainImagePath(productDto.getImagePath());
        tradeProductBo.setSaleCount(productDto.getSaleCounts());
        if (productDto.getCheckTime() != null) {
            tradeProductBo.setOnsaleTime(productDto.getCheckTime().getTime());
        }
        if (productDto.getAddedDate() != null) {
            tradeProductBo.setAddedTime(productDto.getAddedDate().getTime());
        }
        if (productDto.getSubmitAuditTime() != null) {
            tradeProductBo.setSubmitAuditTime(productDto.getSubmitAuditTime().getTime());
        }
        if (productDto.getSaleStatus() != null) {
            tradeProductBo.setSaleStatus(productDto.getSaleStatus().getCode());
        }
        if (productDto.getAuditStatus() != null) {
            tradeProductBo.setAuditStatus(productDto.getAuditStatus().getCode());
        }
        if (CollectionUtils.isNotEmpty(productDto.getImgList())) {
            tradeProductBo.setAllImagePath(String.join(",", productDto.getImgList()));
        }
        if (productDto.getVirtualSaleCounts() != null && productDto.getSaleCounts() != null) {
            tradeProductBo.setTotalSaleCounts(productDto.getVirtualSaleCounts() + productDto.getSaleCounts());
        }
        if (productDto.getCreateTime() != null) {
            tradeProductBo.setCreateTimeStamp(productDto.getCreateTime().getTime());
        }
        if (productDto.getUpdateTime() != null) {
            tradeProductBo.setUpdateTimeStamp(productDto.getUpdateTime().getTime());
        }

        // 属性格式化设置
        List<RemoteProductAttributeBo> attributeList = productDto.getAttributeList();
        if (CollectionUtils.isNotEmpty(attributeList)) {
            List<ProductAttributeBo> productAttributeBos = new ArrayList<>(attributeList.size());
            for (RemoteProductAttributeBo attributeDto : attributeList) {
                ProductAttributeBo productAttributeBo = ProductAttributeBo.builder()
                        .attributeId(attributeDto.getAttributeId())
                        .attributeName(attributeDto.getAttributeName())
                        .build();
                List<String> attributeValues = attributeDto.getAttributeValueList().stream()
                        .map(ProductAttributeValueBo::getValueName)
                        .collect(Collectors.toList());
                productAttributeBo.setAttributeValues(attributeValues);
                productAttributeBos.add(productAttributeBo);
            }
            tradeProductBo.setProductAttribute(productAttributeBos);
        }
        Map<String/*字段名称*/, Object/*字段值*/> doc = JsonUtil.beanToMap(tradeProductBo);
        log.info("【商品索引构建】商品ID为: {}, 索引内容为: {}", productDto.getProductId(), JsonUtil.toJsonString(doc));
        return doc;
    }

    @Override
    public SearchTradeProductRespBo search(SearchProductBo searchBo) {
        // 构建搜索请求
        searchBo.setSaleStatus(ProductEnum.SaleStatusEnum.ON_SALE.getCode());
        searchBo.setAuditStatus(ProductEnum.AuditStatusEnum.ON_SALE.getCode());
        SearchRequest searchRequest = buildSearchRequest(searchBo);
        log.info("【商品搜索】搜索条件为: {}", JsonUtil.toJsonString(searchRequest));
        if (searchRequest == null) {
            BasePageResp<SearchedTradeProductBo> emptyPage = PageResultHelper.defaultEmpty(searchBo);
            return SearchTradeProductRespBo.defaultEmpty(emptyPage);
        }
        // 调用ES进行查询和聚合
        EagleQueryResult searchResult = eagleService.queryByCondition(searchRequest);
        log.info("【商品搜索】搜索条件为: {}, 搜索结果为: {}", JsonUtil.toJsonString(searchBo), searchResult);
        // 解析搜索结果
        EsTradeProductResultBo resultBo = resolveSearchResult(searchResult, searchBo);
        log.debug("【商品搜索】搜索条件为: {}, 搜索结果为: {}", JsonUtil.toJsonString(searchBo), JsonUtil.toJsonString(resultBo));
        // 根据查询结果填充相关数据
        return fillTradeProduct(resultBo, searchBo);
    }

    @Override
    public BasePageResp<SearchedTradeProductBo> searchInShop(SearchProductInShopBo searchBo) {
        // 构建搜索请求
        searchBo.setSaleStatus(ProductEnum.SaleStatusEnum.ON_SALE.getCode());
        searchBo.setAuditStatus(ProductEnum.AuditStatusEnum.ON_SALE.getCode());
        // 构建搜索请求
        SearchRequest searchRequest = buildSearchInShopRequest(searchBo);
        log.info("【店铺内商品搜索】搜索条件为: {}", JsonUtil.toJsonString(searchRequest));
        // 如果返回null，是代表数据不匹配，比如 传入店铺分类ID，但查不到店铺分类
        if (searchRequest == null) {
            return PageResultHelper.defaultEmpty(searchBo);
        }
        // 调用ES进行查询和聚合
        EagleQueryResult searchResult = eagleService.queryByCondition(searchRequest);
        log.info("【店铺内商品搜索】搜索条件为: {}, 搜索结果为: {}", JsonUtil.toJsonString(searchBo), JsonUtil.toJsonString(searchResult));
        // 解析搜索结果
        BasePageResp<EsTradeProductBo> productPage = resolveSearchInShopResult(searchResult, searchBo);
        log.debug("【店铺内商品搜索】搜索条件为: {}, 搜索结果为: {}", JsonUtil.toJsonString(searchBo), JsonUtil.toJsonString(productPage));
        // 填充商品信息，补充额外的数据
        if (CollUtil.isNotEmpty(productPage.getData())) {
            Long userId = searchBo.getUser() == null ? null : searchBo.getUser().getUserId();
            return convertAndAppend(productPage, userId, null);
        }
        return PageResultHelper.defaultEmpty(searchBo);
    }

    @Override
    public AddonProductResultBo searchAddonProduct(QueryAddonProductParamBo queryParam) {
        queryParam.setSaleStatus(ProductEnum.SaleStatusEnum.ON_SALE.getCode());
        queryParam.setAuditStatus(ProductEnum.AuditStatusEnum.ON_SALE.getCode());
        // 构建后续会用到的临时上下文
        AddonProductContext tempContext = buildAddonTempContext(queryParam);
        // 补充其他业务参数
        extAddonDiscountQueryParam(queryParam, tempContext);
        // 首先对商品进行搜索，根据凑单的标签，搜索出满足条件的商品
        BasePageResp<SearchedTradeProductBo> productPage = searchForAddon(queryParam);
        // 构建凑单营销汇总信息
        AddonSummaryBo summaryBo = promotionAssist.buildAddonSummary(queryParam.getType(), tempContext);
        // 设置返回结果
        AddonProductResultBo resultBo = new AddonProductResultBo();
        resultBo.setProductPage(productPage);
        resultBo.setAddonSummary(summaryBo);

        return resultBo;
    }

    @Override
    public List<AddonActivityBo> getShopAddonActivity(ShopAddonActivityParamBo param) {
        List<AddonActivityBo> activityList = new ArrayList<>();
        // 获取店铺当前有效的满减活动
        List<RemoteShopUserPromotionBo> promotionList = promotionRemoteService.queryValidShopUserPromotion(Collections.singletonList(param.getShopId()), param.getUserId());
        if (CollUtil.isEmpty(promotionList)) {
            return activityList;
        }
        // 单店铺查询
        RemoteShopUserPromotionBo shopPromotion = promotionList.get(0);
        // 默认展示满减，所以放最前面
        RemoteReductionBo shopReduction = shopPromotion.getShopReduction();
        if (shopReduction != null) {
            AddonActivityBo reduction = new AddonActivityBo();
            reduction.setType(AddonProductPromotionTabEnum.REDUCTION.getCode());
            reduction.setTypeDesc(AddonProductPromotionTabEnum.REDUCTION.getDesc());
            reduction.setActivityId(shopReduction.getActiveId());
            reduction.setActivityName(shopReduction.getActiveName());
            reduction.setEndTime(shopReduction.getEndTime());
            activityList.add(reduction);
        }
        // 获取店铺当前有效的折扣列表
        List<RemoteDiscountBo> shopDiscountList = shopPromotion.getShopDiscountList();
        if (CollectionUtils.isNotEmpty(shopDiscountList)) {
            List<AddonActivityBo> discountList = shopDiscountList.stream()
                    .map(sd -> {
                        AddonActivityBo discount = new AddonActivityBo();
                        discount.setType(AddonProductPromotionTabEnum.DISCOUNT.getCode());
                        discount.setTypeDesc(AddonProductPromotionTabEnum.DISCOUNT.getDesc());
                        discount.setActivityId(sd.getDiscountActId());
                        discount.setActivityName(sd.getDiscountActName());
                        discount.setEndTime(sd.getEndTime());
                        return discount;
                    })
                    .collect(Collectors.toList());
            activityList.addAll(discountList);
        }
        return activityList;
    }

    @Override
    public ProductBaseInfoBo queryProductBaseInfo(QueryProductDetailBo queryBo) {
        ProductBaseContext context = new ProductBaseContext();
        context.setProductId(queryBo.getProductId());
        context.setUserId(queryBo.getUserId());
        context.setCollocationId(queryBo.getCollocationId());
        tradeProductHandlerProcessor.handle(ProductHandlerType.PRODUCT_BASE_DATA, context);
        return context.getProduct();
    }

    @Override
    public EsCommentSummaryBo queryCommentSummary(Long productId) {
        EsTradeProductBo product = getProductById(productId);
        if (product == null || product.getCommentSummary() == null) {
            return EsCommentSummaryBo.ofEmpty();
        }
        return product.getCommentSummary();
    }

    @Override
    public CalculateFreightResp calculateFreight(CalculateFreightReq calculateFreightReq) {
        List<RemoteProductSkuBo> skuList = productRemoteService.queryProductSku(Arrays.asList(calculateFreightReq.getSkuId()));
        AssertUtil.throwIfTrue(CollectionUtils.isEmpty(skuList), "商品sku不存在");

        // 拆分地址
        List<Long> regionIds = Arrays.asList(calculateFreightReq.getRegionPath().split(StrUtil.COMMA))
                .stream().map(Long::parseLong).collect(Collectors.toList());

        // 构建运费计算接口需要的参数
        RemoteProductSkuBo sku = skuList.get(0);
        BigDecimal totalAmount = calculateFreightReq.getSalePrice().multiply(new BigDecimal(calculateFreightReq.getQuantity()));
        CalculateFreightBo calculateFreightBo = new CalculateFreightBo();
        calculateFreightBo.setRegionId(regionIds.get(regionIds.size() - 1).intValue());
        calculateFreightBo.setRegionPath(calculateFreightReq.getRegionPath());

        CalculateFreightProductBo productBo = JsonUtil.copy(sku, CalculateFreightProductBo.class);
        productBo.setTemplateId(sku.getFreightTemplateId());
        productBo.setProductAmount(totalAmount);
        productBo.setBuyCount(calculateFreightReq.getQuantity());

        CalculateFreightShopBo shopBo = new CalculateFreightShopBo();
        shopBo.setShopId(sku.getShopId());
        shopBo.setTotalAmount(totalAmount);
        shopBo.setProductList(Arrays.asList(productBo));
        calculateFreightBo.setShopList(Arrays.asList(shopBo));

        List<ShopFreightBo> freightList = freightRemoteService.calculateFreight(calculateFreightBo);
        BigDecimal freight = CollectionUtils.isEmpty(freightList) ? BigDecimal.ZERO : freightList.get(0).getFreight();
        // 计算是否在限购区域
        Boolean inRestrictedRegion = freightTemplateService.isRestrictedRegion(sku.getFreightTemplateId(), regionIds);
        return CalculateFreightResp.builder().freight(freight).inRestrictedRegion(inRestrictedRegion).build();
    }

    @Override
    public List<QueryProductByIdListResp> queryProductByIdList(ProductIdsReq request) {
        List<QueryProductByIdListResp> result = new ArrayList<>();
        SearchResponse searchResult = eagleService.queryById(EsConst.INDEX_TRADE_PRODUCT, request.getProductIds());
        // 构建搜索请求
        SearchHits hits = searchResult.getHits();
        for (SearchHit next : hits) {
            EsTradeProductBo esTradeProductBo = JsonUtil.parseObject(next.getSourceAsString(), EsTradeProductBo.class);
            QueryProductByIdListResp resp = new QueryProductByIdListResp();
            resp.setProductId(esTradeProductBo.getProductId().toString());
            resp.setMarketPrice(esTradeProductBo.getMarketPrice());
            resp.setMinSalePrice(esTradeProductBo.getMinSalePrice());
            resp.setAuditStatus(esTradeProductBo.getAuditStatus());
            resp.setAuditStatusName(AuditStatusEnum.getDescByCode(esTradeProductBo.getAuditStatus()));
            resp.setMainImagePath(esTradeProductBo.getMainImagePath());
            resp.setProductName(esTradeProductBo.getProductName());
            resp.setSaleStatus(esTradeProductBo.getSaleStatus());
            resp.setSaleStatusName(ProductEnum.SaleStatusEnum.getDescByCode(esTradeProductBo.getSaleStatus()));
            resp.setTotalStock(esTradeProductBo.getTotalStock());
            resp.setSinglePrice(isSinglePrice(esTradeProductBo));
            EsCommentSummaryBo esCommentSummaryBo = esTradeProductBo.getCommentSummary();
            resp.setTotalCount(Objects.isNull(esCommentSummaryBo) ? 0 : esCommentSummaryBo.getTotalCount());
            result.add(resp);
        }
        return result;
    }

    private Boolean isSinglePrice(EsTradeProductBo esTradeProductBo) {
        // 超过一个规格
        if (CollUtil.isNotEmpty(esTradeProductBo.getSkuAutoIds()) && esTradeProductBo.getSkuAutoIds().size() > 1) {
            return Boolean.FALSE;
        }

        // 开启了阶梯价
        if (esTradeProductBo.getWhetherOpenLadder()) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    @Override
    public List<SearchedTradeProductBo> queryNewestProduct(Long shopId) {
        SearchProductBo queryBo = new SearchProductBo();
        queryBo.setShopId(shopId);
        queryBo.setPageNo(CommonConst.START_PAGE);
        queryBo.setPageSize(CommonConst.NEWEST_PRODUCT_NUM);

        // 根据上架时间降序
        FieldSortReq sortBo = new FieldSortReq();
        sortBo.setSort(EsConst.TradeProduct.SEARCH_FIELD_ON_SALE_TIME);
        sortBo.setIzAsc(Boolean.FALSE);
        queryBo.setSortList(Collections.singletonList(sortBo));
        SearchTradeProductRespBo search = search(queryBo);
        BasePageResp<SearchedTradeProductBo> pageResult = search.getProductPage();
        return PageResultHelper.getData(pageResult);
    }

    /*@Override
    public List<ProductPageBo> queryHotSaleProduct(ProductQueryBo queryBo) {
        BasePageParam pageParam = new BasePageParam();
        pageParam.setPageNum(CommonConstant.DEFAULT_PAGE_NO);
        pageParam.setPageSize(CommonConstant.HOT_SALE_PRODUCT_NUM);

        // 只返回上架的商品 根据销量降序排列
        queryBo.setSaleStatusCode(ProductEnum.SaleStatusEnum.ON_SALE.getCode());
        queryBo.setAuditStatusCode(ProductEnum.AuditStatusEnum.ON_SALE.getCode());
        FieldSortReq sortReq = new FieldSortReq();
        sortReq.setSort(EsConstant.Product.SEARCH_FIELD_TOTAL_SALE_COUNTS);
        sortReq.setIzAsc(Boolean.FALSE);
        queryBo.setSortList(Arrays.asList(sortReq));

        BasePageResp<ProductPageBo> search = esProductAssist.search(pageParam, queryBo, EsConstant.INDEX_TRADE_PRODUCT);
        // todo 查询限时购价格
        return search.getData();
    }*/

    @DistributeLock(keyPattern = LockConst.LOCK_ES_PRODUCT_UPDATE_PATTERN, scenes =
            LockConst.SCENE_ES_PRODUCT_UPDATE,
            waitLock = true,
            keyValues = {"{0}"})
    @Override
    public void updateProductCollectionCount(Long productId) {
        Map<String, Map<String, Object>> paramMap = buildProductCollectionCountData(productId);
        // 调用ES部分更新
        eagleService.partUpdate(EsConst.INDEX_TRADE_PRODUCT, paramMap);
        log.info("【商品收藏】商品ID为: {}, 索引内容为: {}", productId, JsonUtil.toJsonString(paramMap));
    }

    @DistributeLock(keyPattern = LockConst.LOCK_ES_PRODUCT_UPDATE_PATTERN, scenes =
            LockConst.SCENE_ES_PRODUCT_UPDATE,
            waitLock = true,
            keyValues = {"{0}.productId"})
    @Override
    public void updateProductVisitCount(ProductVisitBo visitBo) {
        String messageId = visitBo.getMessageId();
        Long productId = visitBo.getProductId();
        int addVisitCounts = visitBo.getAddVisitCounts();

        String mark = CacheConst.PRODUCT_VISIT_COUNT_MESSAGE_ID + messageId;
        try {
            // 将 messageId放入redis,防止重复消费
            Boolean markFlag = squirrelUtil.setnx(mark, 1, CacheConst.PRODUCT_VISIT_COUNT_MESSAGE_ID_EXPIRE_TIME);
            if (!markFlag) {
                return;
            }

            EsTradeProductBo productBo = getProductById(productId);
            if (productBo == null) {
                squirrelUtil.deleteKey(mark);
                return;
            }

            Integer visitCounts = ObjectUtil.defaultIfNull(productBo.getVisitCounts(), 0);
            visitCounts = visitCounts + addVisitCounts;
            // 调用ES部分更新
            Map<String, Map<String, Object>> paramMap = buildProductVisitData(productId, visitCounts);
            eagleService.partUpdate(EsConst.INDEX_TRADE_PRODUCT, paramMap);
        } catch (Exception e) {
            squirrelUtil.deleteKey(mark);

            log.error("更新商品访问量失败,productId:{}", productId, e);
            throw e;
        }
    }

    @DistributeLock(keyPattern = LockConst.LOCK_ES_PRODUCT_UPDATE_PATTERN, scenes =
            LockConst.SCENE_ES_PRODUCT_UPDATE,
            waitLock = true,
            keyValues = {"{0}"})
    @Override
    public void updateProductCommentSummary(Long productId) {
        ProductCommentSummaryResp productSummary = order2TradeRemoteService.getProductCommentSummary(productId);

        Map<String, Map<String, Object>> paramMap = buildProductCommentSummaryData(productId, productSummary);
        // 调用ES部分更新
        eagleService.partUpdate(EsConst.INDEX_TRADE_PRODUCT, paramMap);
        log.info("【商品评价】商品ID为: {}, 索引内容为: {}", productId, JsonUtil.toJsonString(paramMap));
    }

    /**
     * todo 加入缓存
     */
    @Override
    public List<SearchedTradeProductBo> queryHotSaleProduct(SearchProductBo queryBo) {
        queryBo.setPageNo(CommonConst.START_PAGE);
        queryBo.setPageSize(CommonConst.HOT_SALE_PRODUCT_NUM);

        // 根据总销量排序
        FieldSortReq sortBo = new FieldSortReq();
        sortBo.setSort(EsConst.TradeProduct.SEARCH_FIELD_TOTAL_SALE_COUNTS);
        sortBo.setIzAsc(Boolean.FALSE);
        queryBo.setSortList(Collections.singletonList(sortBo));
        SearchTradeProductRespBo search = search(queryBo);
        BasePageResp<SearchedTradeProductBo> pageResult = search.getProductPage();
        return PageResultHelper.getData(pageResult);
    }

    @Override
    public MallShopProductResp queryMallShopProduct(SearchProductBo queryBo) {
        queryBo.setPageNo(CommonConst.START_PAGE);
        queryBo.setPageSize(CommonConst.HOT_SALE_PRODUCT_NUM);

        // 根据店铺展示顺序排序
        FieldSortReq sequenceSort = new FieldSortReq();
        sequenceSort.setSort(EsConst.TradeProduct.SEARCH_FIELD_SHOP_DISPLAY_SEQUENCE);
        sequenceSort.setIzAsc(Boolean.FALSE);
        FieldSortReq saleCountSort = new FieldSortReq();
        saleCountSort.setSort(EsConst.TradeProduct.SEARCH_FIELD_TOTAL_SALE_COUNTS);
        saleCountSort.setIzAsc(Boolean.FALSE);
        FieldSortReq onsaleTimeSort = new FieldSortReq();
        onsaleTimeSort.setSort(EsConst.TradeProduct.SEARCH_FIELD_ADDED_TIME);
        onsaleTimeSort.setIzAsc(Boolean.FALSE);

        queryBo.setSortList(Arrays.asList(sequenceSort, saleCountSort, onsaleTimeSort));
        SearchTradeProductRespBo search = search(queryBo);
        BasePageResp<SearchedTradeProductBo> pageResult = search.getProductPage();

        MallShopProductResp resp = new MallShopProductResp();
        resp.setProductList(JsonUtil.copyList(PageResultHelper.getData(pageResult), TradeProductDto.class));
        resp.setProductCount(pageResult.getTotalCount());

        return resp;
    }

    /**
     * todo 加入缓存
     */
    @Override
    public List<SearchedTradeProductBo> queryHotAttentionProduct(SearchProductBo queryBo) {
        queryBo.setPageNo(CommonConst.START_PAGE);
        queryBo.setPageSize(CommonConst.HOT_ATTENTION_PRODUCT_NUM);

        // 根据浏览量降序
        FieldSortReq sortBo = new FieldSortReq();
        sortBo.setSort(EsConst.TradeProduct.SEARCH_FIELD_FAVORITE_COUNT);
        sortBo.setIzAsc(Boolean.FALSE);
        queryBo.setSortList(Collections.singletonList(sortBo));
        SearchTradeProductRespBo search = search(queryBo);
        BasePageResp<SearchedTradeProductBo> pageResult = search.getProductPage();
        return PageResultHelper.getData(pageResult);
    }

    /**
     * todo 加入缓存
     */
    @Override
    public List<SearchedTradeProductBo> queryGuessYouLike(Long productId) {
        EsTradeProductBo product = getProductById(productId);
        if (product == null) {
            return Collections.emptyList();
        }

        SearchProductBo searchBo = new SearchProductBo();
        searchBo.setPageNo(CommonConst.START_PAGE);
        searchBo.setPageSize(CommonConst.GUESS_YOU_LIKE_PRODUCT_NUM);

        // 查询关联的商品信息
        List<Long> recommendProductIds = productRemoteService.queryRecommendProductIds(productId);
        if (CollectionUtils.isEmpty(recommendProductIds)) {
            searchBo.setCategoryId(product.getCategoryId());
        } else {
            searchBo.setProductIds(recommendProductIds);
        }
        SearchTradeProductRespBo search = search(searchBo);
        BasePageResp<SearchedTradeProductBo> pageResult = search.getProductPage();
        return PageResultHelper.getData(pageResult);
    }


    //**********************************************************************************************


    /**
     * 商品列表除了基本的商品信息外，还需要填充其他信息，比如价格处理，专享价处理，库存处理等
     *
     * @param resultBo ES搜索结果
     * <AUTHOR>
     */
    private SearchTradeProductRespBo fillTradeProduct(EsTradeProductResultBo resultBo, SearchProductBo searchBo) {
        if (resultBo == null || resultBo.getProductPage() == null || CollUtil.isEmpty(resultBo.getProductPage().getData())) {
            BasePageResp<SearchedTradeProductBo> emptyPage = PageResultHelper.defaultEmpty(searchBo);
            return SearchTradeProductRespBo.defaultEmpty(emptyPage);
        }
        SearchTradeProductRespBo respBo = new SearchTradeProductRespBo();
        // 填充商品信息，补充额外的数据
        appendProductExtInfo(respBo, resultBo.getProductPage(), searchBo.getUserId());
        // 后续考虑多线程设置
        // 填充品牌信息
        appendBrandInfo(respBo, resultBo.getBranchIdList());
        // 填充类目信息
        appendCategoryInfo(respBo, resultBo.getCategoryIdList());
        // 填充店铺名称
        appendShopInfo(respBo);
        return respBo;
    }

    private SearchRequest buildSearchRequest(SearchProductBo searchBo) {
        // 构建查询条件
        BoolQueryBuilder boolQueryBuilder = buildSearchCondition(searchBo);
        // 约定：返回null代表没有需要返回结果为空
        if (boolQueryBuilder == null) {
            return null;
        }
        // 构建聚合查询
        List<AggregationBuilder> aggregationBuilders = buildAggregationList();
        // 构建排序
        List<SortBuilder<FieldSortBuilder>> sortBuilders = buildFieldSortList(searchBo);
        // 整合查询条件
        int from = PageUtil.getStart(searchBo.getPageNo(), searchBo.getPageSize());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(boolQueryBuilder)
                .from(from)
                .size(searchBo.getPageSize());
        // 设置聚合
        aggregationBuilders.forEach(sourceBuilder::aggregation);
        // 设置排序
        sortBuilders.forEach(sourceBuilder::sort);
        // 创建搜索请求
        SearchRequest searchRequest = new SearchRequest(EsConst.INDEX_TRADE_PRODUCT);
        searchRequest.source(sourceBuilder);
        return searchRequest;
    }

    /**
     * 构建商品搜索查询条件
     * 考虑查询时字段魔法值
     *
     * @param searchBo org.elasticsearch.index.query.BoolQueryBuilder
     * <AUTHOR>
     */
    private BoolQueryBuilder buildSearchCondition(SearchProductBo searchBo) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(searchBo.getSearchKey())) {
            String searchKey = searchBo.getSearchKey();
//            // 根据业务要求，用户输入的关键包含“，”“,”“ ”都视为多个商品搜索
//            searchKey = searchKey.replaceAll("[,，]", " ");
//            // 根据空格拆分为并列多个关键词
//            String[] keys = searchKey.split(" ");
            BoolQueryBuilder tempBoolQueryBuilder = QueryBuilders.boolQuery();
//            for (String key : keys) {
//                if (StringUtils.isEmpty(key)) {
//                    continue;
//                }
//                List<String> keywords = eagleService.analyze(key);
            MatchQueryBuilder productNameMatch = QueryBuilders.matchQuery(EsConst.TradeProduct.SEARCH_FIELD_PRODUCT_NAME, searchKey);
//                if (CollUtil.isNotEmpty(keywords)) {
//                    float minimumShouldMatch = keywords.size() * esIndexProps.getMinimumShouldProportion();
//                    log.info("product size={},minimumShouldMatch={}", keywords.size(), minimumShouldMatch);
//                    productNameMatch.minimumShouldMatch(Math.round(minimumShouldMatch) + "");
//                }
            tempBoolQueryBuilder.should(productNameMatch);
//            }

            boolQueryBuilder.must(tempBoolQueryBuilder
                    .should(QueryBuilders.wildcardQuery(EsConst.TradeProduct.SEARCH_FIELD_BARS_CODE, eagleService.appendWildcard(searchBo.getSearchKey())))
                    .minimumShouldMatch(1));
        }
        if (searchBo.getBrandId() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConst.TradeProduct.SEARCH_FIELD_BRAND_ID, searchBo.getBrandId()));
        }
        if (StrUtil.isNotBlank(searchBo.getBarcode())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConst.TradeProduct.SEARCH_FIELD_BARS_CODE, searchBo.getBarcode()));
        }
        if (StringUtils.isNotBlank(searchBo.getCategoryPath())) {
            boolQueryBuilder.filter(QueryBuilders.prefixQuery(EsConst.TradeProduct.SEARCH_FIELD_CATEGORY_PATH, searchBo.getCategoryPath()));
        }
        if (StringUtils.isNotBlank(searchBo.getAttributeValue())) {
            boolQueryBuilder.filter(QueryBuilders.nestedQuery(EsConst.TradeProduct.SEARCH_NEST_FIELD_PRODUCT_ATTRIBUTE,
                    QueryBuilders.termQuery(EsConst.TradeProduct.SEARCH_NEST_FIELD_PRODUCT_ATTRIBUTE_VALUE, searchBo.getAttributeValue()), ScoreMode.Avg));
        }
        if (CollectionUtils.isNotEmpty(searchBo.getProductIds())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery(EsConst.TradeProduct.SEARCH_FIELD_PRODUCT_ID, searchBo.getProductIds()));
        }
        if (searchBo.getCategoryId() != null) {
            String path = getCatePath(searchBo.getCategoryId());
            if (StrUtil.isBlank(path)) {
                return null;
            }
            boolQueryBuilder.filter(QueryBuilders.prefixQuery(EsConst.TradeProduct.SEARCH_FIELD_CATEGORY_PATH, path));
        }
        if (searchBo.getShopId() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConst.TradeProduct.SEARCH_FIELD_SHOP_ID, searchBo.getShopId()));
        }
        if (searchBo.getSaleStatus() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConst.TradeProduct.SEARCH_FIELD_SALE_STATUS, searchBo.getSaleStatus()));
        }
        if (searchBo.getAuditStatus() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConst.TradeProduct.SEARCH_FIELD_AUDIT_STATUS, searchBo.getAuditStatus()));
        }
        // 设置专属商家的条件。如果店铺开启了专属商家，且当前商家不是这个店铺的专属，则当前不能搜索到这个店铺的商品
        // 店铺接口返回的是当前用户能查看的所有的店铺，做了专属商家的过滤
        /*List<Long> visibleShopIdList = shopRemoteService.searchVisibleShopFromEs(searchBo.getUserId());
        // 约定：返回null代表没有需要返回结果为空
        if (CollUtil.isEmpty(visibleShopIdList)) {
            return null;
        }
        boolQueryBuilder.filter(QueryBuilders.termsQuery(EsConst.TradeProduct.SEARCH_FIELD_SHOP_ID, visibleShopIdList));*/

        // 如果传了优惠券ID 则查询可以使用该优惠券的商品
        if (searchBo.getCouponId() != null && searchBo.getCouponId() > 0) {
            List<Long> productIds = promotionRemoteService.getRelateProductIds(searchBo.getCouponId());
            if (CollectionUtils.isNotEmpty(productIds)) {
                boolQueryBuilder.filter(QueryBuilders.termsQuery(EsConst.TradeProduct.SEARCH_FIELD_PRODUCT_ID, productIds));
            }
        }

        return boolQueryBuilder;
    }

    private String getCatePath(Long cateId) {
        CategoryBo cate = productRemoteService.getCategoryById(cateId);
        if (cate == null) {
            return null;
        }
        String path = cate.getPath();
        if (cate.getDepth() == CommonConst.CATE_DEPTH_3) {
            return path;
        }
        return path + CommonConst.CATE_SPLITTER;
    }

    /**
     * 构建商品搜索结果的聚合条件
     *
     * <AUTHOR>
     */
    private List<AggregationBuilder> buildAggregationList() {
        List<AggregationBuilder> aggregationBuilders = new ArrayList<>();
        // 构建聚合查询

        // 聚合商品属性
        /*TermsAggregationBuilder attributeIdAggregation = AggregationBuilders.terms("attributeId")
                .field("productAttribute.attributeId")
                .subAggregation(AggregationBuilders.terms("attributeValues").field("productAttribute.attributeValues"));

        AggregationBuilder attrAgg = AggregationBuilders.nested(EsConst.TradeProduct.AGG_ATTRIBUTE, "productAttribute")
                .subAggregation(attributeIdAggregation);
        aggregationBuilders.add(attrAgg);*/

        // 聚合品牌ID
        AggregationBuilder brandIdAgg = AggregationBuilders.terms(EsConst.TradeProduct.AGG_BRAND_ID).field("brandId");
        aggregationBuilders.add(brandIdAgg);

        // 聚合类目ID
        AggregationBuilder cateIdAgg = AggregationBuilders.terms(EsConst.TradeProduct.AGG_CATEGORY_ID).field("categoryId");
        aggregationBuilders.add(cateIdAgg);

        return aggregationBuilders;
    }

    private List<SortBuilder<FieldSortBuilder>> buildFieldSortList(SearchProductBo searchBo) {
        List<SortBuilder<FieldSortBuilder>> sortBuilders = buildSelectFieldSort(searchBo);
        appendDefaultFieldSort(sortBuilders);
        return sortBuilders;
    }

    private List<SortBuilder<FieldSortBuilder>> buildSelectFieldSort(SearchProductBo searchBo) {
        List<FieldSortReq> sortList = searchBo.getSortList();
        if (CollectionUtils.isEmpty(sortList)) {
            // 会有默认排序，所以指定长度
            return new ArrayList<>(5);
        }
        return sortList.stream()
                .map(sf -> {
                    String esSort = ProductSortFieldMappingEnum.getEsFieldName(sf.getSort());
                    if (StrUtil.isBlank(esSort)) {
                        return null;
                    }
                    return SortBuilders.fieldSort(esSort).order(convert(sf.getIzAsc()));
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private void appendDefaultFieldSort(List<SortBuilder<FieldSortBuilder>> sortBuilders) {
        sortBuilders.add(SortBuilders.fieldSort(ProductSortFieldMappingEnum.SCORE.getEsFieldName()).order(SortOrder.DESC));
        sortBuilders.add(SortBuilders.fieldSort(ProductSortFieldMappingEnum.SHOP_PRODUCT_SEQUENCE.getEsFieldName()).order(SortOrder.DESC));
        // 序号一致时，优先销量大的排前
        sortBuilders.add(SortBuilders.fieldSort(ProductSortFieldMappingEnum.TOTAL_SALE_COUNTS.getEsFieldName()).order(SortOrder.DESC));
        // 销量一致时，优先上架时间近的排前
        sortBuilders.add(SortBuilders.fieldSort(ProductSortFieldMappingEnum.ON_SALE_TIME.getEsFieldName()).order(SortOrder.DESC));
    }

    private SortOrder convert(Boolean isAsc) {
        return isAsc ? SortOrder.ASC : SortOrder.DESC;
    }

    /**
     * 解析搜索结果
     *
     * @param searchResult ES结果
     * <AUTHOR>
     */
    private EsTradeProductResultBo resolveSearchResult(EagleQueryResult searchResult, SearchProductBo searchBo) {
        if (searchResult.getTotalHit() != null && searchResult.getTotalHit() == 0) {
            return EsTradeProductResultBo.defaultEmpty();
        }
        // 解析商品列表
        List<EsTradeProductBo> productList = searchResult.getHits().stream()
                .map(hit -> JsonUtil.parseObject(hit, EsTradeProductBo.class))
                .collect(Collectors.toList());
        // 解析商品属性
        // List<ProductAttributeBo> attrList = resolveAttributeAggregation(searchResult);
        // 解析品牌ID聚合
        List<Long> brandIdList = resolveBrandAggregation(searchResult);
        // 解析类目ID聚合
        List<Long> categoryIdList = resolveCategoryAggregation(searchResult);
        // 构造分页结果
        int totalHit = searchResult.getTotalHit().intValue();
        BasePageResp<EsTradeProductBo> productPage = new BasePageResp<>();
        productPage.setData(productList);
        productPage.setPages(PageUtil.totalPage(totalHit, searchBo.getPageSize()));
        productPage.setTotalCount(searchResult.getTotalHit());
        productPage.setPageNo(searchBo.getPageNo());
        productPage.setPageSize(searchBo.getPageSize());
        return EsTradeProductResultBo.builder()
                .productPage(productPage)
                // .attributeList(attrList)
                .branchIdList(brandIdList)
                .categoryIdList(categoryIdList)
                .build();
    }

    private List<ProductAttributeBo> resolveAttributeAggregation(EagleQueryResult searchResult) {
        if (searchResult.getAggregations() == null) {
            return Collections.emptyList();
        }
        // 获取属性聚合结果
        Nested nestedAggregation = searchResult.getAggregations().get(EsConst.TradeProduct.AGG_ATTRIBUTE);
        // 获取 attributeValues 的 terms 聚合
        Terms aggResult = nestedAggregation.getAggregations().get("attributeId");
        if (CollectionUtils.isEmpty(aggResult.getBuckets())) {
            return Collections.emptyList();
        }
        List<ProductAttributeBo> attributeList = new ArrayList<>(aggResult.getBuckets().size());
        // 遍历聚合桶
        for (Terms.Bucket bucket : aggResult.getBuckets()) {
            String attributeId = bucket.getKeyAsString();
            // 获取 attributeValues 的 terms 聚合
            Terms attributeValuesAggregation = bucket.getAggregations().get("attributeValues");
            List<String> attributeValues = attributeValuesAggregation.getBuckets().stream()
                    .map(MultiBucketsAggregation.Bucket::getKeyAsString)
                    .collect(Collectors.toList());
            ProductAttributeBo attrBo = ProductAttributeBo.builder()
                    .attributeId(Long.parseLong(attributeId))
                    .attributeValues(attributeValues)
                    .build();
            attributeList.add(attrBo);
        }
        return attributeList;
    }

    private List<Long> resolveBrandAggregation(EagleQueryResult searchResult) {
        if (searchResult.getAggregations() == null) {
            return Collections.emptyList();
        }
        // 获取聚合结果
        Terms brandIdAggregation = searchResult.getAggregations().get(EsConst.TradeProduct.AGG_BRAND_ID);
        return getDirectKeyLong(brandIdAggregation);
    }

    private List<Long> resolveCategoryAggregation(EagleQueryResult searchResult) {
        if (searchResult.getAggregations() == null) {
            return Collections.emptyList();
        }
        // 获取聚合结果
        Terms brandIdAggregation = searchResult.getAggregations().get(EsConst.TradeProduct.AGG_CATEGORY_ID);
        return getDirectKeyLong(brandIdAggregation);
    }

    private List<Long> getDirectKeyLong(Terms aggTerms) {
        if (CollectionUtils.isEmpty(aggTerms.getBuckets())) {
            return Collections.emptyList();
        }
        List<Long> idList = new ArrayList<>(aggTerms.getBuckets().size());
        // 遍历品牌聚合桶
        for (Terms.Bucket bucket : aggTerms.getBuckets()) {
            String id = bucket.getKeyAsString();
            idList.add(Long.valueOf(id));
        }
        return idList;
    }

    private SearchRequest buildSearchInShopRequest(SearchProductInShopBo searchBo) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // 店铺内搜索，店铺ID必填
        boolQueryBuilder.must(QueryBuilders.termQuery("shopId", searchBo.getShopId()));
        if (StringUtils.isNotBlank(searchBo.getSearchKey())) {
            // 构建搜索请求
            // 根据业务要求，用户输入的关键包含“，”“,”“ ”都视为多个商品搜索
            String searchKey = searchBo.getSearchKey();
//            searchKey = searchKey.replaceAll("[,，]", " ");
//            // 根据空格拆分为并列多个关键词
//            String[] keys = searchKey.split(" ");
            BoolQueryBuilder tempBoolQueryBuilder = QueryBuilders.boolQuery();
//            for (String key : keys) {
//                if (StringUtils.isEmpty(key)) {
//                    continue;
//                }
//                List<String> keywords = eagleService.analyze(key);
            MatchQueryBuilder productNameMatch = QueryBuilders.matchQuery(EsConst.TradeProduct.SEARCH_FIELD_PRODUCT_NAME, searchKey);
//                if (CollUtil.isNotEmpty(keywords)) {
//                    float minimumShouldMatch = keywords.size() * esIndexProps.getMinimumShouldProportion();
//                    productNameMatch.minimumShouldMatch(Math.round(minimumShouldMatch) + "");
//                }
            tempBoolQueryBuilder.should(productNameMatch);
//            }

            boolQueryBuilder.must(tempBoolQueryBuilder
                    .should(QueryBuilders.wildcardQuery(EsConst.TradeProduct.SEARCH_FIELD_BARS_CODE, eagleService.appendWildcard(searchBo.getSearchKey())))
                    .minimumShouldMatch(1));
        }
        if (searchBo.getSaleStatus() != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery(EsConst.TradeProduct.SEARCH_FIELD_SALE_STATUS, searchBo.getSaleStatus()));
        }
        if (searchBo.getAuditStatus() != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery(EsConst.TradeProduct.SEARCH_FIELD_AUDIT_STATUS, searchBo.getAuditStatus()));
        }
        if (searchBo.getMinPrice() != null || searchBo.getMaxPrice() != null) {
            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("minSalePrice");
            if (searchBo.getMinPrice() != null) {
                rangeQueryBuilder.gte(searchBo.getMinPrice());
            }
            if (searchBo.getMaxPrice() != null) {
                rangeQueryBuilder.lte(searchBo.getMaxPrice());
            }
            boolQueryBuilder.must(rangeQueryBuilder);
        }
        if (searchBo.getShopCategoryId() != null) {
            List<Long> subShopCateIdList = productRemoteService.getSubAndSelfShopCateById(searchBo.getShopCategoryId());
            if (CollUtil.isEmpty(subShopCateIdList)) {
                return null;
            }
            boolQueryBuilder.must(QueryBuilders.termsQuery("shopCategoryIds", subShopCateIdList));
        }
        // 构建排序
        List<SortBuilder<FieldSortBuilder>> sortBuilders = buildSelectFieldSort(searchBo);
        // 序号越大越靠前
        appendDefaultFieldSort(sortBuilders);
        // 整合查询条件
        int from = PageUtil.getStart(searchBo.getPageNo(), searchBo.getPageSize());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(boolQueryBuilder)
                .from(from)
                .size(searchBo.getPageSize());
        // 设置排序
        sortBuilders.forEach(sourceBuilder::sort);
        // 创建搜索请求
        SearchRequest searchRequest = new SearchRequest(EsConst.INDEX_TRADE_PRODUCT);
        searchRequest.source(sourceBuilder);
        return searchRequest;
    }

    private List<SortBuilder<FieldSortBuilder>> buildSelectFieldSort(BasePageReq searchBo) {
        List<FieldSortReq> sortList = searchBo.getSortList();
        if (CollectionUtils.isEmpty(sortList)) {
            // 会有默认排序，所以指定长度
            return new ArrayList<>(5);
        }
        return sortList.stream()
                .map(sf -> {
                    String esSort = ProductSortFieldMappingEnum.getEsFieldName(sf.getSort());
                    if (StrUtil.isBlank(esSort)) {
                        return null;
                    }
                    return SortBuilders.fieldSort(esSort).order(convert(sf.getIzAsc()));
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 解析店铺内搜索结果
     *
     * @param searchResult ES结果
     * <AUTHOR>
     */
    private BasePageResp<EsTradeProductBo> resolveSearchInShopResult(EagleQueryResult searchResult, SearchProductInShopBo searchBo) {
        if (searchResult.getTotalHit() != null && searchResult.getTotalHit() == 0) {
            return PageResultHelper.defaultEmpty(searchBo);
        }
        // 解析商品列表
        List<EsTradeProductBo> productList = searchResult.getHits().stream()
                .map(hit -> JsonUtil.parseObject(hit, EsTradeProductBo.class))
                .collect(Collectors.toList());
        // 构造分页结果
        int totalHit = searchResult.getTotalHit().intValue();
        BasePageResp<EsTradeProductBo> productPage = new BasePageResp<>();
        productPage.setData(productList);
        productPage.setPages(PageUtil.totalPage(totalHit, searchBo.getPageSize()));
        productPage.setTotalCount(searchResult.getTotalHit());
        productPage.setPageNo(searchBo.getPageNo());
        productPage.setPageSize(searchBo.getPageSize());
        return productPage;
    }

    /**
     * 构建商品收藏数数据
     *
     * @param productId 商品ID
     * @return 商品收藏数据
     */
    private Map<String, Map<String, Object>> buildProductCollectionCountData(Long productId) {
        Integer favoriteCount = userFavoriteRemoteService.getFavoriteCount(productId);
        EsTradeProductBo tradeProductBo = new EsTradeProductBo();
        tradeProductBo.setProductId(productId);
        tradeProductBo.setFavoriteCount(favoriteCount);

        // 构建ES存储对象
        return buildProductData(tradeProductBo);
    }

    private void appendProductExtInfo(SearchTradeProductRespBo respBo, BasePageResp<EsTradeProductBo> productPage, Long userId) {
        if (productPage == null || CollectionUtils.isEmpty(productPage.getData())) {
            return;
        }
        BasePageResp<SearchedTradeProductBo> searchedPage = convertAndAppend(productPage, userId, null);
        respBo.setProductPage(searchedPage);
    }

    private BasePageResp<SearchedTradeProductBo> convertAndAppend(BasePageResp<EsTradeProductBo> productPage, Long userId, PromotionSwitch promotionSwitch) {
        List<EsTradeProductBo> esProductList = productPage.getData();
        if (CollectionUtils.isEmpty(esProductList)) {
            return PageResultHelper.transfer(productPage, SearchedTradeProductBo.class);
        }

        // 获取商品ID列表
        Set<Long> productIdSet = new HashSet<>();
        Set<Long> shopIdSet = new HashSet<>();
        for (EsTradeProductBo prod : esProductList) {
            productIdSet.add(prod.getProductId());
            shopIdSet.add(prod.getShopId());
            // 热门关注商品，默认0
            prod.setFavoriteCount(ObjectUtil.defaultIfNull(prod.getFavoriteCount(), 0));
        }

        List<Long> productIdList = new ArrayList<>(productIdSet);
        List<Long> shopIdList = new ArrayList<>(shopIdSet);

        ThreadPoolExecutor executor = ThreadPoolUtil.ASYNC_API_POOL;
        // 阶梯价
        CompletableFuture<List<RemoteLadderPriceBo>> ladderPriceFuture = CompletableFuture.supplyAsync(() -> this.getLadderPrice(productIdList), executor)
                .exceptionally(ex -> {
                    log.error("交易商品填充数据，获取阶梯价异常", ex);
                    return new ArrayList<>(0);
                });
        // 获取店铺营销，userId是为了获取专享价
        CompletableFuture<Map<Long, RemoteShopUserPromotionBo>> shopPromotionFuture = CompletableFuture.supplyAsync(() -> this.getUserPromotion(shopIdList, userId), executor)
                .exceptionally(ex -> {
                    log.error("交易商品填充数据，获取店铺营销异常", ex);
                    return Collections.emptyMap();
                });
        // 获取商品sku信息
        CompletableFuture<Map<Long, List<RemoteSkuBo>>> skuFuture = CompletableFuture.supplyAsync(() -> productRemoteService.getProductSkuGroupByProductId(productIdList), executor)
                .exceptionally(ex -> {
                    log.error("交易商品填充数据，获取sku信息异常", ex);
                    return Collections.emptyMap();
                });
        // 获取商品的匹配的可用优惠券，并考虑用户的使用情况，即如果优惠券有限制，且用户用过，则不返回
        CompletableFuture<Map<Long, List<CouponSimpleResp>>> couponFuture = CompletableFuture.supplyAsync(() -> this.getProductMatchCoupon(userId, esProductList), executor)
                .exceptionally(ex -> {
                    log.error("交易商品填充数据，获取优惠券异常", ex);
                    return Collections.emptyMap();
                });
        // 获取购物车中商品数量
        CompletableFuture<Map<Long, Long>> productCountFuture = CompletableFuture.supplyAsync(() -> this.getProductCountInCart(userId, productIdList), executor)
                .exceptionally(ex -> {
                    log.error("交易商品填充数据，获取商品已购买数量异常", ex);
                    return Collections.emptyMap();
                });
        // 获取商品匹配的限时购活动
        CompletableFuture<Map<Long, EffectiveFlashSaleQueryResp>> flashSaleFuture = CompletableFuture.supplyAsync(() -> promotionRemoteService.getProductMatchFlashSaleToMap(productIdList), executor)
                .exceptionally(ex -> {
                    log.error("交易商品填充数据，获取限时购活动异常", ex);
                    return Collections.emptyMap();
                });
        // 阶梯价
        CompletableFuture<List<ShopResp>> shopFuture = CompletableFuture.supplyAsync(() -> shopRemoteService.queryShopByIds(shopIdList), executor)
                .exceptionally(ex -> {
                    log.error("交易商品填充数据，获取店铺信息异常", ex);
                    return new ArrayList<>(0);
                });

        // 当所有异步调用都完成时，执行业务逻辑
        try {
            // 等待所有异步调用完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(ladderPriceFuture, shopPromotionFuture, skuFuture, couponFuture, productCountFuture, flashSaleFuture);
            allFutures.get();

            // 阶梯价
            List<RemoteLadderPriceBo> ladderPriceList = ladderPriceFuture.get();
            Map<Long, List<RemoteLadderPriceBo>> ladderPriceMap = ladderPriceList.stream()
                    .collect(Collectors.groupingBy(RemoteLadderPriceBo::getProductId));
            // 按productId取阶梯价最小值
            Map<Long, BigDecimal> minLadderPrice = ladderPriceList.stream()
                    .collect(Collectors.toMap(RemoteLadderPriceBo::getProductId, RemoteLadderPriceBo::getPrice, (oldValue, newValue) -> oldValue.compareTo(newValue) < 0 ? oldValue : newValue));
            // 获取店铺营销，userId是为了获取专享价
            Map<Long, RemoteShopUserPromotionBo> shopPromotionMap = shopPromotionFuture.get();
            // 获取商品sku信息
            Map<Long, List<RemoteSkuBo>> skuMap = skuFuture.get();
            // 获取用户优惠券信息
            Map<Long, List<CouponSimpleResp>> productCouponMap = couponFuture.get();
            // 获取购物车中商品数量
            Map<Long, Long> productCountMap = productCountFuture.get();
            // 获取商品匹配的限时购活动
            Map<Long, EffectiveFlashSaleQueryResp> flashSaleMap = flashSaleFuture.get();
            // 获取店铺信息
            List<ShopResp> shopList = shopFuture.get();
            if (CollUtil.isEmpty(shopList)) {
                shopList = new ArrayList<>(1);
            }
            Map<Long, ShopResp> shopMap = shopList.stream().collect(Collectors.toMap(ShopResp::getId, Function.identity(), (oldV, newV) -> newV));
            // 拷贝并设置商品价格，包括计算预估到手价
            return PageResultHelper.transfer(productPage, SearchedTradeProductBo.class, (esProduct, searchedProduct) -> {
                fillTradeProductData(esProduct, searchedProduct, ladderPriceMap, minLadderPrice, shopPromotionMap, skuMap, productCouponMap, productCountMap, flashSaleMap, shopMap, promotionSwitch);
            });
        } catch (Exception e) {
            log.error("交易商品设置数据异常", e);
            // 异常时，返回商品列表，价格这些降级处理，返回默认值
            // 默认和公共数据
            return PageResultHelper.transfer(productPage, SearchedTradeProductBo.class, this::fillTradeProductDefaultData);
        }
    }

    private void appendBrandInfo(SearchTradeProductRespBo respBo, List<Long> branchIdList) {
        if (CollUtil.isEmpty(branchIdList)) {
            return;
        }
        List<BrandBo> brandList = productRemoteService.getBrandList(branchIdList);
        if (CollUtil.isEmpty(brandList)) {
            return;
        }
        List<TradeProductBrandBo> tradeBrandList = brandList.stream()
                .map(brand -> TradeProductBrandBo.builder()
                        .brandId(brand.getId())
                        .brandName(brand.getName())
                        .logo(brand.getLogo())
                        .build())
                .collect(Collectors.toList());
        respBo.setBrandList(tradeBrandList);
    }

    private void appendCategoryInfo(SearchTradeProductRespBo respBo, List<Long> categoryIdList) {
        List<CateLevel1Bo> cateList = productRemoteService.getCategoryTree(categoryIdList);
        log.info("【商品搜索】类目信息为: {}", JsonUtil.toJsonString(cateList));
        if (CollUtil.isEmpty(cateList)) {
            return;
        }
        respBo.setCategoryTreeList(cateList);
    }

    private void appendShopInfo(SearchTradeProductRespBo respBo) {
        BasePageResp<SearchedTradeProductBo> productPage = respBo.getProductPage();
        if (productPage == null || CollUtil.isEmpty(productPage.getData())) {
            return;
        }
        List<SearchedTradeProductBo> productList = productPage.getData();
        List<Long> shopIdList = productList.stream()
                .map(SearchedTradeProductBo::getShopId)
                .collect(Collectors.toList());
        List<ShopResp> shopList = shopRemoteService.queryShopByIds(shopIdList);
        Map<Long, ShopResp> shop = shopList.stream()
                .collect(Collectors.toMap(ShopResp::getId, Function.identity(), (oldValue, newValue) -> newValue));
        productList.forEach(product -> {
            ShopResp shopResp = shop.get(product.getShopId());
            if (shopResp != null) {
                product.setShopName(shopResp.getShopName());
            }
        });
    }


    private BasePageResp<SearchedTradeProductBo> searchForAddon(QueryAddonProductParamBo queryParam) {
        // 构建搜索请求
        SearchRequest searchRequest = buildAddonSearchRequest(queryParam);
        log.info("【凑单商品搜索】搜索条件为: {}", JsonUtil.toJsonString(searchRequest));
        // 调用ES进行查询和聚合
        EagleQueryResult searchResult = eagleService.queryByCondition(searchRequest);
        log.info("【凑单商品搜索】搜索结果为: {}", JsonUtil.toJsonString(searchResult));
        // 解析搜索结果
        if (searchResult.getTotalHit() != null && searchResult.getTotalHit() == 0) {
            return PageResultHelper.defaultEmpty(queryParam);
        }
        // 解析商品列表
        List<EsTradeProductBo> productList = searchResult.getHits().stream()
                .map(hit -> JsonUtil.parseObject(hit, EsTradeProductBo.class))
                .collect(Collectors.toList());
        // 构造分页结果
        int totalHit = searchResult.getTotalHit().intValue();
        BasePageResp<EsTradeProductBo> productPage = new BasePageResp<>();
        productPage.setData(productList);
        productPage.setPages(PageUtil.totalPage(totalHit, queryParam.getPageSize()));
        productPage.setTotalCount(searchResult.getTotalHit());
        productPage.setPageNo(queryParam.getPageNo());
        productPage.setPageSize(queryParam.getPageSize());

        // 营销与专享价互斥
        PromotionSwitch promotionSwitch = PromotionSwitch.builder()
                .excludeExclusivePrice(true)
                .excludeFlashSale(true)
                .build();
        // 限时购与折扣互斥
        if (AddonProductPromotionTabEnum.DISCOUNT.getCode().equals(queryParam.getType())) {
            promotionSwitch.setExcludeFlashSale(true);
        }
        BasePageResp<SearchedTradeProductBo> resultPage = convertAndAppend(productPage, queryParam.getUser().getUserId(), promotionSwitch);
        log.debug("【凑单商品搜索】列表结果为: {}", JsonUtil.toJsonString(resultPage));
        return resultPage;
    }

    private void extAddonDiscountQueryParam(QueryAddonProductParamBo queryParam, AddonProductContext tempContext) {
        RemoteDiscountBo shopDiscount = tempContext.getShopDiscount();
        if (shopDiscount == null) {
            return;
        }
        // 适用所有商品与选择部分商品是互斥的。
        boolean discountForAll = Boolean.TRUE.equals(shopDiscount.getIzAllProduct());
        // 如果不是适用所有商品，则需要获取折扣活动的商品ID列表
        if (!discountForAll) {
            queryParam.setDiscountProductIdList(shopDiscount.getProductIdList());
        }
        queryParam.setDiscountForAll(discountForAll);
    }

    private AddonProductContext buildAddonTempContext(QueryAddonProductParamBo queryParam) {
        // 用户购物车勾选的商品汇总
        int selectedProductCount = 0;
        BigDecimal selectedProductAmount = BigDecimal.ZERO;
        List<Long> productIdList = new ArrayList<>();
        for (ShopProductBo bo : queryParam.getProductList()) {
            if (Boolean.TRUE.equals(bo.getWhetherSelected())) {
                selectedProductCount++;
                // 初始商品金额，用原价重置，后续根据折扣或者满减重新计算
                bo.setFinalSalePrice(bo.getRealSalePrice());
                bo.setTotalAmount(cn.hutool.core.util.NumberUtil.mul(bo.getRealSalePrice(), bo.getQuantity()).setScale(2, RoundingMode.HALF_UP));
                selectedProductAmount = selectedProductAmount.add(NumberUtil.mul(bo.getRealSalePrice(), bo.getQuantity()));
                productIdList.add(bo.getProductId());
            }
        }

        RemoteDiscountBo shopDiscount = null;
        // 只有折扣活动才需要查询店铺的折扣商品
        if (AddonProductPromotionTabEnum.DISCOUNT.getCode().equals(queryParam.getType())) {
            // 店铺的折扣，通过活动ID获取折扣活动以及对应的商品列表
            shopDiscount = promotionRemoteService.getDiscountById(queryParam.getActivityId());
            AssertUtil.throwIfNull(shopDiscount, "折扣活动不存在");
        } else if (AddonProductPromotionTabEnum.REDUCTION.getCode().equals(queryParam.getType())) {
            // 店铺的满减
            RemoteReductionBo reduction = promotionRemoteService.getReductionById(queryParam.getActivityId());
            AssertUtil.throwIfNull(reduction, "满减活动不存在");
        } else {
            throw new BusinessException("【凑单商品搜索】不支持的凑单类型: " + queryParam.getType());
        }

        AddonProductContext tempContext = new AddonProductContext();
        tempContext.setSelectedProductAmount(selectedProductAmount);
        tempContext.setSelectedProductCount(selectedProductCount);
        tempContext.setShopDiscount(shopDiscount);
        tempContext.setDiscountForAll(queryParam.getDiscountForAll());
        tempContext.setSelectedProductIdList(productIdList);
        tempContext.setShopId(queryParam.getShop().getShopId());
        tempContext.setUserId(queryParam.getUser().getUserId());
        tempContext.setProductList(queryParam.getProductList());
        if (!Objects.isNull(shopDiscount)) {
            tempContext.setActivityId(shopDiscount.getDiscountActId());
        }
        return tempContext;
    }

    private SearchRequest buildAddonSearchRequest(QueryAddonProductParamBo queryParam) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // 店铺内搜索，店铺ID必填
        boolQueryBuilder.must(QueryBuilders.termQuery("shopId", queryParam.getShop().getShopId()));
        // 如果是满减，则查询店铺所有商品；如果是折扣，这需要先获取折扣活动的商品信息
        if (AddonProductPromotionTabEnum.DISCOUNT.getCode().equals(queryParam.getType())) {
            if (!queryParam.getDiscountForAll()) {
                // 如果是适用部分商品，则需要查询商品ID列表
                boolQueryBuilder.must(QueryBuilders.termsQuery("productId", queryParam.getDiscountProductIdList()));
            }

        }

        // 只展示销售中的商品
        boolQueryBuilder.must(QueryBuilders.termQuery(EsConst.TradeProduct.SEARCH_FIELD_SALE_STATUS, ProductEnum.SaleStatusEnum.ON_SALE.getCode()));
        boolQueryBuilder.must(QueryBuilders.termQuery(EsConst.TradeProduct.SEARCH_FIELD_AUDIT_STATUS, ProductEnum.AuditStatusEnum.ON_SALE.getCode()));

        // 构建排序
        List<SortBuilder<FieldSortBuilder>> sortBuilders = buildSelectFieldSort(queryParam.getSortList());
        appendDefaultFieldSort(sortBuilders);
        // 整合查询条件
        int from = PageUtil.getStart(queryParam.getPageNo(), queryParam.getPageSize());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(boolQueryBuilder)
                .from(from)
                .size(queryParam.getPageSize());
        // 设置排序
        sortBuilders.forEach(sourceBuilder::sort);
        // 创建搜索请求
        SearchRequest searchRequest = new SearchRequest(EsConst.INDEX_TRADE_PRODUCT);
        searchRequest.source(sourceBuilder);
        return searchRequest;
    }

    private List<SortBuilder<FieldSortBuilder>> buildSelectFieldSort(List<FieldSortReq> sortList) {
        if (CollectionUtils.isEmpty(sortList)) {
            return new ArrayList<>(5);
        }
        return sortList.stream()
                .map(sf -> SortBuilders.fieldSort(sf.getSort()).order(convert(sf.getIzAsc())))
                .collect(Collectors.toList());
    }


    /**
     * 构建商品访问量数据
     *
     * @param productId   商品ID
     * @param visitCounts 访问量
     * @return 商品访问量数据
     */
    private Map<String, Map<String, Object>> buildProductVisitData(Long productId, Integer visitCounts) {
        EsTradeProductBo tradeProductBo = new EsTradeProductBo();
        tradeProductBo.setProductId(productId);
        tradeProductBo.setVisitCounts(visitCounts);

        // 构建ES存储对象
        return buildProductData(tradeProductBo);
    }

    /**
     * 构建商品评论汇总数据
     *
     * @param productId      商品ID
     * @param productSummary 商品评论汇总数据
     * @return 商品评论汇总数据
     */
    private Map<String, Map<String, Object>> buildProductCommentSummaryData(Long productId, ProductCommentSummaryResp productSummary) {
        EsTradeProductBo tradeProductBo = new EsTradeProductBo();
        tradeProductBo.setProductId(productId);
        tradeProductBo.setCommentSummary(JsonUtil.copy(productSummary, EsCommentSummaryBo.class));

        // 构建ES存储对象
        return buildProductData(tradeProductBo);
    }

    private Map<String, Map<String, Object>> buildProductData(EsTradeProductBo tradeProductBo) {
        // 构建ES存储对象
        Map<String, Map<String, Object>> paramMap = new HashMap<>(2);
        paramMap.put(String.valueOf(tradeProductBo.getProductId()), JsonUtil.beanToMap(tradeProductBo));
        return paramMap;
    }

    /**
     * 根据商品ID获取商品信息
     *
     * @param productId 商品ID
     * @return 商品信息
     */
    private EsTradeProductBo getProductById(Long productId) {
        SearchResponse searchResult = eagleService.queryById(EsConst.INDEX_TRADE_PRODUCT, Collections.singletonList(String.valueOf(productId)));

        // 构建搜索请求
        log.info("【商品搜索】搜索条件为: {}, 搜索结果为: {}", JsonUtil.toJsonString(productId), JsonUtil.toJsonString(searchResult));
        SearchHits hits = searchResult.getHits();
        List<String> productStrList = Lists.newArrayList();
        for (SearchHit next : hits) {
            productStrList.add(next.getSourceAsString());
        }
        if (CollectionUtils.isEmpty(productStrList)) {
            return null;
        }
        return JsonUtil.parseObject(productStrList.get(0), EsTradeProductBo.class);
    }

    /**
     * 根据商品、价格获取满足的优惠券的最大金额
     *
     * @param productId        商品ID
     * @param salePrice        商品当前价格
     * @param shopId           店铺ID
     * @param productCouponMap 优惠券列表
     * <AUTHOR>
     */
    private BigDecimal getMaxCouponPrice(Long productId, BigDecimal salePrice, Long shopId, Map<Long, List<CouponSimpleResp>> productCouponMap,
                                         PromotionSwitch promotionSwitch) {
        if (Boolean.TRUE.equals(promotionSwitch.getExcludeCoupon())) {
            return BigDecimal.ZERO;
        }
        List<CouponSimpleResp> couponList = productCouponMap.get(productId);
        if (CollectionUtils.isEmpty(couponList)) {
            return BigDecimal.ZERO;
        }
        // 降序，面额最大的优先处理，这里返回的已经是每个商品满足的优惠券了
        couponList.sort(Comparator.comparing(CouponSimpleResp::getPrice).reversed());
        for (CouponSimpleResp coupon : couponList) {
            // 金额是否满足条件
            BigDecimal condAmount = cn.hutool.core.util.NumberUtil.nullToZero(coupon.getOrderAmount());
            if (salePrice.compareTo(condAmount) < 0) {
                continue;
            }
            return coupon.getPrice();
        }
        return BigDecimal.ZERO;
    }

    private BigDecimal getReductionPrice(BigDecimal salePrice, RemoteReductionBo shopReduction,
                                         PromotionSwitch promotionSwitch) {
        if (Boolean.TRUE.equals(promotionSwitch.getExcludeReduction())) {
            return BigDecimal.ZERO;
        }
        if (shopReduction == null || salePrice.compareTo(shopReduction.getMoneyOffCondition()) < 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal condition = shopReduction.getMoneyOffCondition();
        BigDecimal moneyOffFee = shopReduction.getMoneyOffFee();
        // 理论上不存在条件是0的，认为是脏数据
        if (condition.compareTo(BigDecimal.ZERO) == 0 || moneyOffFee == null || moneyOffFee.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        // 满减是否叠加
        boolean overlay = Boolean.TRUE.equals(shopReduction.getMoneyOffOverLay());
        // 计算当前金额满足的叠加次数
        int reductionCount = 1;
        if (overlay) {
            reductionCount = salePrice.divideToIntegralValue(condition).intValue();
        }
        return cn.hutool.core.util.NumberUtil.mul(moneyOffFee, reductionCount);
    }

    private BigDecimal findMaxCouponOrReductionPrice(Long productId, BigDecimal salePrice, Long shopId,
                                                     Map<Long, List<CouponSimpleResp>> productCouponMap, RemoteReductionBo shopReduction,
                                                     PromotionSwitch promotionSwitch) {
        BigDecimal maxCouponPrice = getMaxCouponPrice(productId, salePrice, shopId, productCouponMap, promotionSwitch);
        BigDecimal maxReductionPrice = getReductionPrice(salePrice, shopReduction, promotionSwitch);
        log.info("【商品优惠】商品ID: {}, 最大优惠券金额: {}, 最大满减金额: {}", productId, maxCouponPrice, maxReductionPrice);
        return maxCouponPrice.max(maxReductionPrice);
    }


    private Map<Long, Long> getProductCountInCart(Long userId, List<Long> productIdList) {
        List<ShoppingCart> cartList = shoppingCartRepository.getByUserAndProductIds(userId, productIdList);
        if (CollectionUtils.isEmpty(cartList)) {
            return Collections.emptyMap();
        }
        // 购物车数据按照商品ID分组，并汇总数量
        return cartList.stream()
                .collect(Collectors.groupingBy(ShoppingCart::getProductId, Collectors.summingLong(ShoppingCart::getQuantity)));
    }

    private List<RemoteLadderPriceBo> getLadderPrice(List<Long> productIdList) {
        if (CollUtil.isEmpty(productIdList)) {
            return new ArrayList<>(0);
        }
        List<RemoteLadderPriceBo> list = productRemoteService.queryProductLadderPrice(productIdList);
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>(0);
        }
        return list;
    }

    /**
     * 获取用户相关的营销
     *
     * @param shopIdList 店铺ID
     * @param userId     用户ID，未登录时为空，此时营销不会返回专享价
     * <AUTHOR>
     */
    private Map<Long, RemoteShopUserPromotionBo> getUserPromotion(List<Long> shopIdList, Long userId) {
        List<RemoteShopUserPromotionBo> promotionList = promotionRemoteService.queryValidShopUserPromotion(shopIdList, userId);
        return promotionList.stream()
                .collect(Collectors.toMap(RemoteShopUserPromotionBo::getShopId, Function.identity(), (oldValue, newValue) -> newValue));
    }

    private Map<Long, List<CouponSimpleResp>> getProductMatchCoupon(Long userId, List<EsTradeProductBo> esProductList) {
        if (CollUtil.isEmpty(esProductList)) {
            return Collections.emptyMap();
        }
        Map<Long, List<Long>> productMap = esProductList.stream()
                .collect(Collectors.groupingBy(EsTradeProductBo::getShopId, Collectors.mapping(EsTradeProductBo::getProductId, Collectors.toList())));
        return promotionRemoteService.getProductMatchCouponWithUserLimit(userId, productMap);
    }

    /**
     * 填充交易商品相关数据
     * <p>价格规则：单规格商品，如果只有基本价格和专享价，页面不需要显示 XX元起，直接显示XX元；否则如果有阶梯价，则显示XX元起；
     * 多规格商品，如果多规格的价格相同，且没有专享价、阶梯价，则直接显示XX元；否则如果多规格价格不同，或者有规格有专享价、阶梯价，则显示XX元起；</p>
     *
     * <AUTHOR>
     */
    private void fillTradeProductData(EsTradeProductBo esProduct, SearchedTradeProductBo searchedProduct,
                                      Map<Long, List<RemoteLadderPriceBo>> ladderPriceMap, Map<Long, BigDecimal> minLadderPrice,
                                      Map<Long, RemoteShopUserPromotionBo> shopPromotionMap,
                                      Map<Long, List<RemoteSkuBo>> skuMap,
                                      Map<Long, List<CouponSimpleResp>> productCouponMap,
                                      Map<Long, Long> productCountMap,
                                      Map<Long, EffectiveFlashSaleQueryResp> flashSaleMap,
                                      Map<Long, ShopResp> shopMap,
                                      PromotionSwitch promotionSwitch) {
        // 默认和公共数据
        fillTradeProductDefaultData(esProduct, searchedProduct);

        // 单规格商品返回skuId，多规格商品用户弹窗选择sku
        // 默认值和这里会设置基本的是否显示XX元起，后续还会按需重置
        if (skuMap != null && skuMap.containsKey(esProduct.getProductId())) {
            List<RemoteSkuBo> skuList = skuMap.get(esProduct.getProductId());
            boolean singleSku = skuList.size() == 1;
            searchedProduct.setWhetherSingleSku(singleSku);
            if (singleSku) {
                searchedProduct.setSkuId(skuList.get(0).getSkuId());
                searchedProduct.setSingleSkuStock(esProduct.getTotalStock());
            }
            // 多规格商品，如果价格不相同，需要显示XX元起(ES查询结果中的minSalePrice已经是多规格最低价，这里只需要判断多规格价格是否一致)
            else {
                boolean anyNotMatch = skuList.stream()
                        .anyMatch(sku -> sku.getSalePrice().compareTo(searchedProduct.getMinSalePrice()) != 0);
                searchedProduct.setShowPriceStartAt(anyNotMatch);
            }
        }

        // 计算填充价格
        fillTradeProductPrice(esProduct, searchedProduct, minLadderPrice, shopPromotionMap, productCouponMap, flashSaleMap, promotionSwitch);

        // 阶梯价
        List<RemoteLadderPriceBo> productLadderList = ladderPriceMap.get(esProduct.getProductId());
        // 倍数起购量
        int multipleCount = shoppingCartAssist.getMultipleCount(searchedProduct.getMultipleCount());
        // 计算并设置起购量，起购量需要综合阶梯价和倍数起购量进行计算
        int minBuyCount = shoppingCartAssist.calculateBaseBuyCount(multipleCount, productLadderList);
        searchedProduct.setMinBuyCount(minBuyCount);

        // 商品上的总库存是基于sku的总的，=0代表任意sku都没有库存了
        if (esProduct.getTotalStock() == null || esProduct.getTotalStock() <= 0) {
            searchedProduct.setWhetherOutOfStock(true);
        }

        // 设置商品维度的购物车数量
        searchedProduct.setCartProductCount(cn.hutool.core.util.NumberUtil.nullToZero(productCountMap.get(esProduct.getProductId())));

        // 设置店铺信息
        ShopResp shop = shopMap.get(esProduct.getShopId());
        if (shop != null) {
            searchedProduct.setShopName(shop.getShopName());
        }
    }

    private void fillTradeProductDefaultData(EsTradeProductBo esProduct, SearchedTradeProductBo searchedProduct) {
        // 优先取专享价，没有专享价设置阶梯价，还没有取多SKU最低价，所以售价先默认sku最低价
        BigDecimal minSalePrice = cn.hutool.core.util.NumberUtil.nullToZero(esProduct.getMinSalePrice());
        searchedProduct.setMinSalePrice(minSalePrice);
        searchedProduct.setSalePrice(minSalePrice);
        searchedProduct.setTempSalePrice(minSalePrice);
        // 预估到手价初始为null
        searchedProduct.setEstimatePrice(null);
        searchedProduct.setWhetherExclusive(false);
        searchedProduct.setWhetherFlashSale(false);
        searchedProduct.setShowPriceStartAt(false);
        // 设置浏览量
        searchedProduct.setVisitCounts(ObjectUtil.defaultIfNull(esProduct.getVisitCounts(), 0));
        // 设置总销量，将虚拟销量加上去
        searchedProduct.setTotalSaleCounts(ObjectUtil.defaultIfNull(esProduct.getTotalSaleCounts(), 0L));
        EsCommentSummaryBo comment = esProduct.getCommentSummary();
        if (comment != null) {
            searchedProduct.setEvaluateNum(cn.hutool.core.util.NumberUtil.nullToZero(comment.getTotalCount()));
        }
        searchedProduct.setWhetherSingleSku(false);
    }

    /**
     * 填充设置交易商品的价格
     * <p>
     * 1. 如果存在限时购活动，则优先级最高，且排他
     * 2. 接着看是否有专享价，排他
     * 3. 接下来是阶梯价
     * 4. 最后前面价格都没有，取sku的最小价格作为默认价格
     * 5. 预估到手价，基于前面的价格，以及折扣、满减、优惠券计算，整体要去优惠金额最大的
     * 6. 优惠券是用户已领取的，或者可以领取的商品的最高面额的
     * </p>
     *
     * <AUTHOR>
     */
    private void fillTradeProductPrice(EsTradeProductBo esProduct, SearchedTradeProductBo searchedProduct,
                                       Map<Long, BigDecimal> minLadderPrice,
                                       Map<Long, RemoteShopUserPromotionBo> shopPromotionMap,
                                       Map<Long, List<CouponSimpleResp>> productCouponMap,
                                       Map<Long, EffectiveFlashSaleQueryResp> flashSaleMap,
                                       PromotionSwitch promotionSwitch) {
        log.info("交易商品填充价格, salePrice={}, showPriceStartAt={}, promotionSwitch={}", searchedProduct.getSalePrice(), searchedProduct.getShowPriceStartAt(), promotionSwitch);
        // 专享价，处理的结果是商品维度的，所以满足 多规格商品只要有一个规格参与专享价就显示
        RemoteShopUserPromotionBo shopPromotion = shopPromotionMap.get(esProduct.getShopId());
        Map<Long, BigDecimal> minExclusivePriceMap = promotionAssist.flatMinExclusivePrice(shopPromotion);
        if (promotionSwitch == null) {
            promotionSwitch = PromotionSwitch.defaultValue();
        }

        Long productId = esProduct.getProductId();
        // 优先限时购活动价格
        EffectiveFlashSaleQueryResp flashSale = null;
        if (!Boolean.TRUE.equals(promotionSwitch.excludeFlashSale) && flashSaleMap.containsKey(productId) &&
                (flashSale = flashSaleMap.get(productId)) != null &&
                // 进行中一定是审核通过的
                FlashSaleStatusEnum.ONGOING.getStatus().equals(flashSale.getStatus())) {
            BigDecimal price = cn.hutool.core.util.NumberUtil.nullToZero(flashSale.getMinPrice());
            searchedProduct.setSalePrice(price);
            searchedProduct.setTempSalePrice(price);
            if (Boolean.TRUE.equals(flashSale.getFrontFlag())) {
                searchedProduct.setWhetherFlashSale(true);
            }
            log.info("交易商品填充价格, 填充限时购活动价格, salePrice={}", price);
        }
        // 专享价
        else if (!Boolean.TRUE.equals(promotionSwitch.excludeExclusivePrice) && minExclusivePriceMap.containsKey(productId)) {
            // 专享价这里是取专享价与原多规格价格的最小值
            BigDecimal price = (minExclusivePriceMap.get(productId)).min(searchedProduct.getSalePrice());
            searchedProduct.setSalePrice(price);
            searchedProduct.setTempSalePrice(price);
            searchedProduct.setWhetherExclusive(true);
            // 多规格，且专享价与多规格最小价不一致，则显示XX元起
            if (!Boolean.TRUE.equals(searchedProduct.getWhetherSingleSku()) && price.compareTo(searchedProduct.getMinSalePrice()) != 0) {
                searchedProduct.setShowPriceStartAt(true);
            }
            log.info("交易商品填充价格, 填充专享价, salePrice={}, showPriceStartAt={}", price, searchedProduct.getShowPriceStartAt());
        }
        // 阶梯价
        else if (minLadderPrice.containsKey(productId)) {
            // 设置阶梯价
            BigDecimal ladderPrice = minLadderPrice.get(productId);
            searchedProduct.setSalePrice(ladderPrice);
            searchedProduct.setTempSalePrice(ladderPrice);
            // 阶梯价有价格梯度，可以稳定显示XX元起
            searchedProduct.setShowPriceStartAt(true);
            log.info("交易商品填充价格, 填充阶梯价, salePrice={}", ladderPrice);
        }
        // 限时购、专享价不参与折扣
        if (!Boolean.TRUE.equals(searchedProduct.getWhetherExclusive()) &&
                !Boolean.TRUE.equals(searchedProduct.getWhetherFlashSale())) {
            // 继续处理折扣
            Map<Long, RemoteDiscountRuleBo> productDiscountMap = promotionAssist.findProductDiscount(shopPromotion, searchedProduct.getSalePrice());
            // 如果有适用所有商品的折扣
            RemoteDiscountRuleBo rule = null;
            if (productDiscountMap.containsKey(CommonConst.ALL_PRODUCT_ID)) {
                rule = productDiscountMap.get(CommonConst.ALL_PRODUCT_ID);
            } else if (productDiscountMap.containsKey(productId)) {
                // 如果有适用于单个商品的折扣
                rule = productDiscountMap.get(productId);
            }
            log.info("交易商品填充价格, 填充折扣, salePrice={}, discount={}", searchedProduct.getSalePrice(), JsonUtil.toJsonString(rule));
            if (rule != null && searchedProduct.getSalePrice().compareTo(rule.getQuota()) >= 0) {
                BigDecimal discount = cn.hutool.core.util.NumberUtil.div(rule.getDiscount(), CommonConst.TEN, 2);
                BigDecimal discountPrice = cn.hutool.core.util.NumberUtil.mul(searchedProduct.getSalePrice(), discount).setScale(2, RoundingMode.HALF_UP);
                searchedProduct.setEstimatePrice(discountPrice);
                // 这里只修改中间临时价格，用于后续优惠券和满减的计算
                searchedProduct.setTempSalePrice(discountPrice);
                log.info("交易商品填充价格, 填充折扣, salePrice={}, tempSalePrice={}, estimatePrice={}",
                        searchedProduct.getSalePrice(), searchedProduct.getTempSalePrice(), discountPrice);
            }
        }

        // 判断优惠券和满减的最大值，优惠券和满减是互斥的，所以取最大值处理
        RemoteReductionBo shopReduction = shopPromotion == null ? null : shopPromotion.getShopReduction();
        // 取此时的售价，即计算了阶梯价、专享价、折扣后的售价
        BigDecimal maxCouponOrReductionPrice = findMaxCouponOrReductionPrice(productId, searchedProduct.getTempSalePrice(), esProduct.getShopId(), productCouponMap, shopReduction, promotionSwitch);
        BigDecimal newSalePrice = searchedProduct.getTempSalePrice().subtract(maxCouponOrReductionPrice);
        if (newSalePrice.compareTo(BigDecimal.ZERO) < 0) {
            newSalePrice = BigDecimal.ZERO;
        }
        searchedProduct.setEstimatePrice(newSalePrice);
        // 如果预估到手价与售价相同，则不显示预估到手价
        if (searchedProduct.getSalePrice() != null
                && searchedProduct.getSalePrice().equals(searchedProduct.getEstimatePrice())) {
            searchedProduct.setEstimatePrice(null);
        }
    }

    @Getter
    @Setter
    @Builder
    @ToString
    static class PromotionSwitch {
        private Boolean excludeFlashSale;
        private Boolean excludeExclusivePrice;
        private Boolean excludeCoupon;
        private Boolean excludeDiscount;
        private Boolean excludeReduction;

        public static PromotionSwitch defaultValue() {
            return PromotionSwitch.builder()
                    .excludeFlashSale(false)
                    .excludeExclusivePrice(false)
                    .excludeCoupon(false)
                    .excludeDiscount(false)
                    .excludeReduction(false)
                    .build();
        }
    }

}
