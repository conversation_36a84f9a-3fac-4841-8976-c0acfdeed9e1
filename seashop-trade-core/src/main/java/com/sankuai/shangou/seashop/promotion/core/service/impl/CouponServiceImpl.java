package com.sankuai.shangou.seashop.promotion.core.service.impl;

import static com.sankuai.shangou.seashop.promotion.common.constant.PromotionConstant.PRODUCT_DELETED;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.request.BaseBatchIdReq;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.leaf.LeafService;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.product.core.remote.ProductRemoteService;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductByIdReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductPageResp;
import com.sankuai.shangou.seashop.promotion.common.constant.LeafConstant;
import com.sankuai.shangou.seashop.promotion.common.constant.PromotionConstant;
import com.sankuai.shangou.seashop.promotion.common.enums.PromotionResultCodeEnum;
import com.sankuai.shangou.seashop.promotion.common.remote.MemberRemoteService;
import com.sankuai.shangou.seashop.promotion.core.model.bo.CouponReceiveBo;
import com.sankuai.shangou.seashop.promotion.core.model.bo.CouponSaveBo;
import com.sankuai.shangou.seashop.promotion.core.service.CouponService;
import com.sankuai.shangou.seashop.promotion.core.service.assist.ProductAssistForPromotion;
import com.sankuai.shangou.seashop.promotion.core.service.assist.bo.SaveCouponLogBo;
import com.sankuai.shangou.seashop.promotion.core.service.assist.operationLog.PromotionLogAssist;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.Coupon;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.CouponProduct;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.CouponRecord;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.CouponSetting;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.SendMessageRecordCoupon;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.SendMessageRecordCouponSn;
import com.sankuai.shangou.seashop.promotion.dao.core.model.ActiveBaseDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponExtDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponParamDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponProductParamDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponRecordParamDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponSettingParamDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponSimpleDto;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.CouponProductRepository;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.CouponRecordRepository;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.CouponRepository;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.CouponSettingRepository;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.SendMessageRecordCouponRepository;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.SendMessageRecordCouponSnRepository;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.CouponProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.ActiveStatusEnum;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.CouponStatusEnum;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.ReceiveTypeEnum;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.UseAreaEnum;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ProductAndShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.SendCouponCmdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponProductQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.ProductAvailableQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponSimpleListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.coupon.ProductAvailableQueryResp;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryMemberListReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberListResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberResp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description:
 */
@Service
@Slf4j
public class CouponServiceImpl implements CouponService {

    @Resource
    private CouponRepository couponRepository;
    @Resource
    private CouponProductRepository couponProductRepository;
    @Resource
    private CouponSettingRepository couponSettingRepository;
    @Resource
    private CouponRecordRepository couponRecordRepository;
    @Resource
    private SendMessageRecordCouponRepository sendMessageRecordCouponRepository;
    @Resource
    private SendMessageRecordCouponSnRepository sendMessageRecordCouponSnRepository;
    @Resource
    private LeafService leafService;
    @Resource
    private MemberRemoteService memberRemoteService;
    @Resource
    private ProductRemoteService productRemoteService;
    @Resource
    private PromotionLogAssist promotionLogAssist;
    @Resource
    private ProductAssistForPromotion productAssistForPromotion;

    @Value("${promotion.receive.coupon.switch:true}")
    private Boolean couponReceiveSwitch;

    @Override
//    @ExaminProcess(processModel = ExaminModelEnum.PROMOTION, processType = ExaProEnum.MODIFY, serviceMethod = "", dto = CouponSaveBo.class, entity = Coupon.class)
    public void save(CouponSaveBo saveBo) {
        log.info("save-saveBo:{}", saveBo);

        Coupon saveCoupon;
        CouponResp oldCoupon;
        if (null != saveBo.getId()) {
            saveCoupon = couponRepository.findById(saveBo.getId());
            if (null == saveCoupon) {
                throw new BusinessException(PromotionResultCodeEnum.DISCOUNT_ACTIVE_NOT_FIND.getCode(), PromotionResultCodeEnum.DISCOUNT_ACTIVE_NOT_FIND.getMsg());
            }
            // 已经结束的优惠券不能再编辑
            Date endTime = saveCoupon.getEndTime();
            if (endTime.before(new Date())) {
                throw new BusinessException(PromotionResultCodeEnum.COUPON_ACTIVE_END.getCode(), PromotionResultCodeEnum.COUPON_ACTIVE_END.getMsg());
            }
            oldCoupon = getById(saveBo.getId());
        }
        else {
            oldCoupon = null;
            saveCoupon = new Coupon();
        }

        if (null != saveBo.getId()) {
            // 修改时，需要判断已领用的优惠券数量，不能产生超发的情况
            List<CouponRecord> couponRecordList = couponRecordRepository.listByCouponId(saveBo.getId());
            if (CollUtil.isNotEmpty(couponRecordList)) {
                if (saveBo.getNum() < couponRecordList.size()) {
                    // 修改的数量，小于已领用的数量
                    throw new BusinessException(PromotionResultCodeEnum.COUPON_MAX_RECEIVE_LESS_THAN_RECEIVE.getCode(),
                            PromotionResultCodeEnum.COUPON_MAX_RECEIVE_LESS_THAN_RECEIVE.getMsg());
                }
                if (saveBo.getPerMax() > 0) {
                    // 获取couponRecordList中通过userId分组后的最大一组的数量
                    Map<Long, List<CouponRecord>> collect = couponRecordList.stream().collect(Collectors.groupingBy(CouponRecord::getUserId));
                    int maxSize = collect.values().stream().max(Comparator.comparingInt(List::size)).get().size();
                    if (saveBo.getPerMax() < maxSize) {
                        // 修改的数量，小于已领用的数量
                        throw new BusinessException(PromotionResultCodeEnum.COUPON_MAX_RECEIVE_PER_LESS_THAN_RECEIVE.getCode(),
                                PromotionResultCodeEnum.COUPON_MAX_RECEIVE_PER_LESS_THAN_RECEIVE.getMsg());
                    }
                }
            }

        }

        BeanUtils.copyProperties(saveBo, saveCoupon, "id");

        Integer useArea = saveCoupon.getUseArea();
        if (UseAreaEnum.ALL.getCode().equals(useArea)) {
            // 如果是全场通用，则不需要保存商品信息
            saveBo.setProductIdList(Collections.emptyList());
        }

        // 如果选择了商品，需要去检查一下商品是否上架
        productAssistForPromotion.checkOnSaleStatus(saveBo.getProductIdList());

        TransactionHelper.doInTransaction(() -> {
            // 保存优惠券主体对象
            couponRepository.saveOrUpdate(saveCoupon);

            /*
             * 保存优惠券和商品的关系
             */
            List<CouponProduct> oldCouponProductList = opCouponProducts(saveBo, saveCoupon);
            if (CollUtil.isNotEmpty(oldCouponProductList)) {
                couponProductRepository.saveOrUpdateBatch(oldCouponProductList);
            }

            /*
             * 保存优惠券显示设置
             */
            if (null != saveBo.getId()) {
                // 更新的话，先删除显示设置(数据不多，直接删除处理)
                couponSettingRepository.deleteByCouponId(saveCoupon.getId());
            }
            List<Integer> platForm = saveBo.getPlatForm();
            if (CollectionUtils.isNotEmpty(platForm)) {
                for (Integer platform : platForm) {
                    CouponSetting couponSetting = new CouponSetting();
                    couponSetting.setCouponId(saveCoupon.getId());
                    couponSetting.setPlatForm(platform);
                    couponSettingRepository.save(couponSetting);
                }
            }

            // 记录操作日志
            promotionLogAssist.recordCouponLog(saveBo.getOperationUserId(), saveBo.getOperationShopId(),
                    SaveCouponLogBo.build(oldCoupon), SaveCouponLogBo.build(saveBo));
        });

    }

    @NotNull
    private List<CouponProduct> opCouponProducts(CouponSaveBo saveBo, Coupon saveCoupon) {
        List<CouponProduct> oldCouponProductList = new ArrayList<>();
        if (null != saveBo.getId()) {
            oldCouponProductList = couponProductRepository.listByCouponId(saveCoupon.getId());
        }
        List<Long> productIdList = saveBo.getProductIdList();
        if (null == productIdList) {
            productIdList = new ArrayList<>();
        }

        int oldProductSize = oldCouponProductList.size();
        int newProductSize = productIdList.size();
        int commonSize = Math.min(oldProductSize, newProductSize);

        // 共享部分的更新逻辑（数量重叠的部分）
        for (int i = 0; i < commonSize; i++) {
            Long productId = productIdList.get(i);
            CouponProduct couponProduct = oldCouponProductList.get(i);
            couponProduct.setProductId(productId);
            couponProduct.setCouponId(saveCoupon.getId());
        }

        if (oldProductSize > newProductSize) {
            // 删除多余的数据
            for (int i = newProductSize; i < oldProductSize; i++) {
                oldCouponProductList.get(i).setDelFlag(Boolean.TRUE);
            }
        }
        else if (oldProductSize < newProductSize) {
            // 新增数据
            for (int i = oldProductSize; i < newProductSize; i++) {
                Long productId = productIdList.get(i);
                CouponProduct newCouponProduct = new CouponProduct();
                newCouponProduct.setProductId(productId);
                newCouponProduct.setCouponId(saveCoupon.getId());
                oldCouponProductList.add(newCouponProduct);
            }
        }

        return oldCouponProductList;
    }

    @Override
    public void endActive(BaseIdReq baseIdReq) {
        Coupon coupon = couponRepository.findById(baseIdReq.getId());
        if (null == coupon) {
            throw new BusinessException(PromotionResultCodeEnum.COUPON_ACTIVE_NOT_FIND.getCode(),
                    PromotionResultCodeEnum.COUPON_ACTIVE_NOT_FIND.getMsg());
        }
        Date now = new Date();
        Date endTime = coupon.getEndTime();
        if (endTime.before(now)) {
            throw new BusinessException(PromotionResultCodeEnum.COUPON_ACTIVE_END.getCode(),
                    PromotionResultCodeEnum.COUPON_ACTIVE_END.getMsg());
        }
        coupon.setEndTime(new Date());
        coupon.setStatus(ActiveStatusEnum.END.getCode());
        couponRepository.saveOrUpdate(coupon);

        // 记录操作日志
        promotionLogAssist.recordEndCouponLog(baseIdReq);
    }

    @Override
    public BasePageResp<CouponSimpleResp> pageList(CouponQueryReq couponQueryReq) {
        BasePageResp<CouponSimpleDto> couponListDtoBasePageResp = couponRepository.pageList(couponQueryReq.buildPage(),
                JsonUtil.copy(couponQueryReq, CouponParamDto.class));
        couponListDtoBasePageResp.getData().parallelStream().forEach(
                data -> {
                    if (data.getUseArea().equals(UseAreaEnum.ALL.getCode())) {
                        data.setProductCount(PromotionConstant.ALL_PRODUCT);
                    }
                    else {
                        CouponProductParamDto paramDto = CouponProductParamDto.builder()
                                .couponId(data.getId()).build();
                        long count = couponProductRepository.countByCouponId(paramDto);
                        data.setProductCount(count);
                    }
                    List<CouponRecord> couponRecordList = couponRecordRepository.listByCouponId(data.getId());
                    if (CollectionUtils.isNotEmpty(couponRecordList)) {
                        // 领用张数
                        data.setReceiveNum(couponRecordList.size());
                        // 已使用数量
                        long count = couponRecordList.stream().filter(cr -> cr.getCouponStatus().equals(CouponStatusEnum.USED.getCode())).count();
                        data.setUseCount((int) count);
                        // 领用人数
                        long receiveCount = couponRecordList.stream().map(CouponRecord::getUserId).distinct().count();
                        data.setReceiveCount((int) receiveCount);
                    }
                    // 设置状态
                    ActiveBaseDto.opStatus(data);
                }
        );
        return PageResultHelper.transfer(couponListDtoBasePageResp, CouponSimpleResp.class);
    }

    @Override
    public Boolean flagHasCoupons(Long shopId) {
        CouponQueryReq couponQueryReq = new CouponQueryReq();
        couponQueryReq.setShopId(shopId);
        couponQueryReq.setStatus(1);
        CouponParamDto param = JsonUtil.copy(couponQueryReq, CouponParamDto.class);
        param.setHasStock(true);
        BasePageResp<CouponSimpleDto> couponListDtoBasePageResp = couponRepository.pageList(couponQueryReq.buildPage(), param);
        if (couponListDtoBasePageResp.getTotalCount() > 0L) {
            return true;
        }
        return false;
    }

    @Override
    public CouponResp getById(Long id) {

        Coupon coupon = couponRepository.findById(id);
        if (null == coupon) {
            throw new BusinessException(PromotionResultCodeEnum.COUPON_ACTIVE_NOT_FIND.getCode(),
                    PromotionResultCodeEnum.COUPON_ACTIVE_NOT_FIND.getMsg());
        }
        CouponResp couponResp = JsonUtil.copy(coupon, CouponResp.class);
        couponResp.setProductIdList(Collections.emptyList());
        if (couponResp.getUseArea().equals(UseAreaEnum.PRODUCT.getCode())) {
            List<CouponProduct> couponProducts = couponProductRepository.listByCouponId(coupon.getId());
            if (CollectionUtils.isNotEmpty(couponProducts)) {
                couponResp.setProductIdList(couponProducts.stream().map(CouponProduct::getProductId).collect(Collectors.toList()));
            }
            List<Long> productIdList = couponResp.getProductIdList();
            List<List<Long>> productSplitList = CollUtil.split(productIdList, PromotionConstant.MAX_LIKE_QUERY);
            List<ProductPageResp> productPageRespList = new ArrayList<>();
            for (List<Long> productIds : productSplitList) {
                QueryProductByIdReq queryProductByIdReq = new QueryProductByIdReq();
                queryProductByIdReq.setProductIds(productIds);
                ProductListResp productListResp = productRemoteService.queryProductById(queryProductByIdReq);
                if (null != productListResp && CollUtil.isNotEmpty(productListResp.getProductList())) {
                    productPageRespList.addAll(productListResp.getProductList());
                }
            }
            couponResp.setProductList(initCouponProduct(productPageRespList, productIdList));
        }
        couponResp.setPlatForm(Collections.emptyList());
        if (couponResp.getReceiveType().equals(ReceiveTypeEnum.SHOP_INDEX.getCode())) {
            CouponSettingParamDto paramDto = CouponSettingParamDto.builder()
                    .couponId(coupon.getId()).build();
            List<CouponSetting> couponSettings = couponSettingRepository.listByExample(paramDto);
            if (CollectionUtils.isNotEmpty(couponSettings)) {
                couponResp.setPlatForm(couponSettings.stream().map(CouponSetting::getPlatForm).collect(Collectors.toList()));
            }
        }

        return couponResp;
    }

    private List<CouponProductDto> initCouponProduct(List<ProductPageResp> productList, List<Long> productIdList) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return Collections.emptyList();
        }

        Map<Long, ProductPageResp> productMap = Optional.ofNullable(productList).orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(ProductPageResp::getProductId, Function.identity(), (k1, k2) -> k1));
        List<CouponProductDto> couponProductList = new ArrayList<>();
        productIdList.forEach(productId -> {
            ProductPageResp product = productMap.get(productId);
            if (product != null) {
                couponProductList.add(JsonUtil.copy(product, CouponProductDto.class));
                return;
            }

            CouponProductDto couponProductDto = new CouponProductDto();
            couponProductDto.setProductId(productId);
            couponProductDto.setProductName(PRODUCT_DELETED);
            couponProductList.add(couponProductDto);
        });
        return couponProductList;
    }

    @Override
    public void receiveCoupon(CouponReceiveBo receiveBo) {
        if (couponReceiveSwitch == null || !couponReceiveSwitch) {
            throw new BusinessException(PromotionResultCodeEnum.RECEIVE_COUPON_ACTIVITY_CLOSED.getCode(),
                    PromotionResultCodeEnum.RECEIVE_COUPON_ACTIVITY_CLOSED.getMsg());
        }

        Coupon coupon = couponRepository.findById(receiveBo.getCouponId());
        if (null == coupon) {
            throw new BusinessException(PromotionResultCodeEnum.COUPON_ACTIVE_NOT_FIND.getCode(),
                    PromotionResultCodeEnum.COUPON_ACTIVE_NOT_FIND.getMsg());
        }
        Date now = new Date();
        // 校验优惠券是否在有效期内, 只有已经结束的不能领取，未开始的也可以领
        if (now.after(coupon.getEndTime())) {
            throw new BusinessException(PromotionResultCodeEnum.COUPON_ACTIVE_END.getCode(),
                    PromotionResultCodeEnum.COUPON_ACTIVE_END.getMsg());
        }
        /*if (now.before(coupon.getStartTime()) || now.after(coupon.getEndTime()) || coupon.getStartTime().after(coupon.getEndTime())) {
            throw new BusinessException(PromotionResultCodeEnum.COUPON_ACTIVE_NOT_FIND.getCode(),
                    PromotionResultCodeEnum.COUPON_ACTIVE_NOT_FIND.getMsg());
        }*/

        // 判断是否有最大领取限制
        Integer perMax = coupon.getPerMax();
        if (perMax > 0) {
            // 大于0，说明有领取限制
            CouponRecordParamDto paramDto = CouponRecordParamDto.builder()
                    .couponId(coupon.getId()).userId(receiveBo.getUserId()).build();
            long count = couponRecordRepository.countByCouponAndUserId(paramDto);
            if (count >= perMax) {
                throw new BusinessException(PromotionResultCodeEnum.COUPON_RECEIVE_MAX.getCode(),
                        PromotionResultCodeEnum.COUPON_RECEIVE_MAX.getMsg());
            }
        }

        // 判断优惠券是否还有剩余
        Integer num = coupon.getNum();
        CouponRecordParamDto paramDto = CouponRecordParamDto.builder()
                .couponId(coupon.getId()).build();
        long count = couponRecordRepository.countByCouponAndUserId(paramDto);
        if (count >= num) {
            throw new BusinessException(PromotionResultCodeEnum.COUPON_RECEIVE_EMPTY.getCode(),
                    PromotionResultCodeEnum.COUPON_RECEIVE_EMPTY.getMsg());
        }

        // 保存领取记录
        CouponRecord couponRecord = new CouponRecord();
        couponRecord.setCouponId(coupon.getId());
        couponRecord.setCouponSn(PromotionConstant.COUPON_SN_PREFIX + leafService.generateNoBySnowFlake(LeafConstant.COUPON_SN_LEAF_KEY));
        couponRecord.setUserId(receiveBo.getUserId());
        couponRecord.setUserName(receiveBo.getUserName());
        couponRecord.setCouponStatus(CouponStatusEnum.NOT_USED.getCode());
        couponRecord.setShopId(receiveBo.getShopId());
        couponRecord.setShopName(receiveBo.getShopName());
        couponRecord.setCouponTime(now);

        TransactionHelper.doInTransaction(() -> {
            couponRecordRepository.save(couponRecord);
            // 扣减优惠券数量
            int update = couponRepository.reduceStock(coupon.getId());
            if (update <= 0) {
                // 修改失败，说明优惠券已经抢光了
                throw new BusinessException(PromotionResultCodeEnum.COUPON_RECEIVE_EMPTY.getCode(),
                        PromotionResultCodeEnum.COUPON_RECEIVE_EMPTY.getMsg());
            }
        });
    }

    @Override
    public Integer queryAvailableCouponCountByUser(Long userId) {
        return couponRecordRepository.queryAvailableCouponCountByUser(userId);
    }

    @Override
    public void sendCoupon(SendCouponCmdReq request) {
        // 去重couponIds
        List<Long> couponIds = request.getCouponIds().stream().distinct().collect(Collectors.toList());
        List<Coupon> couponList = couponRepository.queryEffectiveByIdList(couponIds);
        if (CollUtil.isEmpty(couponList) || couponList.size() != couponIds.size()) {
            throw new BusinessException(PromotionResultCodeEnum.COUPON_PART_NOT_EXIST.getCode(),
                    PromotionResultCodeEnum.COUPON_PART_NOT_EXIST.getMsg());
        }

        // 去重memberIds
        List<Long> memberIds = request.getMemberIds().stream().distinct().collect(Collectors.toList());
        int memberSize = memberIds.size();

        //获取消息id
        Long msgId = request.getMsgId();

        // 如果存在发行数量小于领取数量+已领取数量的优惠券，则抛出异常
        List<Coupon> notEnoughCouponList = couponList.stream().filter(coupon -> coupon.getNum() < memberSize + coupon.getReceiveNum()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(notEnoughCouponList)) {
            log.info("sendCoupon-notEnoughCouponList:{}", notEnoughCouponList);
            throw new BusinessException(PromotionResultCodeEnum.COUPON_PART_NOT_ENOUGH.getCode(),
                    PromotionResultCodeEnum.COUPON_PART_NOT_ENOUGH.getMsg());
        }

        final Map<Long, String> memberIdNameMap = new HashMap<>(memberSize);

        QueryMemberListReq queryMemberListReq = new QueryMemberListReq();
        queryMemberListReq.setMemberIds(memberIds);
        MemberListResp memberListResp = memberRemoteService.queryMemberList(queryMemberListReq);
        if (null != memberListResp && CollectionUtils.isNotEmpty(memberListResp.getMemberRespList())) {
            List<MemberResp> memberRespList = memberListResp.getMemberRespList();
            memberRespList.stream().forEach(
                    memberResp -> {
                        memberIdNameMap.put(memberResp.getId(), memberResp.getUserName());
                    }
            );
        }

        List<Long> snowFlakeList = leafService.batchGenerateNoBySnowFlake(LeafConstant.COUPON_SN_LEAF_KEY, memberSize);

        TransactionHelper.doInTransaction(() -> {
            couponList.parallelStream().forEach(
                    coupon -> {
                        int updateCount = couponRepository.receiveCoupon(coupon.getId(), memberSize);
                        if (updateCount <= 0) {
                            log.info("sendCoupon-coupon:{},size:{}", coupon, memberSize);
                            throw new BusinessException(PromotionResultCodeEnum.COUPON_PART_NOT_ENOUGH.getCode(),
                                    PromotionResultCodeEnum.COUPON_PART_NOT_ENOUGH.getMsg());
                        }

                        // 循环 memberIds 生成优惠券记录
                        List<CouponRecord> couponRecordList = new ArrayList<>(memberSize);
                        // 循环生成消息
                        List<SendMessageRecordCouponSn> sendMessageRecordCouponSns = new ArrayList<>(memberSize);
                        int index = 0;
                        for (Long memberId : memberIds) {
                            MemberResp member = memberRemoteService.queryMember(memberId);
                            if (member == null) {
                                throw new BusinessException("用户不存在");
                            }

                            // 判断优惠券是否有领取限制
                            if (coupon.getPerMax() > 0) {
                                CouponRecordParamDto recordParam = CouponRecordParamDto.builder().couponId(coupon.getId()).userId(memberId).build();
                                long count = couponRecordRepository.countByCouponAndUserId(recordParam);
                                if (count >= coupon.getPerMax()) {

                                    throw new BusinessException(PromotionResultCodeEnum.COUPON_OVER_MEMBER_MAX_RECEIVED_NUM.getCode(),
                                            String.format("用户【%s】已达到【%s】优惠券的最大领取数量", member.getUserName(), coupon.getCouponName()));
                                }
                            }

                            CouponRecord couponRecord = new CouponRecord();
                            couponRecord.setCouponId(coupon.getId());
                            couponRecord.setCouponSn(PromotionConstant.COUPON_SN_PREFIX + snowFlakeList.get(index));
                            couponRecord.setUserId(memberId);
                            couponRecord.setUserName(memberIdNameMap.get(memberId));
                            couponRecord.setCouponStatus(CouponStatusEnum.NOT_USED.getCode());
                            couponRecord.setShopId(coupon.getShopId());
                            couponRecord.setShopName(coupon.getShopName());
                            couponRecord.setCouponTime(new Date());
                            couponRecordList.add(couponRecord);
                            //加入消息记录
                            SendMessageRecordCouponSn recordCouponSn = new SendMessageRecordCouponSn();
                            recordCouponSn.setMessageId(msgId);
                            recordCouponSn.setCouponSn(couponRecord.getCouponSn());
                            sendMessageRecordCouponSns.add(recordCouponSn);
                            index++;
                        }
                        couponRecordRepository.saveBatch(couponRecordList);
                        //加入消息记录
                        sendMessageRecordCouponSnRepository.saveBatch(sendMessageRecordCouponSns);
                        SendMessageRecordCoupon recordCoupon = new SendMessageRecordCoupon();
                        recordCoupon.setMessageId(msgId);
                        recordCoupon.setCouponId(coupon.getId());
                        sendMessageRecordCouponRepository.save(recordCoupon);
                    }
            );

            // 记录操作日志
//            promotionLogAssist.recordSendCouponLog(request.getOperationUserId(), request.getOperationShopId(), request);
        });
    }

    @Override
    public CouponSimpleListResp queryByProductId(ProductAndShopIdReq request) {
        CouponSimpleListResp couponSimpleListResp = new CouponSimpleListResp();
        List<Coupon> coupons = couponRepository.selectByProductId(request.getProductId(), request.getShopId());
        if (CollectionUtils.isEmpty(coupons)) {
            return couponSimpleListResp;
        }
        List<CouponSimpleResp> couponList = JsonUtil.copyList(coupons, CouponSimpleResp.class);
        couponSimpleListResp.setCouponList(couponList);

        // 计算领取状态
        initReceived(couponList, request.getUserId());
        return couponSimpleListResp;
    }

    private void initReceived(List<CouponSimpleResp> couponList, Long userId) {
        if (CollectionUtils.isEmpty(couponList) || ObjectUtils.isEmpty(userId)) {
            return;
        }

        List<Long> couponIds = couponList.stream().map(CouponSimpleResp::getId).distinct().collect(Collectors.toList());
        List<CouponRecord> records = couponRecordRepository.listByCouponIdsAndUserId(couponIds, userId);
        // 根据优惠券id分组, 计算当前用户领取的张数
        Map<Long, List<CouponRecord>> recordMap = records.stream()
                .filter(record -> CouponStatusEnum.NOT_USED.getCode().equals(record.getCouponStatus()))
                .collect(Collectors.groupingBy(CouponRecord::getCouponId));
        couponList.forEach(coupon -> {
            List<CouponRecord> couponRecords = recordMap.get(coupon.getId());
            coupon.setUserReceivedNum(CollectionUtils.isEmpty(couponRecords) ? 0 : couponRecords.size());
            coupon.setUserReceived(CollectionUtils.isNotEmpty(couponRecords));
            Long usedUum = Optional.ofNullable(couponRecords).orElse(Collections.emptyList())
                    .stream().filter(cr -> cr.getCouponStatus().equals(CouponStatusEnum.USED.getCode())).count();
            coupon.setUserUsedUum(usedUum.intValue());
        });
    }

    @Override
    public CouponSimpleListResp getByIdList(BaseBatchIdReq request) {
        //判空
        if (ObjectUtils.isEmpty(request.getId())) {
            return new CouponSimpleListResp();
        }
        List<Coupon> couponList = couponRepository.lambdaQuery().in(Coupon::getId, request.getId()).list();
        if (CollectionUtils.isEmpty(couponList)) {
            return new CouponSimpleListResp();
        }
        List<CouponSimpleResp> couponSimpleRespList = JsonUtil.copyList(couponList, CouponSimpleResp.class);
        CouponSimpleListResp couponSimpleListResp = new CouponSimpleListResp();
        couponSimpleListResp.setCouponList(couponSimpleRespList);
        return couponSimpleListResp;
    }

    //    计算使用率
    @Override
    public String calculateUsageRate(BaseIdReq msgId) {
        Double usageRate = sendMessageRecordCouponSnRepository.calculateUsageRate(msgId.getId());
        if (usageRate == null) {
            return NumberUtil.roundStr(new Double(0), 2);
        }
        //转化为百分数
        return NumberUtil.roundStr(usageRate, 2);
    }

    @Override
    public CouponSimpleListResp getByMessageId(BaseIdReq msgId) {
        List<SendMessageRecordCoupon> usageRate = sendMessageRecordCouponRepository.getByMessageId(msgId.getId());
        //获取所有优惠券id
        List<Long> couponIdList = usageRate.stream().map(SendMessageRecordCoupon::getCouponId).collect(Collectors.toList());
        BaseBatchIdReq baseBatchIdReq = new BaseBatchIdReq();
        baseBatchIdReq.setId(couponIdList);
        return getByIdList(baseBatchIdReq);
    }

    @Override
    public BasePageResp<CouponProductDto> queryCouponProductPage(CouponProductQueryReq req) {
        Coupon coupon = couponRepository.findById(req.getCouponId());
        if (null == coupon) {
            throw new BusinessException(PromotionResultCodeEnum.COUPON_ACTIVE_NOT_FIND.getCode(),
                    PromotionResultCodeEnum.COUPON_ACTIVE_NOT_FIND.getMsg());
        }

        // 如果是全场通用 则不返回
        if (coupon.getUseArea().equals(UseAreaEnum.ALL.getCode())) {
            return PageResultHelper.defaultEmpty(req);
        }
        Page<CouponProduct> couponProductPage = PageHelper.startPage(req.getPageNo(), req.getPageSize());
        couponProductRepository.listByCouponId(coupon.getId());

        List<Long> productIdList = couponProductPage.getResult().stream().map(CouponProduct::getProductId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productIdList)) {
            return PageResultHelper.defaultEmpty(req);
        }

        List<List<Long>> productSplitList = CollUtil.split(productIdList, PromotionConstant.MAX_LIKE_QUERY);
        List<ProductPageResp> productPageRespList = new ArrayList<>();
        for (List<Long> productIds : productSplitList) {
            QueryProductByIdReq queryProductByIdReq = new QueryProductByIdReq();
            queryProductByIdReq.setProductIds(productIds);
            ProductListResp productListResp = productRemoteService.queryProductById(queryProductByIdReq);
            if (null != productListResp && CollUtil.isNotEmpty(productListResp.getProductList())) {
                productPageRespList.addAll(productListResp.getProductList());
            }
        }
        Map<Long, ProductPageResp> productMap = productPageRespList.stream()
                .collect(Collectors.toMap(ProductPageResp::getProductId, Function.identity(), (k1, k2) -> k1));

        return PageResultHelper.transfer(couponProductPage, CouponProductDto.class, (source, target) -> {
            ProductPageResp product = productMap.get(source.getProductId());
            if (null != product) {
                BeanUtils.copyProperties(product, target);
                return;
            }
            target.setProductName(PRODUCT_DELETED);
        });
    }

    @Override
    public ProductAvailableQueryResp getProductAvailableCouponList(ProductAvailableQueryReq request) {
        Map<Long, List<CouponSimpleResp>> productCouponMap = new HashMap<>(request.getProductMap().size());
        Long userId = request.getUserId();

        // 查询所有进行中的优惠券
        List<Long> shopIds = new ArrayList<>(request.getProductMap().keySet());
        List<Long> allProductIds = request.getProductMap().values().stream().flatMap(Collection::stream).distinct().collect(Collectors.toList());
        List<CouponExtDto> allCouponList = couponRepository.selectAvailableList(shopIds, allProductIds);

        List<CouponSimpleResp> couponList = JsonUtil.copyList(allCouponList, CouponSimpleResp.class);
        // 计算优惠券的领取状态
        initReceived(couponList, userId);

        // 1.提取出用户未使用的优惠券
        List<CouponSimpleResp> unusedCouponList = couponList.stream()
                .filter(coupon -> coupon.getUserReceivedNum() > coupon.getUserUsedUum())
                .collect(Collectors.toList());

        // 2.提取出还可以领取优惠券
        List<CouponSimpleResp> availableCouponList = couponList.stream()
                .filter(coupon -> coupon.getNum() > coupon.getReceiveNum()
                        && (coupon.getPerMax() == 0 || coupon.getPerMax() > coupon.getUserReceivedNum())
                        && coupon.getReceiveType() == 0)
                .collect(Collectors.toList());
        // 将 1.2 优惠券列表合并
        unusedCouponList.addAll(availableCouponList);

        // 先查询出所有可用的优惠券信息
        Map<Long, List<Long>> productMap = request.getProductMap();
        productMap.forEach((shopId, productIdList) -> {
            if (CollUtil.isEmpty(productIdList)) {
                return;
            }

            for (Long productId : productIdList) {
                List<CouponSimpleResp> productCouponList = unusedCouponList.stream()
                        .filter(coupon -> {
                                    // 如果不是当前店铺的优惠券 直接跳过
                                    if (!coupon.getShopId().equals(shopId)) {
                                        return false;
                                    }

                                    // 满足商品的使用条件
                                    return UseAreaEnum.ALL.getCode().equals(coupon.getUseArea())
                                            || (UseAreaEnum.PRODUCT.getCode().equals(coupon.getUseArea()) && productId.equals(coupon.getProductId()));
                                }
                        ).collect(Collectors.toList());
                productCouponMap.put(productId, productCouponList);
            }
        });
        ProductAvailableQueryResp productAvailableQueryResp = new ProductAvailableQueryResp();
        productAvailableQueryResp.setProductCouponMap(productCouponMap);
        return productAvailableQueryResp;
    }

    @Override
    public List<Long> queryRelateProductIds(Long couponId) {
        Coupon coupon = couponRepository.findById(couponId);
        if (null == coupon || coupon.getUseArea().equals(UseAreaEnum.ALL.getCode())) {
            return Collections.emptyList();
        }

        // 如果是全场通用 则不返回
        List<CouponProduct> couponProducts = couponProductRepository.listByCouponId(couponId);
        return couponProducts.stream().map(CouponProduct::getProductId).collect(Collectors.toList());
    }

    /**
     * 匹配符合条件的优惠券
     *
     * @param couponList
     * @param shopId
     * @param productIds
     * @return
     */
    private List<CouponExtDto> matchCouponList(List<CouponExtDto> couponList, Long shopId, List<Long> productIds) {
        if (CollectionUtils.isEmpty(couponList)) {
            return Collections.emptyList();
        }

        return couponList.stream().filter(coupon ->
                        (coupon.getShopId().equals(shopId) && coupon.getUseArea().equals(UseAreaEnum.ALL.getCode()))
                                || (CollectionUtils.isNotEmpty(productIds) && productIds.contains(coupon.getProductId())))
                .collect(Collectors.toList());
    }

}
