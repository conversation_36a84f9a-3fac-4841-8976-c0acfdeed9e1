package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.submit;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductDescriptionAudit;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductDescriptionAuditRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 提交商品详情审核
 *
 * <AUTHOR>
 * @date 2023/11/17 13:40
 */
@Component
@Slf4j
public class SubmitProductDescriptionAuditHandler extends AbsSubmitProductAuditHandler {

    @Resource
    private ProductDescriptionAuditRepository productDescriptionAuditRepository;

    @Override
    protected void handle(ProductContext context) {
        Long productId = context.getProductId();
        ProductBo auditProductBo = context.getAuditProductBo();

        log.info("【商品提交审核】保存商品详情审核记录【start】, productId: {}", productId);

        ProductDescriptionAudit productAudit = JsonUtil.copy(auditProductBo, ProductDescriptionAudit.class);
        productAudit.setId(productAudit.getProductId());
        productDescriptionAuditRepository.save(productAudit);

        log.info("【商品提交审核】保存商品详情审核记录【end】, productId: {}", productId);
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.SUBMIT_PRODUCT_DESCRIPTION_AUDIT;
    }


}
