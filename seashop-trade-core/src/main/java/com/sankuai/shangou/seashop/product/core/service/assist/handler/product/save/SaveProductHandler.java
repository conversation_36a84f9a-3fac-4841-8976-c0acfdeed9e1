package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.save;

import java.util.Date;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductFieldHelper;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.converter.ProductConverter;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductDescription;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductDescriptionRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRepository;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.extern.slf4j.Slf4j;

/**
 * 保存商品信息处理器
 *
 * <AUTHOR>
 * @date 2023/11/15 18:28
 */
@Component
@Slf4j
public class SaveProductHandler extends AbsSaveProductHandler {

    @Resource
    private ProductRepository productRepository;
    @Resource
    private ProductDescriptionRepository productDescriptionRepository;

    /**
     * 创建商品
     *
     * @param context 保存商品上下文对象
     */
    @Override
    public void create(ProductContext context) {
        ProductBo saveProductBo = context.getSaveProductBo();
        // 首次保存将商品标记为新商品
        Product product = ProductConverter.convertToEntity(saveProductBo, context.getShopId(), context.isDraftFlag());
        productRepository.save(product);

        ProductDescription productDescription = JsonUtil.copy(saveProductBo, ProductDescription.class);
        productDescription.setId(productDescription.getProductId());
        productDescriptionRepository.save(productDescription);
        context.setNeedAudit(!context.isDraftFlag());
    }

    /**
     * 执行更新方法前置逻辑
     *
     * @param context 保存商品上下文对象
     */
    @Override
    public void beforeUpdate(ProductContext context) {
        ProductBo saveProductBo = context.getSaveProductBo();
        ProductBo oldProductBo = context.getOldProductBo();
        Long productId = context.getProductId();

        // 如果是编辑 对比新旧商品数据 提取需要审核的字段和不需要审核的字段 部分更新的场景忽略null值
        Map<Boolean, Map<String, Object>> fieldMap = ProductFieldHelper.getFieldMap(saveProductBo, oldProductBo, context.isPartSave());
        context.setProductCompareMap(fieldMap);
        log.info("【保存商品】对比新旧商品数据, 商品id: {}, 提取字段结果: {}", productId, fieldMap);
    }

    /**
     * 更新商品
     *
     * @param context 保存商品上下文对象
     */
    @Override
    public void update(ProductContext context) {
        Long productId = context.getProductId();
        ProductBo oldProductBo = context.getOldProductBo();
        Map<Boolean, Map<String, Object>> fieldCompareMap = context.getProductCompareMap();
        Map<String, Object> auditFieldMap = fieldCompareMap.get(Boolean.TRUE);
        Map<String, Object> noAuditFieldMap = fieldCompareMap.get(Boolean.FALSE);

        // 将不需要审核的字段直接更新到数据库 如果保存之前是草稿箱状态 则需要将状态改为上架
        Product product = JsonUtil.copy(noAuditFieldMap, Product.class);
        product.setProductId(productId);
        if (ProductEnum.SaleStatusEnum.DRAFT.getCode().equals(oldProductBo.getSaleStatus())) {
            product.setSaleStatus(ProductEnum.SaleStatusEnum.ON_SALE.getCode());
            context.setNeedAudit(Boolean.TRUE);
        }
        product.setUpdateTime(new Date());
        productRepository.updateByProductId(product);

        // 修改了需要审核的字段
        if (!auditFieldMap.isEmpty()) {
            context.setNeedAudit(Boolean.TRUE);
        }

        // 如果旧商品状态为违规下架 则肯定需要重新审批
        if (ProductEnum.AuditStatusEnum.VIOLATION.getCode().equals(oldProductBo.getAuditStatus())) {
            context.setNeedAudit(Boolean.TRUE);
        }
    }

    /**
     * 保存到草稿
     *
     * @param context 保存商品上下文对象
     */
    @Override
    public void updateDraft(ProductContext context) {
        Long productId = context.getProductId();

        Product updProduct = JsonUtil.copy(context.getSaveProductBo(), Product.class);
        updProduct.setProductId(productId);
        updProduct.setUpdateTime(new Date());
        productRepository.updateByProductId(updProduct);

        ProductDescription updProductDescription = JsonUtil.copy(context.getSaveProductBo(), ProductDescription.class);
        updProductDescription.setProductId(productId);
        productDescriptionRepository.updateByProductId(updProductDescription);

        context.setNeedAudit(Boolean.FALSE);
    }

    /**
     * 执行更新商品后置逻辑(对于部分更新方法, 需要填充旧的商品数据, 用于审核)
     *
     * @param context 保存商品上下文对象
     */
    @Override
    public void postUpdate(ProductContext context) {
        if (!context.isPartSave() || context.isDraftFlag()) {
            return;
        }

        // 赋值审核数据
        ProductBo saveProductBo = context.getSaveProductBo();
        ProductBo oldProductBo = context.getOldProductBo();
        ProductBo auditProductBo = context.getAuditProductBo();
        BeanUtil.copyProperties(oldProductBo, auditProductBo);
        BeanUtil.copyProperties(saveProductBo, auditProductBo, CopyOptions.create().setIgnoreNullValue(true));
    }

    @Override
    public String getHandlerName() {
        return "保存商品主表";
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.SAVE_PRODUCT;
    }
}
