package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.check;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * 商品检查处理器
 *
 * <AUTHOR>
 * @date 2023/11/15 19:41
 */
@Component
@Slf4j
public class CheckProductHandler extends AbsProductHandler {

    @Resource
    private ProductAssist productAssist;

    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.CHECK_SAVE_PRODUCT);
    }

    @Override
    protected void handle(ProductContext context) {
        log.info("【保存商品】处理商品【start】, context:{}", context);

        ProductBo saveProductBo = context.getSaveProductBo();

        // 如果是编辑 需要检验一下是不是店铺的商品
        if (context.isEditFlag()) {
            Product product = productAssist.checkProductAuth(saveProductBo.getProductId(), context.getShopId(), Boolean.TRUE);

            Integer saleStatus = product.getSaleStatus();
            AssertUtil.throwIfTrue(context.isDraftFlag() && !ProductEnum.SaleStatusEnum.DRAFT.getCode().equals(saleStatus),
                    "非草稿箱商品不允许保存为草稿");
            context.setDbProduct(product);
        }

        // 货号在同一店铺下不允许重复 判断商品主表货号不重复
        productAssist.checkProductCode(context.getShopId(), saveProductBo.getProductCode(), saveProductBo.getProductId());
        // 设置默认图片
        setDefaultImage(context);

        ProductBo productBo = context.getSaveProductBo();
        // 计算最小销售价
        productBo.setMinSalePrice(productAssist.getMinSalePrice(productBo));
        // 设置商品主图 取图片第一张
        productBo.setImagePath(getImagePath(productBo.getImageList()));
        // 设置商品id
        productBo.setProductId(context.getProductId());

        log.info("【保存商品】处理商品【end】, context:{}", context);
    }

    private void setDefaultImage(ProductContext context) {
        if (context.isPartSave()) {
            return;
        }

        ProductBo productBo = context.getSaveProductBo();
        List<String> imageList = productBo.getImageList();
        if (CollectionUtils.isEmpty(imageList)) {
            productBo.setImageList(Arrays.asList(CommonConstant.DEFAULT_IMAGE));
        }
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.CHECK_PRODUCT;
    }

    /**
     * 提取第一张图片作为商品主图
     *
     * @param imageList 图片列表
     * @return 商品主图
     */
    private String getImagePath(List<String> imageList) {
        if (CollectionUtils.isEmpty(imageList)) {
            return null;
        }

        return imageList.get(0);
    }
}
