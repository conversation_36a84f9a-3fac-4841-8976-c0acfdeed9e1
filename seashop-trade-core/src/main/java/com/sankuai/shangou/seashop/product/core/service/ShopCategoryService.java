package com.sankuai.shangou.seashop.product.core.service;

import java.util.List;

import com.sankuai.shangou.seashop.product.core.service.model.ShopCategoryBo;
import com.sankuai.shangou.seashop.product.core.service.model.ShopCategoryQueryBo;
import com.sankuai.shangou.seashop.product.core.service.model.TransferProductBo;

/**
 * <AUTHOR>
 * @date 2023/11/13 11:14
 */
public interface ShopCategoryService {

    /**
     * 新增/编辑店铺分类
     *
     * @param shopCategoryBo 店铺分类参数
     */
    void saveShopCategory(ShopCategoryBo shopCategoryBo);

    /**
     * 删除店铺分类
     *
     * @param id     店铺分类id
     * @param shopId 店铺id
     */
    void deleteShopCategory(Long id, Long shopId);

    /**
     * 查询店铺分类
     *
     * @param shopCategoryQueryBo 查询参数
     * @return 店铺分类列表
     */
    List<ShopCategoryBo> queryShopCategory(ShopCategoryQueryBo shopCategoryQueryBo);

    /**
     * 异步转移商品(发送mq通知)
     *
     * @param transferProductBo 转移商品参数
     */
    void asyncTransferProduct(TransferProductBo transferProductBo);

    /**
     * 转移商品
     *
     * @param transferProductBo 转移商品参数
     */
    void transferProduct(TransferProductBo transferProductBo);

    /**
     * 获取下级店铺分类id列表
     *
     * @param shopCategoryId 店铺分类id
     */
    List<Long> getSubShopCategoryIds(Long shopCategoryId);
}
