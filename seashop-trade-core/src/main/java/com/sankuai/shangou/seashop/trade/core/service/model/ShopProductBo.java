package com.sankuai.shangou.seashop.trade.core.service.model;

import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import com.sankuai.shangou.seashop.trade.common.remote.model.product.RemoteLadderPriceBo;
import com.sankuai.shangou.seashop.trade.thrift.core.enums.ShoppingCartEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 购物车商品返回对象
 *
 * <AUTHOR>
 */
@ToString
@Getter
@Setter
public class ShopProductBo {

    /**
     * 购物车主键ID
     */
    private Long id;
    /**
     * 商家用户ID
     */
    private Long userId;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 商品主图
     */
    private String mainImagePath;
    /**
     * 商品SKUID
     */
    private String skuId;
    /**
     * sku库存=sku.safeStock
     */
    private Long skuStock;
    /**
     * 原售价=商城价
     */
    private BigDecimal originSalePrice;
    /**
     * 折扣售价
     */
    private BigDecimal discountSalePrice;
    /**
     * 实际售价，基于专享价和阶梯价得到
     */
    private BigDecimal realSalePrice;
    /**
     * 最终售价。如果没有折扣，则=realSalePrice，如果有折扣，则=discountSalePrice
     */
    private BigDecimal finalSalePrice;
    /**
     * 数量
     */
    private Long quantity;
    /**
     * 商品总金额，根据实际价格计算得到
     */
    private BigDecimal totalAmount;
    /**
     * 添加时间
     */
    private Date addTime;
    /**
     * 是否选中
     */
    private Boolean whetherSelected;
    /**
     * 规格描述
     */
    private List<String> skuNameList;
    /**
     * 起购量，根据倍数和阶梯价数量计算得到的最小购买数量
     */
    private Integer minBuyCount;
    /**
     * 限购数
     */
    private Integer maxBuyCount;
    /**
     * 倍数起购量
     */
    private Integer multipleCount;
    /**
     * 是否专享价
     */
    private Boolean whetherExclusive;
    /**
     * 运费模板ID
     */
    private Long freightTemplateId;
    /**
     * 重量
     */
    private BigDecimal weight;
    /**
     * 体积
     */
    private BigDecimal volume;
    /**
     * 阶梯价
     */
    private List<RemoteLadderPriceBo> ladderPriceList;
    /**
     * 购物车sku状态
     */
    private ShoppingCartEnum.SkuStatus skuStatus;
    /**
     * 商品销售状态
     * 1-销售中 2-仓库中 3-草稿箱
     */
    private ProductEnum.SaleStatusEnum saleStatus;
    /**
     * 商品审核状态
     * 1-待审核 2-销售中 3-未通过 4-违规下架
     */
    private ProductEnum.AuditStatusEnum auditStatus;
    /**
     * 商品是否已删除
     */
    private Boolean whetherDeleted;
    /**
     * 校验失败的错误提示
     */
    private String errorMsg;

    /**
     * 商品货号
     */
    private String productCode;
    /**
     * 规格：颜色，对应商品表的 spec1
     */
    private String color;
    /**
     * 规格：尺码，对应商品表的 spec2
     */
    private String size;
    /**
     * 规格：版本，对应商品表的 spec3
     */
    private String version;
    /**
     * sku自增主键ID
     */
    private Long skuAutoId;
    /**
     * sku编码
     */
    private String sku;
    /**
     * 商品分类ID
     */
    private Long categoryId;

    // 折扣活动ID，trade服务才会处理优惠，所以在这边处理分摊
    private Long discountActivityId;
    // 均摊后的满减金额
    private BigDecimal splitReductionAmount;
    // 均摊后的折扣金额，折扣的均摊在计算折扣的时候就处理了
    private BigDecimal splitDiscountAmount;
    // 均摊后的优惠券金额
    private BigDecimal splitCouponAmount;
    // 如果该商品需要均摊优惠券，即商品在优惠券适用范围内，则会赋值
    private Long couponId;
    // 满减活动ID
    private Long reductionActivityId;


    /**
     * 是否允许7天无理由退货
     */
    private Boolean enableNoReasonReturn;
    /**
     * 是否显示保障(保证金)标志
     */
    private Boolean showGuaranteeFlag;
    /**
     * 是否显示闪电标识
     */
    private Boolean showThunderFlag;

    /**
     * 计量单位
     */
    private String measureUnit;

    // 下面两个字段时额外的用于设置店铺信息的。是否开启专属和当前用户是否时专属商家
    private Boolean whetherShopOpenExclusiveMember;
    private Boolean whetherUserBelongExclusiveMember;


}
