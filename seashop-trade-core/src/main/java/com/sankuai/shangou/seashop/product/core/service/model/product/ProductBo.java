package com.sankuai.shangou.seashop.product.core.service.model.product;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductField;
import com.sankuai.shangou.seashop.product.core.service.assist.comparator.NullAsEmptyStringComparator;
import com.sankuai.shangou.seashop.product.thrift.core.enums.SkuUpdateKeyEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/14 19:21
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductBo extends BaseParamReq {

    /**
     * 商品id 新增时不填/填0
     */
    @ExaminField(description = "商品id")
    private Long productId;

    /**
     * 类目id
     */
    @ProductField(needAudit = true)
    @ExaminField(description = "类目id")
    private Long categoryId;

    /**
     * 分类名称
     */
    @ExaminField(description = "类目名称")
    private String categoryName;

    /**
     * 类目路径
     */
    private String categoryPath;

    /**
     * 品牌id
     */
    @ProductField(needAudit = true)
    @ExaminField(description = "品牌id")
    private Long brandId;

    /**
     * 品牌名称
     */
    @ExaminField(description = "品牌名称")
    private String brandName;

    /**
     * 商品名称(100个字)
     */
    @ProductField(needAudit = true, comparator = NullAsEmptyStringComparator.class)
    @ExaminField(description = "商品名称")
    private String productName;

    /**
     * 广告词
     */
    @ProductField(needAudit = true, comparator = NullAsEmptyStringComparator.class)
    @ExaminField(description = "广告词")
    private String shortDescription;

    /**
     * 是否开启阶梯价
     */
    @ProductField(needAudit = true)
    @ExaminField(description = "是否开启阶梯价")
    private Boolean whetherOpenLadder;

    /**
     * 阶梯价
     */
    @ExaminField(description = "阶梯价", isChildField = true)
    private List<ProductLadderPriceBo> ladderPriceList;

    /**
     * 最小商城价
     */
    @ExaminField(description = "商城价")
    private BigDecimal minSalePrice;

    /**
     * 市场价
     */
    @ProductField(needAudit = true, type = BigDecimal.class)
    @ExaminField(description = "市场价")
    private BigDecimal marketPrice;

    /**
     * 库存(多个规格的库存之和)
     */
    private Long stock;

    /**
     * 商品货号(100个字符)(同一店铺不能重复)
     */
    @ProductField(needAudit = false)
    @ExaminField(description = "商品货号")
    private String productCode;

    /**
     * 计量单位
     */
    @ProductField(needAudit = false)
    @ExaminField(description = "计量单位")
    private String measureUnit;

    /**
     * 限购数
     */
    @ProductField(needAudit = false)
    @ExaminField(description = "限购数")
    private Integer maxBuyCount;

    /**
     * 倍数起购量
     */
    @ProductField(needAudit = false)
    @ExaminField(description = "倍数起购量")
    private Integer multipleCount;

    /**
     * 第一个规格的安全库存
     */
    private Long safeStock;

    /**
     * 店铺分类
     */
    @ProductField(needAudit = false)
    @ExaminField(description = "店铺分类")
    private List<Long> shopCategoryIdList;

    /**
     * 店铺分类名称的集合
     */
    @ExaminField(description = "店铺分类名称")
    private List<String> shopCategoryNameList;

    /**
     * 是否开启规格
     */
    @ProductField(needAudit = true)
    @ExaminField(description = "是否开启规格")
    private Boolean hasSku;

    /**
     * 规格值集合(详情返回, 用于前端复显)
     */
    private List<ProductSpecValueGroupBo> specValueGroupList;

    /**
     * 规格图片集合(多规格时选填)
     */
    private List<ProductSpecImageBo> specImageList;

    /**
     * sku集合 单规格有一条默认值
     */
    @ExaminField(description = "规格集合", isChildField = true)
    private List<ProductSkuBo> skuList;

    /**
     * 运费模板
     */
    @ProductField(needAudit = false)
    @ExaminField(description = "运费模板id")
    private Long freightTemplateId;

    /**
     * 运费模板名称
     */
    @ExaminField(description = "运费模板名称")
    private String freightTemplateName;

    /**
     * 重量(根据运费模板确定是否必填)
     */
    @ProductField(needAudit = false)
    @ExaminField(description = "重量")
    private BigDecimal weight;

    /**
     * 体积(根据运费模板确定是否必填)
     */
    @ProductField(needAudit = false, type = BigDecimal.class)
    @ExaminField(description = "体积")
    private BigDecimal volume;

    /**
     * 商品主图
     */
    private String imagePath;

    /**
     * 主图集合(最多五张)
     */
    @ExaminField(description = "主图集合")
    private List<String> imageList;

    /**
     * 主图视频
     */
    @ProductField(needAudit = true, comparator = NullAsEmptyStringComparator.class)
    @ExaminField(description = "主图视频")
    private String videoPath;

    /**
     * PC端描述
     */
    @ProductField(needAudit = true, comparator = NullAsEmptyStringComparator.class)
    @ExaminField(description = "PC端描述")
    private String description;

    /**
     * 移动端描述
     */
    @ProductField(needAudit = true, comparator = NullAsEmptyStringComparator.class)
    @ExaminField(description = "移动端描述")
    private String mobileDescription;

    /**
     * 顶部版式Id
     */
    @ProductField(needAudit = true)
    @ExaminField(description = "顶部版式Id")
    private Long descriptionPrefixId;

    /**
     * 底部版式Id
     */
    @ProductField(needAudit = true)
    @ExaminField(description = "底部版式Id")
    private Long descriptionSuffixId;

    /**
     * 保存到草稿箱
     */
    @ExaminField(description = "是否保存到草稿")
    private Boolean draftFlag;

    /**
     * 规格1 别名
     */
    @ProductField(needAudit = true, comparator = NullAsEmptyStringComparator.class)
    @ExaminField(description = "规格1 别名")
    private String spec1Alias;

    /**
     * 规格2 别名
     */
    @ProductField(needAudit = true, comparator = NullAsEmptyStringComparator.class)
    @ExaminField(description = "规格2 别名")
    private String spec2Alias;

    /**
     * 规格3 别名
     */
    @ProductField(needAudit = true, comparator = NullAsEmptyStringComparator.class)
    @ExaminField(description = "规格3 别名")
    private String spec3Alias;

    /**
     * 销售状态 1-销售中 2-仓库中 3-草稿箱
     */
    @ProductField(needAudit = false)
    private Integer saleStatus;

    /**
     * 审核状态 1-待审核 2-销售中 3-未通过 4-违规下架
     */
    private Integer auditStatus;

    /**
     * 店铺Id
     */
    private Long shopId;

    /**
     * 分类全称
     */
    private String fullCategoryName;

    /**
     * 分类全id的集合
     */
    private List<Long> fullCategoryIds;

    /**
     * 商品状态
     */
    private Integer status;

    /**
     * 商品状态描述
     */
    private String statusDesc;

    /**
     * 是否新品
     */
    private Boolean whetherNewProduct;

    /**
     * sku更新方式
     */
    private SkuUpdateKeyEnum skuUpdateKey;

    /**
     * 来源 1-商城 2-牵牛花 3-易久批
     */
    private Integer source;

    /**
     * 来源描述
     */
    private String sourceDesc;

    /**
     * 本次选中的规格集合(从sku中提取)
     */
    private List<ProductSpecValueBo> specValueList;

    /**
     * 运费模板定价方式 0:按件数，1：按重量，2：按体积计算
     */
    private Integer freightTemplateIdMethod;

    /**
     * 最高售价
     */
    private BigDecimal maxSalePrice;

    /**
     * 价格范围
     */
    private String salePriceRange;

    /**
     * 销售量
     */
    private Long saleCounts;

    /**
     * 虚拟销售量
     */
    private Long virtualSaleCounts;

    /**
     * 品牌照片
     */
    private String brandLogo;

    /**
     * 发布时间
     */
    private Date addedDate;

    /**
     * 第一个规格的规格自增id
     */
    private Long skuAutoId;

    /**
     * 规格名称id的集合
     */
    @ProductField(needAudit = true, comparator = NullAsEmptyStringComparator.class)
    private String specNameIds;

}
