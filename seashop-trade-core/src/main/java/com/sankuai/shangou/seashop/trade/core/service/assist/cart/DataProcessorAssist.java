package com.sankuai.shangou.seashop.trade.core.service.assist.cart;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.google.common.base.Stopwatch;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.SkuFitCategoryCashDepositResp;
import com.sankuai.shangou.seashop.promotion.core.remote.PromotionRemoteService;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.UseAreaEnum;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.OrderQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.ProductQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponRecordSimpleResp;
import com.sankuai.shangou.seashop.trade.common.constant.CommonConst;
import com.sankuai.shangou.seashop.trade.common.enums.OrderPromotionType;
import com.sankuai.shangou.seashop.trade.common.remote.CashDeposit2TradeRemoteService;
import com.sankuai.shangou.seashop.trade.common.remote.FreightTemplateService;
import com.sankuai.shangou.seashop.trade.common.remote.SettingRemoteService;
import com.sankuai.shangou.seashop.trade.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.trade.common.remote.model.base.TradeSiteSettingBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.product.RemoteLadderPriceBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteCouponBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteReductionBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteShopUserPromotionBo;
import com.sankuai.shangou.seashop.trade.common.util.SettingUtil;
import com.sankuai.shangou.seashop.trade.common.util.ThreadPoolUtil;
import com.sankuai.shangou.seashop.trade.core.service.model.OrderAdditionalBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopExclusivePriceBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopInvoiceConfigBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;
import com.sankuai.shangou.seashop.trade.core.service.model.promotion.AddonDescBo;
import com.sankuai.shangou.seashop.trade.core.service.model.promotion.ProductPromotionBo;
import com.sankuai.shangou.seashop.trade.core.service.model.promotion.PromotionBo;
import com.sankuai.shangou.seashop.trade.core.service.model.promotion.ShopAndProductPromotionBo;
import com.sankuai.shangou.seashop.trade.thrift.core.enums.AddonProductPromotionTabEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.dto.QueryFreightTemplateDto;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopInvoiceResp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 交易相关数据处理辅助实现，用于购物车、订单预览等逻辑中的数据填充，减小基类代码量
 * <AUTHOR>
 */
@Service
@Slf4j
public class DataProcessorAssist {

    @Resource
    private SettingRemoteService settingRemoteService;
    @Resource
    private CashDeposit2TradeRemoteService cashDepositRemoteService;
    @Resource
    private ShopRemoteService shopRemoteService;
    @Resource
    private PromotionRemoteService promotionRemoteService;
    @Resource
    private ShoppingCartAssist shoppingCartAssist;
    @Resource
    private FreightTemplateService freightTemplateService;

    /**
     * 拆分均摊营销优惠。
     * 折扣在折扣计算的逻辑中已经处理均摊了，这里主要是处理满减和优惠券
     * 满减是针对订单所有商品的， 优惠券可能针对所有商品，可能是部分商品，优惠券的商品范围在优惠金额的计算中已经处理了，设置了优惠券ID
     *
     * <AUTHOR>
     * void
     */
    public void splitPromotionAmount(BaseBuildDataHolder dataHolder) {
        // 遍历每个订单，计算每个订单的满减均摊和优惠券均摊
        for (ShopProductListBo shopOrder : dataHolder.getShopProductList()) {
            OrderAdditionalBo additional = shopOrder.getAdditional();
            // 满减均摊
            BigDecimal reductionAmount = additional.getReductionAmount();
            if (reductionAmount != null && reductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 满减均摊
                splitPromotionToItem(shopOrder.getProductList(), additional.getReductionActivityId(), reductionAmount,
                        (sku, reductionId, splitAmount) -> {
                            sku.setReductionActivityId(reductionId);
                            BigDecimal amount = NumberUtil.nullToZero(sku.getSplitReductionAmount());
                            sku.setSplitReductionAmount(amount.add(splitAmount));
                        });
            }
            // 优惠券均摊
            BigDecimal couponAmount = additional.getCouponAmount();
            if (couponAmount != null && couponAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 优惠券均摊
                List<ShopProductBo> matchProductList = shopOrder.getProductList().stream()
                        .filter(sku -> sku.getCouponId() != null && sku.getCouponId().equals(additional.getCouponRecordId()))
                        .collect(Collectors.toList());
                log.info("当前满足优惠券均摊的商品为: {}", JsonUtil.toJsonString(matchProductList));
                splitPromotionToItem(matchProductList, additional.getCouponId(), couponAmount,
                        (sku, couponId, splitAmount) -> {
                            sku.setCouponId(couponId);
                            BigDecimal amount = NumberUtil.nullToZero(sku.getSplitCouponAmount());
                            sku.setSplitCouponAmount(amount.add(splitAmount));
                        });
            }
        }
    }

    /**
     * 计算优惠券，子类按需调用
     *
     * @param context 上下文
     * @param dataHolder 数据
     * <AUTHOR>
     */
    public void calculateCoupon(BuildContext context, BaseBuildDataHolder dataHolder) {
        // 处理优惠券
        for (ShopProductListBo shopOrder : dataHolder.getShopProductList()) {
            calculateCouponForShop(context, shopOrder);
        }
    }

    public void calculateCouponForShop(BuildContext context, ShopProductListBo shopOrder) {
        Long userId = context.getUserId();
        // 获取优惠券并重置相关金额
        Long couponRecordId = shopOrder.getAdditional().getCouponRecordId();
        // couponRecordId=0 是特殊含义，代表去掉优惠券
        if (couponRecordId == null || couponRecordId <= 0) {
            // 如果没有选择优惠券，保险设置一次
            shopOrder.getAdditional().setCouponAmount(BigDecimal.ZERO);
            return;
        }
        RemoteCouponBo coupon = promotionRemoteService.getCouponByRecordId(couponRecordId);
        // 下单时用入口的时间，其他场景默认取当前时间
        Date now = context.getCurrentTime();
        if (coupon == null || DateUtil.compare(coupon.getStartTime(), now) > 0 || DateUtil.compare(coupon.getEndTime(), now) < 0) {
            throw new BusinessException("优惠券不存在或已过期");
        }
        ShoppingCartShopBo shop = shopOrder.getShop();
        if (!shop.getShopId().equals(coupon.getShopId())) {
            throw new BusinessException("优惠券不适用于当前店铺");
        }
        if (!userId.equals(coupon.getUserId())) {
            throw new BusinessException("只能使用自己的优惠券");
        }
        if (shop.getSelectedTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("订单金额为0无须使用优惠券");
        }
        // 校验优惠券是否可用：适用范围以及商品是否满足门槛，正常返回即代表需要计算优惠券
        BigDecimal realCouponAmount = checkCouponCanUseAndReturnRealCouponAmount(coupon, shop, shopOrder.getProductList());

        OrderAdditionalBo additional = shopOrder.getAdditional();
        additional.setCouponRecordId(couponRecordId);
        additional.setCouponId(coupon.getCouponId());
        additional.setCouponAmount(realCouponAmount);
        // 重置店铺总金额
        shopOrder.getShop().setSelectedTotalAmount(shopOrder.getShop().getSelectedTotalAmount().subtract(realCouponAmount));
    }

    /**
     * 填充发票配置和sku的保障标记
     * 异常不影响主业务
     */
    public void fillExtraOrderPreviewData(Long userId, List<ShopProductListBo> shopOrderList) {
        int size = shopOrderList.size();
        List<Long> shopIdList = new ArrayList<>(size);
        List<String> skuIdList = new ArrayList<>(size * 10);
        Set<Long> freightTplIdSet = new HashSet<>(size * 3);
        // 用于查询优惠券的参数对象
        List<OrderQueryReq> couponOrderList = new ArrayList<>(size);
        OrderQueryReq orderQueryReq = null;
        ProductQueryReq prodReq = null;
        for (ShopProductListBo shopOrder : shopOrderList) {
            Long shopId = shopOrder.getShop().getShopId();

            shopIdList.add(shopId);

            List<ProductQueryReq> prodList = new ArrayList<>(shopOrder.getProductList().size());
            for (ShopProductBo sku : shopOrder.getProductList()) {
                skuIdList.add(sku.getSkuId());
                freightTplIdSet.add(sku.getFreightTemplateId());

                // 排除掉专享价的商品
                if (!Boolean.TRUE.equals(sku.getWhetherExclusive())) {
                    prodReq = new ProductQueryReq();
                    prodReq.setProductId(sku.getProductId());
                    prodReq.setProductAmount(sku.getTotalAmount());
                    prodList.add(prodReq);
                }
            }

            // 排除掉专享价后，可能为null
            if (CollUtil.isNotEmpty(prodList)) {
                orderQueryReq = new OrderQueryReq();
                orderQueryReq.setShopId(shopId);
                orderQueryReq.setProductList(prodList);
                couponOrderList.add(orderQueryReq);
            }
        }
        // 多线程获取数据
        Stopwatch stopwatch = Stopwatch.createStarted();

        ThreadPoolExecutor executor = ThreadPoolUtil.ASYNC_API_POOL;
        // 获取平台交易设置
        CompletableFuture<TradeSiteSettingBo> settingFuture = CompletableFuture.supplyAsync(() -> settingRemoteService.getTradeSiteSetting(), executor)
                .exceptionally(ex -> {
                    log.error("获取交易设置异常", ex);
                    return new TradeSiteSettingBo();
                });
        // 获取店铺发票设置
        CompletableFuture<Map<Long, QueryShopInvoiceResp>> shopInvoiceMapFuture = CompletableFuture.supplyAsync(() -> shopRemoteService.queryShopInvoiceSettingMap(shopIdList), executor)
                .exceptionally(ex -> {
                    log.error("获取店铺发票设置异常", ex);
                    return Collections.emptyMap();
                });
        // 获取sku的一级类目保证金配置，标识是更不重要的，单独异常捕获了
        CompletableFuture<Map<String, SkuFitCategoryCashDepositResp>> skuCateMapFuture = CompletableFuture.supplyAsync(() -> this.getSkuCateDeposit(skuIdList), executor)
                .exceptionally(ex -> {
                    log.error("获取sku的一级类目保证金配置异常", ex);
                    return Collections.emptyMap();
                });
        // 获取店铺的保证金配置，标识是更不重要的，单独异常捕获了
        CompletableFuture<Map<Long, CashDepositResp>> shopDepositMapFuture = CompletableFuture.supplyAsync(() -> this.getShopDeposit(shopIdList), executor)
                .exceptionally(ex -> {
                    log.error("获取店铺的保证金配置异常", ex);
                    return Collections.emptyMap();
                });
        // 获取运费模板，用于设置立即发货(闪电)标识
        CompletableFuture<Map<Long, QueryFreightTemplateDto>> freightTplFuture = CompletableFuture.supplyAsync(() -> freightTemplateService.getFreightTplByIdListMap(new ArrayList<>(freightTplIdSet)), executor)
                .exceptionally(ex -> {
                    log.error("获取运费模板异常", ex);
                    return Collections.emptyMap();
                });
        // 根据订单+商品获取有效的优惠券，设置是否显示优惠券
        CompletableFuture<Map<Long, List<CouponRecordSimpleResp>>> couponFuture = CompletableFuture.supplyAsync(() -> this.getValidCoupon(userId, couponOrderList), executor)
                .exceptionally(ex -> {
                    log.error("获取有效优惠券异常", ex);
                    return Collections.emptyMap();
                });
        
        // 当所有异步调用都完成时，执行业务逻辑
        try {
            // 等待所有异步调用完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(settingFuture, shopInvoiceMapFuture, skuCateMapFuture, shopDepositMapFuture, freightTplFuture);
            allFutures.get();
            // 收集所有结果
            TradeSiteSettingBo setting = settingFuture.get();
            Map<Long, QueryShopInvoiceResp> shopInvoiceMap = shopInvoiceMapFuture.get();
            Map<String, SkuFitCategoryCashDepositResp> skuCateMap = skuCateMapFuture.get();
            Map<Long, CashDepositResp> shopDepositMap = shopDepositMapFuture.get();
            Map<Long, QueryFreightTemplateDto> freightTplMap = freightTplFuture.get();
            Map<Long, List<CouponRecordSimpleResp>> couponMap = couponFuture.get();
            stopwatch.stop();
            log.info("获取发票配置和标识耗时：{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));
            // 处理业务逻辑
            fillExtraOrderPreviewData(shopOrderList, setting, shopInvoiceMap, skuCateMap, shopDepositMap, freightTplMap, couponMap);
        } catch (InterruptedException | ExecutionException e) {
            log.error("填充发票配置和标识异常", e);
        }
    }

    /**
     * 计算店铺商品sku的价格和总金额
     *
     * @param shop          店铺
     * @param shopSkuList   店铺的sku
     * @param shopPromotion 店铺的营销
     *                      void
     * <AUTHOR>
     */
    public void calculatePriceAndAmountForShop(BuildContext context,
                                               ShoppingCartShopBo shop,
                                               List<ShopProductBo> shopSkuList,
                                               RemoteShopUserPromotionBo shopPromotion) {
        BigDecimal selectedAmount = BigDecimal.ZERO;
        long productQuantity = 0L;
        BigDecimal productAmount = BigDecimal.ZERO;
        boolean shopSelected = true;
        // 店铺和商品营销，价格这里是逻辑里第一次处理营销，所以直接初始化
        ShopAndProductPromotionBo shopAndProductPromotion = new ShopAndProductPromotionBo();
        // 引用传递，先设置店铺的营销对象
        Map<String/*skuId*/, ProductPromotionBo> productPromotionMap = new HashMap<>(32);
        shopAndProductPromotion.setProductPromotionMap(productPromotionMap);
        shop.setShopAndProductPromotion(shopAndProductPromotion);
        // 处理并得到sku的专享价格
        Map<String, ShopExclusivePriceBo> skuExclusivePriceMap = shoppingCartAssist.flatSkuExclusivePriceAndToMap(shopPromotion.getShopExclusivePriceList());
        // 从专享价SKU中获取对应的商品ID，用于判断设置同商品下任意一个sku是专享价的，设置专享价标识
        Map<Long, Long> exclusiveProductIdMap = shoppingCartAssist.getExclusiveProductId(skuExclusivePriceMap);
        // 统计productId维度的商品数量，阶梯价的逻辑是，多规格商品的总数量满足阶梯价的条件，就需要用阶梯价，同productId的其他规格也需要变更价格
        Map<Long, Long> productQuantityMap = shopSkuList.stream()
                .collect(Collectors.groupingBy(ShopProductBo::getProductId, Collectors.summingLong(ShopProductBo::getQuantity)));
        // 遍历计算商品的基础实际售价，并汇总商品总金额
        for (ShopProductBo sku : shopSkuList) {
            ProductPromotionBo productPromotion = ProductPromotionBo.builder()
                    .productId(sku.getProductId())
                    .skuId(sku.getSkuId())
                    .build();
            // 计算并重置商品的价格
            resetRealSalePriceIfNecessary(context, sku, skuExclusivePriceMap, exclusiveProductIdMap, productPromotion, productQuantityMap);
            productPromotionMap.put(sku.getSkuId(), productPromotion);
            BigDecimal skuAmount = NumberUtil.mul(sku.getFinalSalePrice(), sku.getQuantity()).setScale(2, RoundingMode.HALF_UP);
            // 提交订单时，默认都是选中，所以直接累计
            if (Boolean.TRUE.equals(sku.getWhetherSelected())) {
                selectedAmount = selectedAmount.add(skuAmount);
                productQuantity += sku.getQuantity();
            } else {
                // 任意一个商品没有选中，店铺都不是选中状态
                shopSelected = false;
            }
            // 设置商品总金额
            sku.setTotalAmount(skuAmount);
            productAmount = productAmount.add(skuAmount);
        }
        // 设置店铺初步的总金额
        shop.setSelectedTotalAmount(selectedAmount);
        shop.setProductTotalAmount(productAmount);
        shop.setSelectedQuantity(productQuantity);
        shop.setWhetherSelected(shopSelected);
    }

    /**
     * 计算并构建满减信息
     *
     * @param shopProduct   店铺+商品列表对象
     * @param shopReduction 店铺的满减信息
     *                      void
     * <AUTHOR>
     */
    public void calculateAndBuildReductionAmount(ShopProductListBo shopProduct, RemoteReductionBo shopReduction) {
        if (shopReduction == null) {
            return;
        }
        BigDecimal condition = shopReduction.getMoneyOffCondition();
        if (condition == null || condition.compareTo(BigDecimal.ZERO) <= 0 || shopReduction.getMoneyOffFee().compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        ShoppingCartShopBo shop = shopProduct.getShop();
        List<ShopProductBo> shopSkuList = shopProduct.getProductList();
        // 专享价商品不参与满减，所以需要重新计算总金额，而不是取 shop.getSelectedTotalAmount()
        BigDecimal shopAmount = calculateSkuTotalAmountBesideExclusive(shopSkuList);
        // 要计算保留专享价的金额，后面店铺的总金额要加回去
        BigDecimal exclusiveAmount = shop.getSelectedTotalAmount().subtract(shopAmount).setScale(2, RoundingMode.HALF_UP);

        condition = condition.setScale(2, RoundingMode.HALF_UP);
        // 供应商设置了满减或折扣，当订单金额不足设置的条件时，提示：订单满X减X元，去凑单
        if (shopAmount.compareTo(condition) < 0) {
            String desc = String.format("订单满%s减%s元", condition, shopReduction.getMoneyOffFee());
            shop.addPromotionDesc(new AddonDescBo(AddonProductPromotionTabEnum.REDUCTION, desc));
            shop.setShowAddonBtn(true);
            return;
        }
        // 接下来的逻辑就是满足满减条件了。
        // 如果没有下一层级，提示：已购满X元，已减X元；如果有，提示：已购满X元，已减X元，再凑X元可减X元
        BigDecimal reductionAmount;
        boolean overlay = shopReduction.getMoneyOffOverLay() != null && shopReduction.getMoneyOffOverLay();
        BigDecimal moneyOffFee = shopReduction.getMoneyOffFee();
        int reductionCount = 1;
        // 如果是叠加，则代表有下一层级
        if (overlay) {
            // 计算当前金额满足的叠加次数
            reductionCount = shopAmount.divideToIntegralValue(condition).intValue();
            // 当前已经满足的满减金额
            reductionAmount = NumberUtil.mul(moneyOffFee, reductionCount);
            // 当前金额距下一个满减条件的差额
            BigDecimal diff = NumberUtil.mul(condition, (reductionCount + 1)).subtract(shopAmount).setScale(2, RoundingMode.HALF_UP);
            // 下一个满减的优惠金额
            BigDecimal nextRed = NumberUtil.mul(moneyOffFee, reductionCount + 1);
            // 处理凑单描述，目前只有购物车用
            BigDecimal fittedAmount = NumberUtil.mul(condition, reductionCount).setScale(2, RoundingMode.HALF_UP);
            String desc = String.format("已购满%s元，已减%s元，再凑%s元可减%s元", fittedAmount, reductionAmount, diff, nextRed);
            shop.addPromotionDesc(new AddonDescBo(AddonProductPromotionTabEnum.REDUCTION, desc));
            shop.setShowAddonBtn(true);

            // 处理满减提示，有叠加，提示第一个和最后一个，目前是预览订单页用
            shop.addReductionDesc(String.format("满%s元减%s元", condition, moneyOffFee));
            if (reductionCount > 1) {
                shop.addReductionDesc(String.format("满%s元减%s元", fittedAmount, reductionAmount));
            }
        } else {
            reductionAmount = shopReduction.getMoneyOffFee();
            String desc = String.format("已购满%s元，已减%s元", condition, reductionAmount);
            shop.addPromotionDesc(new AddonDescBo(AddonProductPromotionTabEnum.REDUCTION, desc));
            shop.setShowAddonBtn(false);
            // 没有叠加，提示一个
            shop.addReductionDesc(String.format("满%s元减%s元", condition, reductionAmount));
        }
        BigDecimal currentSelectedTotalAmount = shopAmount.subtract(reductionAmount).setScale(2, RoundingMode.HALF_UP);
        if (currentSelectedTotalAmount.compareTo(BigDecimal.ZERO) < 0) {
            currentSelectedTotalAmount = BigDecimal.ZERO;
        }
        // 专享价的金额加回去
        currentSelectedTotalAmount = currentSelectedTotalAmount.add(exclusiveAmount);
        // 重置商品总金额
        shop.setSelectedTotalAmount(currentSelectedTotalAmount);
        shopProduct.getAdditional().setReductionActivityId(shopReduction.getActiveId());
        shopProduct.getAdditional().setReductionAmount(reductionAmount);
        shopProduct.getAdditional().setReductionConditionAmount(NumberUtil.mul(condition, reductionCount));


        addShopPromotion(shop, PromotionBo.builder()
                .promotionType(OrderPromotionType.REDUCTION.name())
                .promotionType(OrderPromotionType.REDUCTION.getDesc())
                .promotionId(shopReduction.getActiveId())
                .promotionName(shopReduction.getActiveName())
                .matchConditionDesc(String.format("moneyOffCondition(满减门槛)=%s, moneyOffFee(满减金额)=%s, moneyOffOverLay(是否叠加)=%s",
                        shopReduction.getMoneyOffCondition(), shopReduction.getMoneyOffFee(), shopReduction.getMoneyOffOverLay()))
                .promotionValueDesc(String.format("店铺总金额=%s, 叠加次数=%s", shopAmount, reductionCount))
                .build());
    }

    /**
     * 专享价商品不参与 折扣、满减、优惠券，所以需要重新计算总金额
     * <AUTHOR>
     * @param shopSkuList 店铺商品
     * java.math.BigDecimal
     */
    public BigDecimal calculateSkuTotalAmountBesideExclusive(List<ShopProductBo> shopSkuList) {
        if (CollUtil.isEmpty(shopSkuList)) {
            return BigDecimal.ZERO;
        }
        return shopSkuList.stream()
                .filter(sku -> Boolean.TRUE.equals(sku.getWhetherSelected()))
                // 不能直接用false比较，因为有可能是null
                .filter(sku -> !Boolean.TRUE.equals(sku.getWhetherExclusive()))
                .map(sku -> NumberUtil.mul(sku.getFinalSalePrice(), sku.getQuantity()).setScale(2, RoundingMode.HALF_UP))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public Map<Long, List<CouponRecordSimpleResp>> getValidCoupon(Long userId, List<OrderQueryReq> couponOrderList) {
        try {
            if (CollUtil.isEmpty(couponOrderList)) {
                return Collections.emptyMap();
            }
            return promotionRemoteService.getValidCouponByOrderToMap(userId, couponOrderList);
        } catch (Exception e) {
            log.error("获取优惠券异常", e);
            return Collections.emptyMap();
        }
    }



    //********************************************

    public void splitPromotionToItem(List<ShopProductBo> productList, Long activeId, BigDecimal promotionAmount,
                                      AbstractShopProductBuilder.TriConsumer<ShopProductBo, Long, BigDecimal> dataSetConsumer) {
        if (CollUtil.isEmpty(productList)) {
            return;
        }
        // 对每个商品的总额排序，从大到小，这样均摊的时候，最后一个采用总额减时，误差相对更小
        // 专享价不参与折扣、满减、优惠券营销，所以排除
        List<ShopProductBo> excludeAndSortedList = productList.stream()
                .filter(sku -> !Boolean.TRUE.equals(sku.getWhetherExclusive()))
                .sorted((o1, o2) -> o2.getTotalAmount().compareTo(o1.getTotalAmount()))
                .collect(Collectors.toList());
        // 均摊计算的总金额用过滤后的
        BigDecimal totalAmount = excludeAndSortedList.stream()
                // 均摊过程需要用原价计算，
                .map(ShopProductBo::getTotalAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("计算均摊的总金额: {}", totalAmount);
        // 遍历均摊
        BigDecimal totalPromotionAmount = BigDecimal.ZERO;
        for (int i = 0; i < excludeAndSortedList.size() - 1; i++) {
            if (totalPromotionAmount.compareTo(promotionAmount) >= 0) {
                break;
            }
            ShopProductBo product = excludeAndSortedList.get(i);
            BigDecimal productTotalAmount = product.getTotalAmount();
            // 这里的均摊都是根据商品金额按比例计算的，所以均摊的金额不会超过商品金额
            BigDecimal itemPromotionAmount = NumberUtil.mul(promotionAmount, productTotalAmount)
                    .divide(totalAmount, 2, RoundingMode.HALF_UP);
            totalPromotionAmount = totalPromotionAmount.add(itemPromotionAmount);
            log.info("第一轮均摊计算, 序号={}, skuId={}, 商品金额={}, 参与均摊总金额={}, 均摊后金额={}, 当前总的均摊优惠={}", i, product.getSkuId(), productTotalAmount, totalAmount, itemPromotionAmount, totalPromotionAmount);
            dataSetConsumer.accept(product, activeId, itemPromotionAmount);
        }
        // 最后一个，用总额减
        ShopProductBo lastProduct = excludeAndSortedList.get(excludeAndSortedList.size() - 1);
        BigDecimal remainPromotionAmount = promotionAmount.subtract(totalPromotionAmount);
        log.info("第一轮均摊后剩余营销金额={}", remainPromotionAmount);
        if (remainPromotionAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        BigDecimal lastPromotionAmount = remainPromotionAmount;
        // 最后一个，因为是用剩余的减的，此时剩余的优惠金额可能会大于商品金额
        if (remainPromotionAmount.compareTo(lastProduct.getTotalAmount()) > 0) {
            lastPromotionAmount = lastProduct.getTotalAmount();
        }
        dataSetConsumer.accept(lastProduct, activeId, lastPromotionAmount);

        BigDecimal overPromotionAmount = remainPromotionAmount.subtract(lastPromotionAmount);
        log.info("第一轮均摊后营销超出部分={}", overPromotionAmount);
        if (overPromotionAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        for (int i = 0; i < excludeAndSortedList.size(); i++) {
            if (overPromotionAmount.compareTo(BigDecimal.ZERO) <= 0) {
                return;
            }
            ShopProductBo product = excludeAndSortedList.get(i);
            BigDecimal productTotalAmount = product.getTotalAmount();
            BigDecimal itemPromotionAmount = NumberUtil.mul(overPromotionAmount, productTotalAmount)
                    .divide(totalAmount, 2, RoundingMode.HALF_UP);
            if (itemPromotionAmount.compareTo(BigDecimal.ZERO) <= 0) {
                itemPromotionAmount = new BigDecimal("0.01");
            }
            log.info("第二轮均摊计算, 序号={}, skuId={}, 商品金额={}, 参与均摊总金额={}, 均摊后金额={}", i, product.getSkuId(), productTotalAmount, totalAmount, itemPromotionAmount);
            overPromotionAmount = overPromotionAmount.subtract(itemPromotionAmount);
            dataSetConsumer.accept(product, activeId, itemPromotionAmount);
        }
    }


    public static void main(String[] args){
        List<ShopProductBo> productList = buildTestProductList();
        Long activeId = 1L;
        BigDecimal promotionAmount = new BigDecimal("1.14");
        AbstractShopProductBuilder.TriConsumer<ShopProductBo, Long, BigDecimal> dataSetConsumer = (sku, reductionId, splitAmount) -> {
            sku.setReductionActivityId(reductionId);
            sku.setSplitReductionAmount(NumberUtil.nullToZero(sku.getSplitReductionAmount()).add(splitAmount));
        };
        new DataProcessorAssist().splitPromotionToItem(productList, activeId, promotionAmount, dataSetConsumer);
        
        System.out.println(JsonUtil.toJsonString(productList));
    }

    private static List<ShopProductBo> buildTestProductList() {
        List<ShopProductBo> productList = new ArrayList<>();

        ShopProductBo product1 = new ShopProductBo();
        product1.setTotalAmount(new BigDecimal("0.2"));
        productList.add(product1);

        ShopProductBo product2 = new ShopProductBo();
        product2.setTotalAmount(new BigDecimal("0.2"));
        productList.add(product2);

        ShopProductBo product3 = new ShopProductBo();
        product3.setTotalAmount(new BigDecimal("0.2"));
        productList.add(product3);

        ShopProductBo product4 = new ShopProductBo();
        product4.setTotalAmount(new BigDecimal("0.2"));
        productList.add(product4);

        ShopProductBo product5 = new ShopProductBo();
        product5.setTotalAmount(new BigDecimal("0.2"));
        productList.add(product5);

        ShopProductBo product6 = new ShopProductBo();
        product6.setTotalAmount(new BigDecimal("0.2"));
        productList.add(product6);

        ShopProductBo product7 = new ShopProductBo();
        product7.setTotalAmount(new BigDecimal("0.01"));
        productList.add(product7);

        ShopProductBo product8 = new ShopProductBo();
        product8.setTotalAmount(new BigDecimal("0.1"));
        productList.add(product8);

        return productList;
    }







    private BigDecimal checkCouponCanUseAndReturnRealCouponAmount(RemoteCouponBo coupon, ShoppingCartShopBo shop, List<ShopProductBo> productList) {
        BigDecimal couponConditionAmount = new BigDecimal(String.valueOf(coupon.getOrderAmount()));
        // 优惠券有使用门槛，也可能会指定商品，所以判断的时候进行区分
        // 专享价商品不参与优惠券，所以需要重新计算总金额，而不是取 shop.getSelectedTotalAmount()
        BigDecimal matchProductAmount = this.calculateSkuTotalAmountBesideExclusive(productList);
        // 全场通用，且店铺总额小于门槛，则不可用
        if (coupon.getUseArea().equals(0) && matchProductAmount.compareTo(couponConditionAmount) < 0) {
            throw new BusinessException("订单金额不满足优惠券使用门槛");
        }
        BigDecimal couponAmount = new BigDecimal(String.valueOf(coupon.getPrice()));
        // 订单中匹配优惠券商品范围的总金额，默认取订单中排除专享价商品的总金额
        // 指定商品，且任一下单商品不在优惠券商品范围内或者商品总额小于门槛，则不可用
        List<Long> suitProductIdList = null;
        if (UseAreaEnum.PRODUCT.getCode().equals(coupon.getUseArea())) {
            log.info("优惠券使用范围：指定商品");
            // 商品列表数据，根据productId分组，并求和商品总金额
            Map<Long, BigDecimal> orderProductAmountMap = productList.stream()
                    .collect(Collectors.groupingBy(ShopProductBo::getProductId, Collectors.reducing(BigDecimal.ZERO, ShopProductBo::getTotalAmount, BigDecimal::add)));
            Map<Long, Long> couponProductIdMap = coupon.getProductIdList().stream()
                    .collect(Collectors.toMap(Long::longValue, Long::longValue, (o1, o2) -> o2));
            // 下单商品在优惠券商品范围内的商品ID列表
            suitProductIdList = productList.stream()
                    // 不能直接用false比较，因为有可能是null
                    .filter(sku -> !Boolean.TRUE.equals(sku.getWhetherExclusive()))
                    .map(ShopProductBo::getProductId)
                    .filter(couponProductIdMap::containsKey)
                    // 订单商品是sku维度，这里获取商品ID，可能重复
                    .distinct()
                    .collect(Collectors.toList());
            // 如果没有一个商品在优惠券商品范围内，则不可用
            if (CollUtil.isEmpty(suitProductIdList)) {
                throw new BusinessException("优惠券不适用于当前商品");
            }
            // 指定商品可用时，重置订单中匹配优惠券商品范围的总金额
            matchProductAmount = suitProductIdList.stream()
                    .map(orderProductAmountMap::get)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            log.info("优惠券使用范围：指定商品，匹配优惠券商品范围的总金额={}", matchProductAmount);
            if (matchProductAmount.compareTo(couponConditionAmount) < 0) {
                throw new BusinessException("订单金额不满足优惠券使用门槛");
            }
        }
        // 到这里，说明优惠券满足适用条件，设置明细上的优惠券活动ID
        // 如果suitProductIdList为空，则说明是全场通用，否则是指定商品
        Long couponId = coupon.getId();
        if (CollUtil.isEmpty(suitProductIdList)) {
            productList.forEach(p -> p.setCouponId(couponId));
        } else {
            log.info("优惠券使用范围为指定商品，设置优惠券ID到商品上");
            Map<Long, Long> idMap = suitProductIdList.stream()
                    .collect(Collectors.toMap(Function.identity(), id -> id, (oldV, newV) -> newV));
            productList.stream()
                    .filter(p -> idMap.containsKey(p.getProductId()))
                    .forEach(p -> p.setCouponId(couponId));
        }
        // 取匹配优惠券商品范围的总金额和优惠券金额的最小值
        return matchProductAmount.compareTo(couponAmount) > 0 ? couponAmount : matchProductAmount;
    }

    /**
     * 重置商品sku的实际售价
     * <p>售价优先规则：专享价>阶梯价>商城价</p>
     *
     * @param sku                  购物车SKU
     * @param skuExclusivePriceMap 专享价
     * <AUTHOR>
     */
    private void resetRealSalePriceIfNecessary(BuildContext context,
                                               ShopProductBo sku,
                                               Map<String, ShopExclusivePriceBo> skuExclusivePriceMap,
                                               Map<Long, Long> productShouldExclusiveMap,
                                               ProductPromotionBo productPromotion,
                                               Map<Long, Long> productQuantityMap) {
        // 取反，默认需要
        // 有专享价就直接返回，专享价优先级最高
        if (!Boolean.FALSE.equals(context.needExclusivePrice())) {
            // 商品的某个sku有专享价，其他sku也要设置专享价标识
            if (productShouldExclusiveMap.containsKey(sku.getProductId())) {
                sku.setWhetherExclusive(true);
            }
            // 如果有专属价格，使用专属价格
            if (skuExclusivePriceMap != null && skuExclusivePriceMap.containsKey(sku.getSkuId())) {
                ShopExclusivePriceBo exclusivePrice = skuExclusivePriceMap.get(sku.getSkuId());
                sku.setRealSalePrice(exclusivePrice.getPrice());
                sku.setFinalSalePrice(exclusivePrice.getPrice());
                // 构建设置商品营销
                productPromotion.getPromotionList().add(PromotionBo.builder()
                        .promotionType(OrderPromotionType.EXCLUSIVE_PRICE.name())
                        .promotionTypeDesc(OrderPromotionType.EXCLUSIVE_PRICE.getDesc())
                        .promotionId(exclusivePrice.getActivityId())
                        .promotionName(exclusivePrice.getActivityName())
                        .promotionValueDesc(exclusivePrice.getPrice().toPlainString())
                        .build());
                return;
            }
        }
        // 没有专享价时，如果有阶梯价格，使用阶梯价格
        // 取反，默认需要
        if (!Boolean.FALSE.equals(context.needLadderPrice())) {
            List<RemoteLadderPriceBo> ladderPriceList = sku.getLadderPriceList();
            if (CollectionUtils.isNotEmpty(ladderPriceList)) {
                // 阶梯价的数量，要用当前规格对应商品的多规格的总数量
                long quantity = productQuantityMap.get(sku.getProductId());
                // 无法保证接口返回的阶梯价是否是有序的，而且阶梯价目前最多三个，所以直接全部遍历，任意一个满足就返回
                for (RemoteLadderPriceBo ladderPrice : ladderPriceList) {
                    if (quantity >= ladderPrice.getMinBath() && quantity <= ladderPrice.getMaxBath()) {
                        sku.setRealSalePrice(ladderPrice.getPrice());
                        sku.setFinalSalePrice(ladderPrice.getPrice());

                        // 构建设置商品营销
                        productPromotion.getPromotionList().add(PromotionBo.builder()
                                .promotionType(OrderPromotionType.EXCLUSIVE_PRICE.name())
                                .promotionType(OrderPromotionType.EXCLUSIVE_PRICE.getDesc())
                                .matchConditionDesc(String.format("商品数量=%s, minBatch=%s, maxBatch=%s, price=%s",
                                        quantity, ladderPrice.getMinBath(), ladderPrice.getMaxBath(), ladderPrice.getPrice()))
                                .promotionValueDesc(ladderPrice.getPrice().toPlainString())
                                .build());
                        return;
                    }
                }
            }
        }
    }

    private void addShopPromotion(ShoppingCartShopBo shop, PromotionBo promotion) {
        ShopAndProductPromotionBo shopAndProductPromotion = shop.getShopAndProductPromotion();
        if (shopAndProductPromotion == null) {
            shopAndProductPromotion = new ShopAndProductPromotionBo();
            shop.setShopAndProductPromotion(shopAndProductPromotion);
        }
        List<PromotionBo> shopPromotionList = shopAndProductPromotion.getShopPromotionList();
        if (CollectionUtils.isEmpty(shopPromotionList)) {
            shopPromotionList = new ArrayList<>(3);
            shopAndProductPromotion.setShopPromotionList(shopPromotionList);
        }
        shopPromotionList.add(promotion);
    }


    private void fillExtraOrderPreviewData(List<ShopProductListBo> shopOrderList,
                                           TradeSiteSettingBo setting,
                                           Map<Long, QueryShopInvoiceResp> shopInvoiceMap,
                                           Map<String, SkuFitCategoryCashDepositResp> skuCateMap,
                                           Map<Long, CashDepositResp> shopDepositMap,
                                           Map<Long, QueryFreightTemplateDto> freightTplMap,
                                           Map<Long, List<CouponRecordSimpleResp>> couponMap) {

        Stopwatch stopwatch = Stopwatch.createStarted();
        // 使用 completableFuture 通过多线程 实现每个店铺赋值并行处理每个ShopOrder
        CompletableFuture.allOf(shopOrderList.stream()
                .map(shopOrder -> {
                    Long shopId = shopOrder.getShop().getShopId();
                    QueryShopInvoiceResp shopInvoice = shopInvoiceMap.get(shopId);
                    CashDepositResp cashDeposit = shopDepositMap.get(shopId);
                    List<CouponRecordSimpleResp> validCouponList = couponMap.get(shopId);

                    return CompletableFuture.runAsync(() -> {
                        // 每个店铺互不影响
                        try {
                            fillExtraOrderPreviewDataEachShop(shopOrder, setting, shopInvoice, cashDeposit, validCouponList, skuCateMap, freightTplMap);
                        } catch (Exception e) {
                            log.error("预览订单填充额外数据异常, shopId={}", shopId, e);
                        }
                    });
                })
                .toArray(CompletableFuture[]::new))
                // 等待所有Future完成
                .join();
        stopwatch.stop();
        log.info("预览订单填充额外数据耗时：{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));
    }

    private void fillExtraOrderPreviewDataEachShop(ShopProductListBo shopOrder, TradeSiteSettingBo setting,
                                                   QueryShopInvoiceResp shopInvoice,
                                                   CashDepositResp cashDeposit,
                                                   List<CouponRecordSimpleResp> validCouponList,
                                                   Map<String, SkuFitCategoryCashDepositResp> skuCateMap,
                                                   Map<Long, QueryFreightTemplateDto> freightTplMap) {
        ShoppingCartShopBo shop = shopOrder.getShop();
        log.info("填充店铺额外数据, shopId={}", shop.getShopId());

        OrderAdditionalBo additional = shopOrder.getAdditional();
        //  发票相关设置
        if (shopInvoice != null) {
            ShopInvoiceConfigBo config = JsonUtil.copy(shopInvoice, ShopInvoiceConfigBo.class);
            config.setWarrantyDays(SettingUtil.getIntValueOrDefault(setting.getSalesReturnTimeout(), CommonConst.DEFAULT_TRADE_SETTING_WARRANTY_DAYS));
            // 针对可能为null的情况，设置默认值
            config.setDefault();
            additional.setShopInvoiceConfig(config);
        } else {
            ShopInvoiceConfigBo config = ShopInvoiceConfigBo.defaultEmpty();
            additional.setShopInvoiceConfig(config);
        }
        // 保障标记相关设置
        // 保证金是店铺维度，但是页面显示是在商品
        Boolean showGuaranteeFlag = cashDeposit != null && cashDeposit.getTotalBalance().compareTo(BigDecimal.ZERO) > 0;

        // 优惠券相关设置，设置一个标识，如果有，前端显示优惠券选项，具体的优惠券，再调优惠券接口
        boolean hasValidCoupon = CollUtil.isNotEmpty(validCouponList);
        shop.setHasValidCoupon(hasValidCoupon);
        // 保存优惠券是为了后续默认选择面额最大的
        if (hasValidCoupon) {
            // 面额降序
            validCouponList.sort((o1, o2) -> (int) (o2.getPrice() - o1.getPrice()));
            shopOrder.setValidCouponList(validCouponList);
        }

        for (ShopProductBo item : shopOrder.getProductList()) {
            SkuFitCategoryCashDepositResp skuCate = skuCateMap.get(item.getSkuId());
            Boolean enableNoReasonReturn = skuCate != null &&
                    skuCate.getCategoryCashDepositResp() != null &&
                    Boolean.TRUE.equals(skuCate.getCategoryCashDepositResp().getEnableNoReasonReturn());

            item.setEnableNoReasonReturn(enableNoReasonReturn);
            item.setShowGuaranteeFlag(showGuaranteeFlag);

            // 从运费模板判断立即发货标识
            QueryFreightTemplateDto freightTpl = freightTplMap.get(item.getFreightTemplateId());
            boolean showThunderFlag = freightTpl != null && CommonConst.FLAG_DELIVER_IMMEDIATELY.equals(freightTpl.getSendTime());
            item.setShowThunderFlag(showThunderFlag);
        }
    }

    private Map<String, SkuFitCategoryCashDepositResp> getSkuCateDeposit(List<String> skuIdList) {
        try {
            // todo 暂时先注释保证金从查询
            return cashDepositRemoteService.getSkuFitCateConfigMap(skuIdList);
        } catch (Exception e) {
            log.error("获取sku对应一级类目保证金配置异常", e);
            return Collections.emptyMap();
        }
    }

    private Map<Long, CashDepositResp> getShopDeposit(List<Long> shopIdList) {
        try {
            return cashDepositRemoteService.getShopDepositMap(shopIdList);
        } catch (Exception e) {
            log.error("获取店铺保证金配置异常", e);
            return Collections.emptyMap();
        }
    }


}
