package com.sankuai.shangou.seashop.product.core.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.base.utils.SquirrelUtil;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.product.common.constant.CacheConstant;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.common.constant.LockConstant;
import com.sankuai.shangou.seashop.product.common.es.service.EsProductService;
import com.sankuai.shangou.seashop.product.common.remote.base.RemoteCustomFormService;
import com.sankuai.shangou.seashop.product.core.service.CategoryService;
import com.sankuai.shangou.seashop.product.core.service.assist.CategoryAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.TransactionEventPublisher;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.event.SendCategoryChangeEvent;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.ProductLogAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.CategoryBindFormLogBo;
import com.sankuai.shangou.seashop.product.core.service.model.CategoryBo;
import com.sankuai.shangou.seashop.product.core.service.model.CategoryCacheBo;
import com.sankuai.shangou.seashop.product.core.service.model.CategoryQueryBo;
import com.sankuai.shangou.seashop.product.core.service.model.SaveCategoryBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Category;
import com.sankuai.shangou.seashop.product.dao.core.domain.CategoryCashDeposit;
import com.sankuai.shangou.seashop.product.dao.core.repository.CategoryCashDepositRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.CategoryRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRepository;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ShowStatusEnum;
import com.sankuai.shangou.seashop.product.thrift.core.event.category.CategoryChangeEvent;
import com.sankuai.shangou.seashop.product.thrift.core.event.category.type.CategoryChangeType;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryCategoryByIdsReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryResp;
import com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryValidBusinessCategoryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ValidBusinessCategoryIdsResp;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2023/11/10 16:23
 */
@Service
@Slf4j
public class CategoryServiceImpl implements CategoryService {

    /**
     * 最小深度
     */
    private static Integer MIN_DEPTH = 1;
    /**
     * 最大深度
     */
    private static Integer MAX_DEPTH = 3;

    /**
     * 默认佣金比例(一二级类目使用该比例)
     */
    private static BigDecimal DEFAULT_COMMISSION_RATE = new BigDecimal(100);

    @Resource
    private CategoryRepository categoryRepository;
    @Resource
    private ProductRepository productRepository;
    @Resource
    private CategoryCashDepositRepository categoryCashDepositRepository;
    @Resource
    private RemoteCustomFormService remoteCustomFormService;
    @Resource
    private BusinessCategoryQueryFeign businessCategoryQueryFeign;
    @Resource
    private CategoryAssist categoryAssist;
    @Resource
    private SquirrelUtil squirrelUtil;
    @Resource
    private TransactionEventPublisher transactionEventPublisher;
    @Resource
    private ProductAuditRepository productAuditRepository;
    @Resource
    private ProductLogAssist productLogAssist;
    @Resource
    private EsProductService esProductService;

    @Override
    public void saveCategory(SaveCategoryBo categoryBo) {
        String lock = LockConstant.SAVE_CATEGORY_LOCK + categoryBo.getParentCategoryId();

        LockHelper.lock(lock, () -> {
            boolean editFlag = Optional.ofNullable(categoryBo.getId()).filter(id -> id > 0).isPresent();
            checkAndHandleCategoryBo(categoryBo, editFlag);

            TransactionHelper.doInTransaction(() -> {
                // 保存类目信息
                Category category = JsonUtil.copy(categoryBo, Category.class);
                categoryRepository.saveOrUpdate(category);

                // 如果是新增的类目 需要更新path
                if (!editFlag) {
                    category.setPath(getCategoryPath(categoryBo.getParentPath(), category.getId()));
                    categoryRepository.updateById(category);

                    // 更新上级类目的是否有下级
                    updateHasChildrenStatus(category.getParentCategoryId());
                }

                // 更新开启状态
                resetShowStatus(category.getId(), editFlag ? null : true);

                // 发送类目变动通知
                sendCategoryChangeEvent(Collections.singletonList(category.getId()), editFlag ? CategoryChangeType.EDIT : CategoryChangeType.CREATE);

                // 清除缓存
                removeCategoryCache();
            });
        });
    }

    private String getCategoryPath(String parentPath, Long id) {
        return StringUtils.isEmpty(parentPath) ? String.valueOf(id) : parentPath + CommonConstant.CATEGORY_PATH_SPLIT_NO_ESCAPE + id;
    }

    @Override
    public void updateCategoryParam(SaveCategoryBo categoryBo) {
        String lock = LockConstant.SAVE_CATEGORY_LOCK + categoryBo.getName();
        LockHelper.lock(lock, () -> {
            checkAndHandleForUpdateCategoryParam(categoryBo);

            TransactionHelper.doInTransaction(() -> {
                // 更新类目信息
                categoryRepository.updateById(JsonUtil.copy(categoryBo, Category.class));

                // 重置显示状态
                resetShowStatus(categoryBo.getId(), categoryBo.getWhetherShow());

                // 发送类目变动通知
                sendCategoryChangeEvent(Arrays.asList(categoryBo.getId()), CategoryChangeType.EDIT);

                // 清除缓存
                removeCategoryCache();
            });
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCategory(Long id) {
        Category category = categoryRepository.getById(id);
        AssertUtil.throwIfTrue(category == null || category.getWhetherDelete(), "类目不存在");
        AssertUtil.throwIfTrue(category.getDefaultStatus(), "默认类目不能删除");

        // 获取当前类目的下级类目
        List<Long> childIds = getChildIds(category.getId());
        childIds.add(category.getId());

        int count = productRepository.countByCategoryIds(childIds);
        AssertUtil.throwIfTrue(count > 0, "该类目或子类目下存在商品，不能删除");
        count = productAuditRepository.countByCategoryIds(childIds);
        AssertUtil.throwIfTrue(count > 0, "该类目或子类目下存在待审核商品，不能删除");

        // 移除当前类目及其所有子类目
        categoryRepository.logicRemoveByIds(childIds);

        // 更新上级类目的是否有下级
        updateHasChildrenStatus(category.getParentCategoryId());

        // 重置上级的显示状态
        resetSelfShowStatus(category.getParentCategoryId());

        // 发送类目变动通知
        sendCategoryChangeEvent(Arrays.asList(id), CategoryChangeType.DELETE);

        // 清除缓存
        removeCategoryCache();
    }

    @Override
    public List<CategoryBo> queryCategoryTree(CategoryQueryBo queryBo) {
        queryBo.setMaxDepth(Optional.ofNullable(queryBo.getMaxDepth()).orElse(MAX_DEPTH));
        List<Category> categoryList = categoryRepository.list(commonBuilderCategoryQueryWrapper(queryBo));

        List<CategoryBo> categoryBoList = JsonUtil.copyList(categoryList, CategoryBo.class);
        return buildCategoryTree(categoryBoList, queryBo);
    }

    @Override
    public List<CategoryBo> queryCategoryTreeHideNoThreeLevel(CategoryQueryBo queryBo) {
        queryBo.setMaxDepth(Optional.ofNullable(queryBo.getMaxDepth()).orElse(MAX_DEPTH));
        List<Category> categoryList = categoryRepository.list(commonBuilderCategoryQueryWrapper(queryBo));

        List<CategoryBo> categoryBoList = JsonUtil.copyList(categoryList, CategoryBo.class);
        // 24/12/3 cjzhao修改  将不具有三级的类目过滤掉
        // 从第三级类目查起，利用path 找出所有父级id 再用原始list过滤
        List<Long> parentIds = getPathsAsLongList(categoryBoList);
        categoryBoList = categoryBoList.stream().filter(t -> parentIds.contains(t.getId())).collect(Collectors.toList());
        return buildCategoryTree(categoryBoList, queryBo);
    }

    public List<Long> getPathsAsLongList(List<CategoryBo> categoryBoList) {
        return categoryBoList.stream().filter(categoryBo -> categoryBo.getDepth() == 3).flatMap(categoryBo -> Arrays.stream(categoryBo.getPath().split("\\|"))).map(Long::parseLong).collect(Collectors.toList());
    }

    @Override
    public List<CategoryBo> queryCategoryTreeForApply(CategoryQueryBo queryBo, Long shopId) {
        queryBo.setMaxDepth(Optional.ofNullable(queryBo.getMaxDepth()).orElse(MAX_DEPTH));
        List<Category> categoryList = categoryRepository.list(commonBuilderCategoryQueryWrapper(queryBo));
        QueryValidBusinessCategoryReq req = new QueryValidBusinessCategoryReq();
        req.setShopId(shopId);
        ValidBusinessCategoryIdsResp resp = ThriftResponseHelper.executeThriftCall(() -> businessCategoryQueryFeign.queryValidBusinessCategoryIds(req));
        Set<Long> excludeIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(resp.getCategoryIds())) {
            excludeIds.addAll(resp.getCategoryIds());
        }
        if (CollectionUtils.isNotEmpty(resp.getWaitAuditCategoryIds())) {
            excludeIds.addAll(resp.getWaitAuditCategoryIds());
        }

        if (!CollectionUtils.isEmpty(excludeIds)) {
            categoryList = categoryList.stream().filter(category -> !excludeIds.contains(category.getId())).collect(Collectors.toList());
        }
        List<CategoryBo> categoryBoList = JsonUtil.copyList(categoryList, CategoryBo.class);
        return buildCategoryTree(categoryBoList, queryBo);
    }

    @Override
    public List<CategoryBo> queryCategoryList(CategoryQueryBo queryBo) {
        LambdaQueryWrapper<Category> wrapper = commonBuilderCategoryQueryWrapper(queryBo);
        List<Category> categoryList = categoryRepository.list(wrapper);
        if (CollectionUtils.isEmpty(categoryList)) {
            return Collections.EMPTY_LIST;
        }

        List<CategoryBo> categoryBoList = JsonUtil.copyList(categoryList, CategoryBo.class);
        initCategoryData(categoryBoList);
        return categoryBoList;
    }

    @Override
    public Map<Long, CategoryBo> getCategoryMap(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.EMPTY_MAP;
        }

        return queryCategoryList(CategoryQueryBo.builder().ids(ids).build()).stream().collect(Collectors.toMap(CategoryBo::getId, Function.identity(), (k1, k2) -> k2));
    }

//    @Override
//    public List<CategoryBo> queryLastCategoryList(Long id) {
//        Category category = categoryRepository.getById(id);
//        AssertUtil.throwIfTrue(category == null || category.getWhetherDelete(), "类目不存在");
//
//        CategoryQueryBo queryBo = new CategoryQueryBo();
//        // 如果本身就是三级类目 直接返回
//        if (MAX_DEPTH.equals(category.getDepth())) {
//            queryBo.setIds(Arrays.asList(id));
//        } else {
//            queryBo.setRecursiveParentId(id);
//            queryBo.setDepth(MAX_DEPTH);
//        }
//        return queryCategoryList(queryBo);
//    }

    /**
     * cjzhao修改 不需要递归 使用path 左匹配即可
     *
     * @param id 类目id
     * @return
     */
    @Override
    public List<CategoryBo> queryLastCategoryList(Long id) {
        Category category = categoryRepository.getById(id);
        List<Category> categoryList;
        // 如果该分类还有子类目 则获取该分类下的所有三级类目 否则 获取该分类本身
        if (category.getHasChildren()) {
            categoryList = categoryRepository.getThreeDepthByPath(category.getId() + "|");
        } else categoryList = Collections.singletonList(category);
        List<CategoryBo> categoryBoList = JsonUtil.copyList(categoryList, CategoryBo.class);
        initCategoryData(categoryBoList);
        return JsonUtil.copyList(categoryList, CategoryBo.class);
    }

    @Override
    public List<CategoryBo> queryFirstCategoryList(List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Lists.newArrayList();
        }

        List<Category> categoryList = categoryRepository.listCategoryByIds(categoryIds);

        // 提取一级类目
        List<Long> rootCategoryIdList = getFirstIds(categoryList);
        List<CategoryBo> categoryBoList = JsonUtil.copyList(categoryRepository.listCategoryByIds(rootCategoryIdList), CategoryBo.class);
        initCategoryData(categoryBoList);
        return categoryBoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindCustomForm(CategoryBo categoryBo) {
        Category category = categoryRepository.getById(categoryBo.getId());
        AssertUtil.throwIfTrue(category == null || category.getWhetherDelete(), "类目不存在");

        List<Long> formIds = Arrays.asList(categoryBo.getCustomFormId(), category.getCustomFormId());
        Map<Long, String> formMap = remoteCustomFormService.getCustomerFormNameMap(formIds);
        if (categoryBo.getCustomFormId() != null && categoryBo.getCustomFormId() > 0) {
            AssertUtil.throwIfNull(formMap.get(categoryBo.getCustomFormId()), "自定义表单不存在");
        }

        Category updCategory = new Category();
        updCategory.setId(categoryBo.getId());
        updCategory.setCustomFormId(categoryBo.getCustomFormId());
        categoryRepository.updateById(updCategory);

        // 记录操作日志
        productLogAssist.recordCategoryBindFormLog(categoryBo.getOperationUserId(), categoryBo.getOperationShopId(), CategoryBindFormLogBo.build(categoryBo.getId(), formMap.get(category.getCustomFormId())), CategoryBindFormLogBo.build(categoryBo.getId(), formMap.get(categoryBo.getCustomFormId())));

        // 发送类目变动通知
        sendCategoryChangeEvent(Arrays.asList(categoryBo.getId()), CategoryChangeType.EDIT);

        // 清除缓存
        removeCategoryCache();
    }

    @Override
    public CategoryBo getCategoryById(Long id) {
        return getCategoryMap(Arrays.asList(id)).get(id);
    }

    @Override
    public List<CategoryBo> queryCategoryTreeWithParent(CategoryQueryBo queryBo) {
        List<Category> categoryList = categoryRepository.list(commonBuilderCategoryQueryWrapper(queryBo));
        if (CollectionUtils.isEmpty(categoryList)) {
            return Collections.EMPTY_LIST;
        }

        categoryList = categoryRepository.listCategoryByIds(getFullIds(categoryList));
        queryBo.setParentId(CommonConstant.DEFAULT_PARENT_ID);
        return buildCategoryTree(JsonUtil.copyList(categoryList, CategoryBo.class), queryBo);
    }

    /**
     * 检查并处理类目保存入参
     * 校验规则如下:
     * 1、编辑类目是不能修改上级类目
     * 2、类目名称不能跟上级类目或者同级类目一致
     * 3、如果是三级类目 佣金比例必填
     *
     * @param categoryBo 类目参数
     * @param editFlag   是否是编辑
     */
    private void checkAndHandleCategoryBo(SaveCategoryBo categoryBo, boolean editFlag) {
        // 编辑检验id 是否有效
        categoryBo.setParentCategoryId(Optional.ofNullable(categoryBo.getParentCategoryId()).orElse(CommonConstant.DEFAULT_PARENT_ID));
        if (editFlag) {
            Category category = categoryRepository.getById(categoryBo.getId());
            AssertUtil.throwIfTrue(category == null || category.getWhetherDelete(), "类目不存在");
            AssertUtil.throwIfTrue(category.getDefaultStatus(), "默认类目不能编辑");
            categoryBo.setParentCategoryId(category.getParentCategoryId());
        }

        // 操作二、三级类目
        if (categoryBo.getParentCategoryId() > 0) {
            Category parentCategory = categoryRepository.getById(categoryBo.getParentCategoryId());
            AssertUtil.throwIfTrue(parentCategory == null || parentCategory.getWhetherDelete(), "上级类目不存在");

            categoryBo.setDepth(parentCategory.getDepth() + 1);
            categoryBo.setParentPath(parentCategory.getPath());
        }
        // 操作一级类目
        else {
            categoryBo.setDepth(MIN_DEPTH);
            categoryBo.setParentPath("");
        }

        // 校验同级类目名称是否重复
        long count = categoryRepository.count(new LambdaQueryWrapper<Category>().eq(Category::getParentCategoryId, categoryBo.getParentCategoryId()).eq(Category::getName, categoryBo.getName()).eq(Category::getWhetherDelete, Boolean.FALSE).ne(editFlag, Category::getId, categoryBo.getId()));
        AssertUtil.throwIfTrue(count > 0, "该类目下已存在相同名称的类目");

        // 如果操作的是三级类目 佣金比例为必填
        if (MAX_DEPTH.equals(categoryBo.getDepth())) {
            AssertUtil.throwIfNull(categoryBo.getCommissionRate(), "三级类目佣金比例不能为空");
            AssertUtil.throwIfTrue(categoryBo.getCommissionRate().compareTo(BigDecimal.ZERO) < 0, "佣金比例不能小于0");
        } else {
            categoryBo.setCommissionRate(DEFAULT_COMMISSION_RATE);
        }

        // 新增类目 重新计算排序
        if (!editFlag) {
            Long maxDisplaySequence = categoryRepository.getMaxDisplaySequence(categoryBo.getParentCategoryId());
            categoryBo.setDisplaySequence(maxDisplaySequence == null ? 0 : maxDisplaySequence + 1);
        }

        // 给个默认图
        if (StrUtil.isEmpty(categoryBo.getIcon())) {
            categoryBo.setIcon(CommonConstant.DEFAULT_IMAGE);
        }
    }

    /**
     * 检查并处理类目更新入参
     *
     * @param categoryBo 类目参数
     */
    private void checkAndHandleForUpdateCategoryParam(SaveCategoryBo categoryBo) {
        Category category = categoryRepository.getById(categoryBo.getId());
        AssertUtil.throwIfTrue(category == null || category.getWhetherDelete(), "类目不存在");
        categoryBo.setParentCategoryId(category.getParentCategoryId());
        categoryBo.setDepth(category.getDepth());

        // 不能跟上级类目同名
        if (StringUtils.isNotEmpty(categoryBo.getName())) {
            if (categoryBo.getParentCategoryId() > 0) {
                Category parentCategory = categoryRepository.getById(categoryBo.getParentCategoryId());
                AssertUtil.throwIfTrue(parentCategory == null || parentCategory.getWhetherDelete(), "上级类目不存在");
            }

            // 校验同级类目名称是否重复
            long count = categoryRepository.count(new LambdaQueryWrapper<Category>().eq(Category::getParentCategoryId, categoryBo.getParentCategoryId()).eq(Category::getName, categoryBo.getName()).eq(Category::getWhetherDelete, Boolean.FALSE).ne(Category::getId, categoryBo.getId()));
            AssertUtil.throwIfTrue(count > 0, "该类目下已存在相同名称的类目");
        }

        // 如果不是三级类目 一二级分类分佣比例为100%且不能设置为特殊类目
        if (ObjectUtils.notEqual(MAX_DEPTH, category.getDepth())) {
            categoryBo.setCommissionRate(DEFAULT_COMMISSION_RATE);
        }
    }

    /**
     * 重置显示状态(新增/编辑时使用)
     * <p>
     * 1 开启的话, 则开启当前类目上级以及所有下级的显示状态
     * 2 如果是关闭的话, 则关闭当前下级所有的显示状态, 然后判断上级是否还有开启的下级, 如果没有则关闭上级
     *
     * @param categoryId    类目id
     * @param newShowStatus 新的显示状态 true-显示 false-关闭
     */
    private void resetShowStatus(Long categoryId, Boolean newShowStatus) {
        if (newShowStatus == null) {
            return;
        }

        Category category = categoryRepository.getById(categoryId);
        if (category == null || category.getWhetherDelete()) {
            return;
        }

        List<Long> childIds = getChildIds(categoryId);
        childIds.add(categoryId);
        // 开启状态
        if (newShowStatus) {
            List<Long> allIds = new ArrayList<>(childIds);
            allIds.addAll(categoryAssist.getParentIds(category.getPath()));

            categoryRepository.updateShowStatus(allIds, true);
            return;
        }

        // 关闭状态
        categoryRepository.updateShowStatus(childIds, false);
        resetSelfShowStatus(category.getParentCategoryId());
    }

    /**
     * 重置本身的显示状态
     *
     * @param categoryId 类目id
     */
    private void resetSelfShowStatus(Long categoryId) {
        if (categoryId == null || categoryId <= 0) {
            return;
        }

        Category category = categoryRepository.getById(categoryId);
        if (category == null) {
            return;
        }

        long count = categoryRepository.count(new LambdaQueryWrapper<Category>().eq(Category::getParentCategoryId, categoryId).eq(Category::getWhetherShow, Boolean.TRUE).eq(Category::getWhetherDelete, Boolean.FALSE));
        categoryRepository.updateShowStatus(Arrays.asList(categoryId), count > 0);
        // 如果是上级被隐藏, 需要继续去检查一下上级的上级
        if (count == 0) {
            resetSelfShowStatus(category.getParentCategoryId());
        }
    }

    /**
     * 更新是否有下级
     *
     * @param id 类目Id
     */
    private void updateHasChildrenStatus(Long id) {
        if (id == null || id <= 0) {
            return;
        }

        long count = categoryRepository.count(new LambdaQueryWrapper<Category>().eq(Category::getParentCategoryId, id).eq(Category::getWhetherDelete, Boolean.FALSE));
        Category parentCategory = new Category();
        parentCategory.setId(id);
        parentCategory.setHasChildren(count > 0);
        categoryRepository.updateById(parentCategory);
    }

    /**
     * 查询下级id的集合（递归）
     *
     * @param parentId 上级id
     * @return 下级id的集合
     */
    private List<Long> getChildIds(Long parentId) {
        List<Long> childIds = new ArrayList<>();
        recursiveChildIds(Arrays.asList(parentId), childIds, MIN_DEPTH);
        return childIds;
    }

    /**
     * 递归计算下级id的集合
     *
     * @param curParentIds 当前上级id的集合
     * @param childIds     存储所有下级id的集合
     * @param curDepth     当前深度
     */
    private void recursiveChildIds(List<Long> curParentIds, List<Long> childIds, Integer curDepth) {
        if (curDepth > MAX_DEPTH || CollectionUtils.isEmpty(curParentIds)) {
            return;
        }

        List<Long> curChildIds = categoryRepository.listByParentIds(curParentIds).stream().map(Category::getId).collect(Collectors.toList());

        childIds.addAll(curChildIds);
        recursiveChildIds(curChildIds, childIds, curDepth + 1);
    }

    /**
     * 构建类目查询条件
     *
     * @param queryBo 查询参数
     */
    private LambdaQueryWrapper<Category> commonBuilderCategoryQueryWrapper(CategoryQueryBo queryBo) {
        LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper<>();
        if (queryBo == null) {
            return wrapper;
        }

        Integer showStatus = queryBo.getShowStatus();
        if (showStatus != null && !showStatus.equals(ShowStatusEnum.SHOW_ALL.getValue())) {
            wrapper.eq(Category::getWhetherShow, showStatus.equals(ShowStatusEnum.SHOW_OPEN.getValue()));
        }

        if (queryBo.getRecursiveParentId() != null) {
            List<Long> childIds = getChildIds(queryBo.getRecursiveParentId());
            childIds.add(-1L);
            wrapper.in(Category::getId, childIds);
        }

        List<Long> categoryIds = queryBo.getIds();
        if (CollectionUtils.isNotEmpty(categoryIds)) {
            categoryIds = categoryIds.stream().distinct().collect(Collectors.toList());
        }

        // 已经在入参检测 ids的数量不能超过200
        wrapper.in(CollectionUtils.isNotEmpty(categoryIds), Category::getId, categoryIds).like(!StringUtils.isEmpty(queryBo.getNameLike()), Category::getName, queryBo.getNameLike()).eq(queryBo.getParentId() != null, Category::getParentCategoryId, queryBo.getParentId()).eq(queryBo.getDepth() != null, Category::getDepth, queryBo.getDepth()).le(queryBo.getMaxDepth() != null, Category::getDepth, queryBo.getMaxDepth()).eq(queryBo.getWhetherShow() != null, Category::getWhetherShow, queryBo.getWhetherShow()).eq(queryBo.getHasChildren() != null, Category::getHasChildren, queryBo.getHasChildren()).in(CollectionUtils.isNotEmpty(queryBo.getCustomerFormIds()), Category::getCustomFormId, queryBo.getCustomerFormIds()).eq(Category::getWhetherDelete, Boolean.FALSE);
        return wrapper;
    }

    /**
     * 初始化类目信息
     * 1.初始化完整的类目路径信息
     * 2.初始化自定义表单信息
     * 3.初始化保证金信息
     *
     * @param categoryBoList 类目集合
     */
    private void initCategoryData(List<CategoryBo> categoryBoList) {
        if (CollectionUtils.isEmpty(categoryBoList)) {
            return;
        }

        initFullCategory(categoryBoList);
        initFormName(categoryBoList);
        initCashDeposit(categoryBoList);
    }

    /**
     * 初始化完整的类目信息
     *
     * @param categoryBoList 类目集合
     */
    private void initFullCategory(List<CategoryBo> categoryBoList) {
        if (CollectionUtils.isEmpty(categoryBoList)) {
            return;
        }

        Set<Long> parentIds = new HashSet<>();
        categoryBoList.forEach(categoryBo -> {
            List<Long> curParentIds = Arrays.stream(categoryBo.getPath().split(CommonConstant.CATEGORY_PATH_SPLIT)).map(Long::parseLong).collect(Collectors.toList());
            categoryBo.setParentIds(curParentIds.stream().filter(item -> !item.equals(categoryBo.getId())).collect(Collectors.toList()));
            categoryBo.setFullIds(curParentIds);
            parentIds.addAll(curParentIds);
        });

        // 查询上级类目的名称
        Map<Long, String> categoryNameMap = categoryBoList.stream().collect(Collectors.toMap(CategoryBo::getId, CategoryBo::getName, (k1, k2) -> k1));
        Set<Long> existParentIds = categoryNameMap.keySet();
        List<Long> notExistParentIds = parentIds.stream().filter(parentId -> !existParentIds.contains(parentId)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notExistParentIds)) {
            categoryNameMap.putAll(categoryRepository.getCategoryNameMap(notExistParentIds));
        }

        // 查询上级类目的集合
        categoryBoList.forEach(categoryBo -> {
            List<String> parentNames = categoryBo.getParentIds().stream().map(id -> categoryNameMap.get(id)).collect(Collectors.toList());
            parentNames.add(categoryBo.getName());
            categoryBo.setFullCategoryName(String.join(CommonConstant.CATEGORY_FULL_NAME_SPLIT, parentNames));
            categoryBo.setCategoryNameList(parentNames);
        });
    }

    /**
     * 计算保证金
     *
     * @param categoryBoList 类目集合
     */
    private void initCashDeposit(List<CategoryBo> categoryBoList) {
        if (CollectionUtils.isEmpty(categoryBoList)) {
            return;
        }

        List<Long> categoryIds = categoryBoList.stream().filter(item -> CollectionUtils.isNotEmpty(item.getFullIds())).map(item -> item.getFullIds().get(0)).collect(Collectors.toList());
        Map<Long, CategoryCashDeposit> categoryCashDepositMap = categoryCashDepositRepository.getCategoryCashDepositMap(categoryIds);
        categoryBoList.forEach(bo -> {
            List<Long> fullIds = bo.getFullIds();
            if (CollectionUtils.isEmpty(fullIds)) {
                return;
            }
            // 从以及分类提取保证金
            Long rootId = fullIds.get(0);
            CategoryCashDeposit categoryCashDeposit = categoryCashDepositMap.get(rootId);
            bo.setCashDeposit(categoryCashDeposit == null ? BigDecimal.ZERO : categoryCashDeposit.getNeedPayCashDeposit());
            bo.setEnableNoReasonReturn(categoryCashDeposit == null ? Boolean.FALSE : categoryCashDeposit.getEnableNoReasonReturn());
        });
    }

    /**
     * 初始化表单名称
     *
     * @param categoryBoList 类目集合
     */
    private void initFormName(List<CategoryBo> categoryBoList) {
        if (CollectionUtils.isEmpty(categoryBoList)) {
            return;
        }

        List<Long> formIds = categoryBoList.stream().filter(item -> item.getCustomFormId() > 0).map(CategoryBo::getCustomFormId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(formIds)) {
            return;
        }

        Map<Long, String> formNameMap = remoteCustomFormService.getCustomerFormNameMap(formIds);
        categoryBoList.forEach(categoryBo -> {
            String formName = formNameMap.getOrDefault(categoryBo.getCustomFormId(), "");

            categoryBo.setCustomFormName(formName);
            if (StringUtils.isEmpty(formName)) {
                categoryBo.setCustomFormId(CommonConstant.DEFAULT_FORM_ID);
            }
        });
    }

    /**
     * 构建类目树
     *
     * @param categoryBoList 类目集合
     * @param queryBo        查询参数
     * @return 类目树
     */
    private List<CategoryBo> buildCategoryTree(List<CategoryBo> categoryBoList, CategoryQueryBo queryBo) {
        Long rootId = queryBo.getParentId();
        Boolean filterNoChildren = queryBo.getFilterNoChildren();
        Boolean excludeNoSale = queryBo.getExcludeNoSale();

        // 过滤掉没有销售商品的类目
        if (Boolean.TRUE.equals(excludeNoSale)) {
            categoryBoList = getExcludeNoSaleCategory(categoryBoList);
        }

        // 过滤掉没有子类目的类目
        if (Boolean.TRUE.equals(filterNoChildren)) {
            categoryBoList = getHasLeafCategoryList(categoryBoList);
        }

        // 初始化表单信息
        initCategoryData(categoryBoList);

        categoryBoList.sort(Comparator.comparing(CategoryBo::getDisplaySequence).thenComparing(CategoryBo::getId, Comparator.reverseOrder()));
        Map<Long, List<CategoryBo>> categoryMap = categoryBoList.stream().collect(Collectors.groupingBy(CategoryBo::getParentCategoryId));
        // 构建树
        categoryBoList.forEach(item -> {
            List<CategoryBo> children = categoryMap.get(item.getId());
            item.setChildren(children);
        });

        rootId = Optional.ofNullable(rootId).orElse(CommonConstant.DEFAULT_PARENT_ID);
        List<CategoryBo> rootList = categoryMap.getOrDefault(rootId, Collections.EMPTY_LIST);
        return rootList;
    }

    /**
     * 提取出有叶子节点的类目
     */
    private List<CategoryBo> getHasLeafCategoryList(List<CategoryBo> categoryBoList) {
        if (CollectionUtils.isEmpty(categoryBoList)) {
            return Collections.EMPTY_LIST;
        }

        List<Long> mateCategoryIds = categoryBoList.stream().filter(item -> MAX_DEPTH.equals(item.getDepth())).flatMap(item -> getFullIds(item.getPath()).stream()).distinct().collect(Collectors.toList());

        return categoryBoList.stream().filter(item -> mateCategoryIds.contains(item.getId())).collect(Collectors.toList());
    }

    private List<CategoryBo> getExcludeNoSaleCategory(List<CategoryBo> categoryBoList) {
        if (CollectionUtils.isEmpty(categoryBoList)) {
            return new ArrayList<>();
        }

        // 获取最后以及类目的id
        List<Long> lastCategoryIds = categoryBoList.stream().map(CategoryBo::getId).collect(Collectors.toList());
        Map<Long, Long> countMap = esProductService.countByCategoryIds(lastCategoryIds);

        return categoryBoList.stream().filter(category -> {
            // 如果不是三级类目 直接返回
            if (!MAX_DEPTH.equals(category.getDepth())) {
                return true;
            }

            // 过滤出关联了商品的类目
            Long count = countMap.get(category.getId());
            return count != null && count > 0;
        }).collect(Collectors.toList());
    }

    /**
     * 提取完整类目id的集合
     *
     * @param categoryList 类目集合
     * @return 完整类目id的集合
     */
    private List<Long> getFullIds(List<Category> categoryList) {
        if (CollectionUtils.isEmpty(categoryList)) {
            return Collections.EMPTY_LIST;
        }

        Set<Long> fullIds = new HashSet<>();
        categoryList.forEach(category -> {
            fullIds.addAll(getFullIds(category.getPath()));
        });
        return fullIds.stream().collect(Collectors.toList());
    }

    /**
     * 提取完整类目id的集合
     *
     * @param path 类目路径
     * @return 完整类目id的集合
     */
    private List<Long> getFullIds(String path) {
        if (StringUtils.isEmpty(path)) {
            return new ArrayList<>();
        }

        String[] pathArr = path.split(CommonConstant.CATEGORY_PATH_SPLIT);
        if (pathArr.length > 0) {
            return Arrays.stream(pathArr).map(Long::parseLong).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 提取第一级类目id
     *
     * @param path 类目路径
     * @return 第一级类目id
     */
    private Long getFirstId(String path) {
        if (StringUtils.isEmpty(path)) {
            return null;
        }

        String[] pathArr = path.split(CommonConstant.CATEGORY_PATH_SPLIT);
        if (pathArr.length > 0) {
            return Long.parseLong(pathArr[0]);
        }
        return null;
    }

    /**
     * 提取第一级类目id的集合
     *
     * @param categoryList 类目集合
     * @return 第一级类目id的集合
     */
    private List<Long> getFirstIds(List<Category> categoryList) {
        if (CollectionUtils.isEmpty(categoryList)) {
            return new ArrayList<>();
        }

        Set<Long> firstIds = new HashSet<>();
        categoryList.forEach(category -> {
            Long firstId = getFirstId(category.getPath());
            if (firstId == null) {
                return;
            }
            firstIds.add(firstId);
        });
        return new ArrayList<>(firstIds);
    }

    /**
     * 根据类目获得所有类目全名
     */
    @Override
    public List<CategoryResp> getAllCategoryPath(List<Long> threeIds) {
        if (CollectionUtils.isEmpty(threeIds)) {
            return new ArrayList<>();
        }
        List<Category> categoryList = MybatisUtil.queryBatch(ids -> categoryRepository.getAllCategoryPath(threeIds), threeIds);
        List<CategoryResp> categoryResp = JsonUtil.copyList(categoryList, CategoryResp.class);
        //判断不为空
        if (CollectionUtils.isEmpty(categoryResp)) {
            return new ArrayList<>();
        }
        categoryResp.forEach(v -> {
            v.setFullCategoryName(v.getPath());
        });
        return categoryResp;
    }

    @Override
    public List<CategoryCacheBo> listCategoryCache() {
        byte[] arr = (byte[]) squirrelUtil.get(CacheConstant.ALL_CATEGORY_CACHE);
        if (arr == null) {
            List<Category> categoryList = categoryRepository.list(new LambdaQueryWrapper<Category>().eq(Category::getWhetherDelete, Boolean.FALSE));
            List<CategoryCacheBo> cacheList = JsonUtil.copyList(categoryList, CategoryCacheBo.class);
            arr = ZipUtil.gzip(JsonUtil.toJsonString(cacheList), CommonConstant.DEFAULT_CHARSET);
            squirrelUtil.set(CacheConstant.ALL_CATEGORY_CACHE, arr, CacheConstant.CATEGORY_CACHE_TIME);
            return cacheList;
        }
        return JsonUtil.parseArray(ZipUtil.unGzip(arr, CommonConstant.DEFAULT_CHARSET), CategoryCacheBo.class);
    }

    @Override
    public void removeCategoryCache() {
        squirrelUtil.deleteKey(CacheConstant.ALL_CATEGORY_CACHE);
    }

    @Override
    public List<CategoryBo> getCategoryList(QueryCategoryByIdsReq req) {
        List<Long> categoryIds = req.getCategoryIds();

        List<CategoryBo> categoryBoList;
        if (req.getUseCache() != null && req.getUseCache()) {
            List<CategoryCacheBo> cacheList = listCategoryCache();
            categoryBoList = JsonUtil.copyList(cacheList, CategoryBo.class);
        } else {
            List<Category> categoryList = categoryRepository.list(new LambdaQueryWrapper<Category>().eq(Category::getWhetherDelete, Boolean.FALSE));
            categoryBoList = JsonUtil.copyList(categoryList, CategoryBo.class);
        }

        initFullCategory(categoryBoList);
        if (CollectionUtils.isEmpty(categoryIds)) {
            return categoryBoList;
        }
        return categoryBoList.stream().filter(item -> categoryIds.contains(item.getId())).collect(Collectors.toList());
    }

    @Override
    public List<Long> getRecursiveChildIds(Long parentId) {
        List<Long> childIds = getChildIds(parentId);
        childIds.add(0, parentId);
        return childIds;
    }

    @Override
    public CategoryBo queryCategoryByNameDepth(CategoryQueryBo queryBo) {
        Category category = categoryRepository.getOne(new LambdaQueryWrapper<Category>().eq(Category::getName, queryBo.getNameLike()).eq(Category::getDepth, queryBo.getDepth()).last("limit 1"));
        if (category != null) {
            return BeanUtil.copyProperties(category, CategoryBo.class);
        } else {
            return null;
        }
    }

    @Override
    public List<Long> queryAlreadyRemoveCategoryIds() {
        List<Category> alreadyRemoveCategoryIds = this.categoryRepository.list(Wrappers.<Category>lambdaQuery().select(Category::getId).eq(Category::getWhetherDelete, Boolean.TRUE));
        return alreadyRemoveCategoryIds.stream().map(Category::getId).collect(Collectors.toList());
    }

    /**
     * 发送类目变动事件
     *
     * @param categoryIds 类目id集合
     * @param changeType  变动类型
     */
    private void sendCategoryChangeEvent(List<Long> categoryIds, CategoryChangeType changeType) {
        if (CollectionUtils.isEmpty(categoryIds) || changeType == null) {
            log.warn("categoryIds or changeType is null, categoryIds:{}, changeType:{}", categoryIds, changeType);
            return;
        }

        categoryIds = categoryIds.stream().distinct().filter(id -> id > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(categoryIds)) {
            log.warn("categoryIds is empty, categoryIds:{}", categoryIds);
            return;
        }

        CategoryChangeEvent event = new CategoryChangeEvent();
        event.setCategoryIds(categoryIds);
        event.setChangeType(changeType);

        SendCategoryChangeEvent sendEvent = new SendCategoryChangeEvent();
        sendEvent.setEventBody(event);
        transactionEventPublisher.publish(sendEvent);
    }
}
