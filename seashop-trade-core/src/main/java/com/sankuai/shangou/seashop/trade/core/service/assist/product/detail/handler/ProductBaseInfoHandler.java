package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.handler;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.product.core.remote.ProductRemoteService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductStatusEnum;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductDetailDto;
import com.sankuai.shangou.seashop.trade.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.AbsProductQueryHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.ProductBaseContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductAttributeBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductBaseInfoBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductCategoryBo;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 商品基础信息查询
 *
 * <AUTHOR>
 * @date 2023/12/23 11:58
 */
@Component
@Slf4j
public class ProductBaseInfoHandler extends AbsProductQueryHandler {

    @Resource
    private ProductRemoteService productRemoteService;
    @Resource
    private ShopRemoteService shopRemoteService;

    private static final String[] IGNORE_FIELDS = {"attributeList"};
    private static final String[] RETURN_ATTRIBUTES = {"是否支持退货", "订单确认收货后支持退货", "订单确认收货后不支持退货"};
    private static final String PRODUCT_DATE = "生产日期";
    private static final String SHELF_LIFE = "保质期";
    private static final String BRAND_NAME = "品牌";
    private static final String PRODUCT_CODE = "货号";

    @Override
    public void handle(ProductBaseContext context) {
        log.info("查询商品基础信息, context: {}", context);
        ProductDetailDto productDetail = productRemoteService.getProductDetail(context.getProductId());
        ProductBaseInfoBo productBaseInfoBo = JsonUtil.copy(productDetail, ProductBaseInfoBo.class, IGNORE_FIELDS);
        productBaseInfoBo.setCategoryList(categoryListBuild(productDetail));
        productBaseInfoBo.setAttributeList(attributeListBuild(productDetail));
        productBaseInfoBo.setSaleAble(ProductStatusEnum.ON_SALE.getCode().equals(productDetail.getStatus()));
        productBaseInfoBo.setShopName(shopRemoteService.getShopName(productDetail.getShopId()));
        // productBaseInfoBo.setConsultCount(productRemoteService.getConsultCount(context.getProductId()));
        // 给预估价设置一个默认值
        productBaseInfoBo.setEstimatePrice(productDetail.getMinSalePrice());
        context.setProduct(productBaseInfoBo);
        context.setShopId(productDetail.getShopId());

        // 计算销量 实际销量 + 虚拟销量
        Long saleCount = ObjectUtil.defaultIfNull(productDetail.getSaleCounts(), 0l)
            + ObjectUtil.defaultIfNull(productDetail.getVirtualSaleCounts(), 0l);
        productBaseInfoBo.setSalesCount(saleCount.intValue());
    }

    @Override
    public int order() {
        return 0;
    }

    private List<ProductCategoryBo> categoryListBuild(ProductDetailDto productDetail) {
        List<Long> categoryIds = productDetail.getFullCategoryIds();
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Collections.EMPTY_LIST;
        }
        String[] categoryNameArr = productDetail.getFullCategoryName().split(StrUtil.COMMA);

        List<ProductCategoryBo> categoryList = new ArrayList<>();
        for (int i = 0; i < categoryIds.size(); i++) {
            ProductCategoryBo categoryBo = new ProductCategoryBo();
            categoryBo.setCategoryId(categoryIds.get(i));
            categoryBo.setCategoryName(categoryNameArr.length > i ? categoryNameArr[i] : "");
            categoryList.add(categoryBo);
        }
        return categoryList;
    }

    private List<ProductAttributeBo> attributeListBuild(ProductDetailDto productDetail) {
        List<ProductAttributeBo> attributeBoList = new ArrayList<>();
        // 将保质期等信息也加入属性
        attributeBoList.addAll(buildExtAttribute(productDetail));
        return attributeBoList;
    }

    private List<ProductAttributeBo> buildExtAttribute(ProductDetailDto productDetail) {
        List<ProductAttributeBo> attributeBoList = new ArrayList<>();
        attributeBoList.add(buildBrandAttribute(productDetail));
        attributeBoList.add(buildProductCodeAttribute(productDetail));
        return attributeBoList;
    }

    private ProductAttributeBo buildBrandAttribute(ProductDetailDto productDetail) {
        ProductAttributeBo attributeBo = new ProductAttributeBo();
        attributeBo.setAttributeName(BRAND_NAME);
        String brandName = ObjectUtil.defaultIfNull(productDetail.getBrandName(), StrUtil.EMPTY);
        attributeBo.setAttributeValueList(Collections.singletonList(brandName));
        return attributeBo;
    }

    private ProductAttributeBo buildProductCodeAttribute(ProductDetailDto productDetail) {
        ProductAttributeBo attributeBo = new ProductAttributeBo();
        attributeBo.setAttributeName(PRODUCT_CODE);
        String productCode = ObjectUtil.defaultIfNull(productDetail.getProductCode(), StrUtil.EMPTY);
        attributeBo.setAttributeValueList(Collections.singletonList(productCode));
        return attributeBo;
    }
}
