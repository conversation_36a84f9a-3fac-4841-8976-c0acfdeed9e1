package com.sankuai.shangou.seashop.product.core.service.converter;

import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuMergeBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.domain.Sku;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2023/11/25 11:52
 */
public class ProductSkuConverter {

    public static ProductSkuMergeBo convertToBo(Sku sku, Product product) {
        ProductSkuMergeBo bo = new ProductSkuMergeBo();
        if (sku != null) {
            bo.setSkuAutoId(sku.getId());
            bo.setSkuId(sku.getSkuId());
            bo.setSpec1Value(sku.getSpec1Value());
            bo.setSpec2Value(sku.getSpec2Value());
            bo.setSpec3Value(sku.getSpec3Value());
            bo.setSalePrice(sku.getSalePrice());
            bo.setSkuCode(sku.getSkuCode());
            bo.setMeasureUnit(sku.getMeasureUnit());
        }
        if (product != null) {
            bo.setProductId(product.getProductId());
            bo.setShopId(product.getShopId());
            bo.setCategoryId(product.getCategoryId());
            bo.setCategoryPath(product.getCategoryPath());
            bo.setBrandId(product.getBrandId());
            bo.setProductName(product.getProductName());
            bo.setProductCode(product.getProductCode());
            bo.setSaleStatus(ProductEnum.SaleStatusEnum.getByCode(product.getSaleStatus()));
            bo.setAuditStatus(ProductEnum.AuditStatusEnum.getByCode(product.getAuditStatus()));
            bo.setAddedDate(product.getAddedDate());
            bo.setMarketPrice(product.getMarketPrice());
            bo.setMinSalePrice(product.getMinSalePrice());
            bo.setHasSku(product.getHasSku());
            bo.setFreightTemplateId(product.getFreightTemplateId());
            bo.setWeight(product.getWeight());
            bo.setVolume(product.getVolume());
            bo.setMaxBuyCount(product.getMaxBuyCount());
            bo.setWhetherOpenLadder(product.getWhetherOpenLadder());
            bo.setSpec1Alias(product.getSpec1Alias());
            bo.setSpec2Alias(product.getSpec2Alias());
            bo.setSpec3Alias(product.getSpec3Alias());
            bo.setImagePath(product.getImagePath());
            bo.setMultipleCount(product.getMultipleCount());
            bo.setWhetherDelete(product.getWhetherDelete());
            bo.setMeasureUnit(StrUtil.emptyToDefault(bo.getMeasureUnit(), product.getMeasureUnit()));
        }
        return bo;
    }

}
