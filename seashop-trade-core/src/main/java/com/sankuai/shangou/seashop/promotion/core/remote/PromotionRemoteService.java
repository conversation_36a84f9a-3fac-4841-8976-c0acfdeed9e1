package com.sankuai.shangou.seashop.promotion.core.remote;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.promotion.core.model.bo.QueryShopUserPromotionBo;
import com.sankuai.shangou.seashop.promotion.core.service.PromotionProductService;
import com.sankuai.shangou.seashop.promotion.core.service.PromotionService;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.ShopOrderReductionDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.*;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.CollocationDetailReq;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.DiscountActiveProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.CouponStatusEnum;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.CollocationResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.MallCollocationReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.MallCollocationResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponRecordIdOrSnReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponRecordQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.OrderQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.ProductAvailableQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.PromotionRecordOrderQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.EffectiveFlashSaleQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.QuerySkuFlashSaleReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponRecordSimpleListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponRecordSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponSimpleListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.DiscountActiveResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.EffectiveFlashSaleQueryListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.EffectiveFlashSaleQueryResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FlashSaleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FullReductionResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ShopFlashSaleConfigResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ShopReductionOrderListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ShopUserPromotionResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.SkuFlashSaleDetailResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.coupon.CouponRecordOrderListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.coupon.CouponRecordOrderResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.coupon.ProductAvailableQueryResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CouponQueryFeign;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CouponRecordQueryFeign;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.DiscountActiveQueryFeign;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.ExclusivePriceQueryFeign;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleConfigQueryFeign;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleQueryFeign;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FullReductionQueryFeign;
import com.sankuai.shangou.seashop.trade.common.constant.CommonConst;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.ExclusivePriceBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteCollocationBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteCollocationProductBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteCollocationSkuBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteCouponBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteCouponSimpleBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteDiscountBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteFlashSaleBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteFlashSaleDetailBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteReductionBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteShopUserPromotionBo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PromotionRemoteService {

    @Resource
    private FullReductionQueryFeign fullReductionQueryThriftService;
    @Resource
    private DiscountActiveQueryFeign discountActiveQueryThriftService;
    @Resource
    private FlashSaleQueryFeign flashSaleQueryThriftService;
    @Resource
    private ExclusivePriceQueryFeign exclusivePriceQueryThriftService;
    @Resource
    private CouponQueryFeign couponQueryThriftService;
    @Resource
    private CouponRecordQueryFeign couponRecordQueryThriftService;
    @Resource
    private CollocationQueryFeign collocationQueryThriftService;
    @Resource
    private PromotionProductService promotionProductService;
    @Resource
    private FlashSaleConfigQueryFeign flashSaleConfigQueryThriftService;

    @Resource
    private PromotionService promotionService;
    /**
     * 查询店铺有效的营销信息，如果营销与用户有关，则进一步过滤出对用户有效的
     *
     * @param shopIdList 店铺ID
     * @param userId     商家用户ID
     * @return 营销信息，转成本服务的Bo对象防腐
     * <AUTHOR>
     */
    public List<RemoteShopUserPromotionBo> queryValidShopUserPromotion(List<Long> shopIdList, Long userId) {
        log.info("【营销】查询店铺有效的营销信息, shopIdList={}, userId={}", shopIdList, userId);
        QueryShopUserPromotionBo queryReq = QueryShopUserPromotionBo.builder()
            .shopIdList(shopIdList)
            .userId(userId)
            .build();
        ShopUserPromotionResp resp = promotionService.queryPromotionData(queryReq);
        log.info("【营销】查询店铺有效的营销信息, resp={}", JsonUtil.toJsonString(resp));
        if (resp == null || CollUtil.isEmpty(resp.getShopUserPromotionList())) {
            return Collections.emptyList();
        }
        return JsonUtil.copyList(resp.getShopUserPromotionList(), RemoteShopUserPromotionBo.class);
    }


    //******************************start: 满减相关**********************************************

    /**
     * 根据满减活动ID查询满减活动信息，转成本服务的Bo对象防腐
     *
     * @param reductionActivityId 满减活动ID
     * @return 满减活动信息，转成本服务的Bo对象防腐
     */
    public RemoteReductionBo getReductionById(Long reductionActivityId) {
        log.info("【营销】查询满减活动信息, reductionActivityId={}", reductionActivityId);
        QueryFullReductionDetailReq queryReq = new QueryFullReductionDetailReq();
        queryReq.setId(reductionActivityId);
        FullReductionResp resp = ThriftResponseHelper.executeThriftCall(
            () -> fullReductionQueryThriftService.getById(queryReq)
        );
        log.info("【营销】查询满减活动信息, resp={}", JsonUtil.toJsonString(resp));
        RemoteReductionBo reduction = JsonUtil.copy(resp, RemoteReductionBo.class);
        reduction.setActiveId(resp.getId());
        return reduction;
    }

    /**
     * 根据店铺ID查询满减活动信息，转成本服务的Bo对象防腐
     *
     * @param shopId 店铺ID
     * @return 满减活动信息，转成本服务的Bo对象防腐
     */
    public RemoteReductionBo getFullReduction(Long shopId) {
        ShopIdReq req = new ShopIdReq();
        req.setShopId(shopId);
        FullReductionResp resp = ThriftResponseHelper.executeThriftCall(() -> fullReductionQueryThriftService.queryByShopId(req));
        return JsonUtil.copy(resp, RemoteReductionBo.class);
    }

    //******************************end: 满减相关**********************************************


    //******************************start: 折扣相关**********************************************

    /**
     * 根据折扣活动ID查询折扣活动信息，转成本服务的Bo对象防腐
     *
     * @param discountActivityId 折扣活动ID
     * @return 折扣活动信息，转成本服务的Bo对象防腐
     */
    public RemoteDiscountBo getDiscountById(Long discountActivityId) {
        log.info("【营销】查询折扣活动信息, discountActivityId={}", discountActivityId);
        BaseIdReq queryReq = new BaseIdReq();
        queryReq.setId(discountActivityId);
        DiscountActiveResp resp = ThriftResponseHelper.executeThriftCall(
            () -> discountActiveQueryThriftService.getById(queryReq)
        );
        log.info("【营销】查询折扣活动信息, resp={}", JsonUtil.toJsonString(resp));
        RemoteDiscountBo discount = convertToDiscountBo(resp);
        log.info("【营销】转换成本地对象后, discount={}", discount);
        return discount;
    }

    /**
     * 根据店铺ID查询折扣活动信息，转成本服务的Bo对象防腐
     *
     * @param shopId 店铺ID
     * @return 折扣活动信息，转成本服务的Bo对象防腐
     */
    public RemoteDiscountBo getDiscountActive(Long productId, Long shopId) {
        ProductAndShopIdReq req = new ProductAndShopIdReq();
        req.setProductId(productId);
        req.setShopId(shopId);
        DiscountActiveResp resp = ThriftResponseHelper.executeThriftCall(() -> discountActiveQueryThriftService.queryByProductId(req));
        return convertToDiscountBo(resp);
    }

    private RemoteDiscountBo convertToDiscountBo(DiscountActiveResp resp) {
        if (resp == null) {
            return null;
        }
        RemoteDiscountBo discount = JsonUtil.copy(resp, RemoteDiscountBo.class);
        discount.setDiscountActId(resp.getId());
        discount.setDiscountActName(resp.getActiveName());
        List<Long> productIdList = Optional.ofNullable(resp.getProductList())
            .map(list -> list.stream().map(DiscountActiveProductDto::getProductId).collect(Collectors.toList()))
            .orElse(null);
        discount.setProductIdList(productIdList);
        return discount;
    }

    //******************************end: 折扣相关**********************************************


    //****************************** start: 限时购相关**********************************************

    /**
     * 根据商品ID查询限时抢购活动信息，转成本服务的Bo对象防腐
     *
     * @param productId 商品ID
     * @param shopId    店铺ID
     * @return 限时抢购活动信息，转成本服务的Bo对象防腐
     */
    public RemoteFlashSaleBo getFlashSale(Long productId, Long shopId) {
        ProductAndShopIdReq req = new ProductAndShopIdReq();
        req.setProductId(productId);
        req.setShopId(shopId);
        FlashSaleResp resp = ThriftResponseHelper.executeThriftCall(() -> flashSaleQueryThriftService.queryByProductId(req));
        RemoteFlashSaleBo saleBo = JsonUtil.copy(resp, RemoteFlashSaleBo.class);
        // 计算开始倒计时和结束倒计时
        if (saleBo != null) {
            long now = System.currentTimeMillis();
            saleBo.setStartCountDown(saleBo.getBeginDate().getTime() > now ? saleBo.getBeginDate().getTime() - now : 0);
            saleBo.setEndCountDown(saleBo.getEndDate().getTime() > now ? saleBo.getEndDate().getTime() - now : 0);
            saleBo.setFlashSaleActivityId(saleBo.getId());
        }
        return saleBo;
    }

    /**
     * 查询SKU限时购信息，转成本服务的Bo对象防腐
     *
     * @param flashSaleId 限时购ID
     * @param productId   商品ID
     * @param skuId       skuID
     * @return 限时购信息，转成本服务的Bo对象防腐
     */
    public RemoteFlashSaleDetailBo getSkuFlashSaleDetail(Long flashSaleId, Long productId, String skuId) {
        QuerySkuFlashSaleReq req = new QuerySkuFlashSaleReq();
        req.setFlashSaleId(flashSaleId);
        req.setProductId(productId);
        req.setSkuId(skuId);
        log.info("【营销】查询限时购信息, req={}", JsonUtil.toJsonString(req));
        SkuFlashSaleDetailResp resp = ThriftResponseHelper.executeThriftCall(
            () -> flashSaleQueryThriftService.queryValidWithSkuId(req)
        );
        log.info("【营销】查询限时购信息, resp={}", JsonUtil.toJsonString(resp));
        return JsonUtil.copy(resp, RemoteFlashSaleDetailBo.class);
    }

    //****************************** end: 限时购相关**********************************************


    //****************************** start: 专享价相关**********************************************

    /**
     * 根据商品ID查询专享价活动信息，转成本服务的Bo对象防腐
     *
     * @param productId 商品ID
     * @param userId    用户ID
     * @return 专享价活动信息，转成本服务的Bo对象防腐
     */
    public ExclusivePriceBo getExclusivePrice(Long productId, Long userId) {
        ProductAndMemberIdReq req = new ProductAndMemberIdReq();
        req.setProductId(productId);
        req.setMemberId(userId);
        ExclusivePriceResp resp = ThriftResponseHelper.executeThriftCall(() -> exclusivePriceQueryThriftService.queryByProductAndMemberId(req));
        return JsonUtil.copy(resp, ExclusivePriceBo.class);
    }

    //****************************** end: 专享价相关**********************************************


    //****************************** start: 优惠券相关**********************************************

    /**
     * 根据商品ID查询优惠券信息，转成本服务的Bo对象防腐
     *
     * @param productId 商品ID
     * @param shopId    店铺ID
     * @return 优惠券信息，转成本服务的Bo对象防腐
     */
    public List<RemoteCouponSimpleBo> getCouponList(Long productId, Long shopId, Long userId) {
        ProductAndShopIdReq req = new ProductAndShopIdReq();
        req.setProductId(productId);
        req.setShopId(shopId);
        req.setUserId(userId);
        CouponSimpleListResp resp = ThriftResponseHelper.executeThriftCall(() -> couponQueryThriftService.queryByProductId(req));
        return JsonUtil.copyList(resp.getCouponList(), RemoteCouponSimpleBo.class);
    }

    public RemoteCouponBo getCouponByRecordId(Long couponRecordId) {
        log.info("【营销】查询优惠券信息, couponId={}", couponRecordId);
        CouponRecordIdOrSnReq queryReq = new CouponRecordIdOrSnReq();
        queryReq.setRecordIdList(Collections.singletonList(couponRecordId));
        CouponRecordSimpleListResp resp = ThriftResponseHelper.executeThriftCall(() -> couponRecordQueryThriftService.getByIdOrSn(queryReq));
        log.info("【营销】查询优惠券信息, resp={}", JsonUtil.toJsonString(resp));
        if (resp == null || CollUtil.isEmpty(resp.getList())) {
            return null;
        }
        // 记录ID是用户领取的后的领取记录ID，唯一的
        return JsonUtil.copy(resp.getList().get(0), RemoteCouponBo.class);
    }

    /**
     * 查询用户有效的优惠券信息
     * 营销接口提供的是分页接口，分页参数设置大一点
     *
     * @param userId 用户ID
     * @return 优惠券信息，转成本服务的Bo对象防腐
     */
    public List<RemoteCouponBo> getUserValidCouponList(Long userId) {
        log.info("【营销】查询用户有效的优惠券信息, userId={}", userId);
        CouponRecordQueryReq queryReq = new CouponRecordQueryReq();
        queryReq.setUserId(userId);
        queryReq.setPageNo(CommonConst.PAGE_ALL_PAGE_NO);
        queryReq.setPageSize(CommonConst.PAGE_ALL_PAGE_SIZE);
        // 查询有效的，固定状态
        queryReq.setStatus(CouponStatusEnum.NOT_USED.getCode());
        log.info("【营销】查询用户有效的优惠券信息, queryReq={}", JsonUtil.toJsonString(queryReq));
        BasePageResp<CouponRecordSimpleResp> resp = ThriftResponseHelper.executeThriftCall(() -> couponRecordQueryThriftService.pageList(queryReq));
        log.info("【营销】查询用户有效的优惠券信息, resp={}", JsonUtil.toJsonString(resp));
        if (resp == null || CollUtil.isEmpty(resp.getData())) {
            return Collections.emptyList();
        }
        return JsonUtil.copyList(resp.getData(), RemoteCouponBo.class);
    }

    /**
     * 已订单+商品维度获取用户有效的优惠券，用于判断订单预览页是否显示优惠券选择框
     *
     * @param userId 用户ID
     */
    public List<CouponRecordOrderResp> getValidCouponByOrder(Long userId, List<OrderQueryReq> shopOrderList) {
        PromotionRecordOrderQueryReq req = new PromotionRecordOrderQueryReq();
        req.setUserId(userId);
        req.setOrderList(shopOrderList);
        CouponRecordOrderListResp resp = ThriftResponseHelper.executeThriftCall(() -> couponRecordQueryThriftService.getRecordByOrder(req));
        if (resp == null) {
            return Collections.emptyList();
        }
        return resp.getList();
    }

    /**
     * 已订单+商品维度获取用户有效的优惠券并转成map，用于判断订单预览页是否显示优惠券选择框
     *
     * @param userId 用户ID
     */
    public Map<Long, List<CouponRecordSimpleResp>> getValidCouponByOrderToMap(Long userId, List<OrderQueryReq> shopOrderList) {
        List<CouponRecordOrderResp> list = getValidCouponByOrder(userId, shopOrderList);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(CouponRecordOrderResp::getShopId, CouponRecordOrderResp::getRecordList));
    }

    /**
     * 查询优惠券关联的商品ID
     *
     * @param couponId 优惠券ID
     * @return 商品ID列表
     */
    public List<Long> getRelateProductIds(Long couponId) {
        return ThriftResponseHelper.executeThriftCall(() -> couponQueryThriftService.queryRelateProductIds(couponId));
    }

    //****************************** end: 优惠券相关**********************************************


    //****************************** start: 组合购相关**********************************************

    /**
     * 根据店铺ID查询限时组合购活动信息，转成本服务的Bo对象防腐
     *
     * @param shopId 店铺ID
     * @return 组合购活动信息，转成本服务的Bo对象防腐
     */
    public List<RemoteCollocationBo> getCollectionBuy(Long productId, Long shopId) {
        MallCollocationReq req = new MallCollocationReq();
        req.setProductId(productId);
        req.setShopId(shopId);
        MallCollocationResp resp = ThriftResponseHelper.executeThriftCall(() -> collocationQueryThriftService.queryMallCollocationList(req));
        return JsonUtil.copyList(resp.getCollocationRespList(), RemoteCollocationBo.class, (source, target) -> {
            List<RemoteCollocationProductBo> productList = target.getProductRespList();
            if (CollectionUtils.isEmpty(productList)) {
                return;
            }
            productList.forEach(product -> {
                product.setSkuIds(product.getSkuRespList().stream().map(RemoteCollocationSkuBo::getSkuId).collect(Collectors.toList()));
            });
        });
    }

    /**
     * 根据组合购活动ID查询组合购信息，转成本服务的Bo对象防腐
     *
     * @param collocationId 组合购活动ID
     * @return 组合购活动信息，转成本服务的Bo对象防腐
     */
    public RemoteCollocationBo getCollocationById(Long collocationId) {
        CollocationDetailReq req = new CollocationDetailReq();
        req.setId(collocationId);
        log.info("【营销】查询组合购信息, collocationId={}", collocationId);
        CollocationResp resp = ThriftResponseHelper.executeThriftCall(() -> collocationQueryThriftService.queryCollocationDetail(req));
        log.info("【营销】查询组合购信息, resp={}", JsonUtil.toJsonString(resp));
        return JsonUtil.copy(resp, RemoteCollocationBo.class);
    }

    //****************************** end: 组合购相关**********************************************

    /**
     * 获取 订单可用的券列表，最优惠的最上面
     * @param req
     * @return
     */
    public List<CouponRecordOrderResp> getRecordByOrder(PromotionRecordOrderQueryReq req) {
        if (req.getOrderList().isEmpty()){
            return Collections.emptyList();
        }
        CouponRecordOrderListResp resp = ThriftResponseHelper.executeThriftCall(() ->
            couponRecordQueryThriftService.getRecordByOrder(req));
        return Optional.ofNullable(resp)
            .map(CouponRecordOrderListResp::getList)
            .filter(CollectionUtil::isNotEmpty)
            .orElse(Collections.emptyList());
    }

    /**
     * 获取订单可用的满减列表
     * @param req
     * @return
     */
    public List<ShopOrderReductionDto> queryShopPromotionByOrder(PromotionRecordOrderQueryReq req) {
        if (req.getOrderList().isEmpty()){
            return Collections.emptyList();
        }
        ShopReductionOrderListResp resp = promotionService.queryShopPromotionByOrder(req);
        return Optional.ofNullable(resp).map(ShopReductionOrderListResp::getList).orElse(Collections.emptyList());
    }

    public List<Long> collocationFlashSaleProductId (Long shopId) {
        return promotionProductService.collocationFlashSaleProductIdList(shopId);
    }

    /**
     * 查询限时购配置
     *
     * @param shopId 店铺id
     * @return
     */
    public ShopFlashSaleConfigResp getShopConfig(Long shopId) {
        ShopIdReq shopIdReq = new ShopIdReq();
        shopIdReq.setShopId(shopId);
        return ThriftResponseHelper.executeThriftCall(() -> flashSaleConfigQueryThriftService.getShopConfig(shopIdReq));
    }

    /**
     * 获取店铺下商品可用的优惠券列表，并根据用户使用情况返回，即如果用户已经用了，且只能用一张，不返回
     *
     * @param userId     用户ID
     * @param productMap 店铺下的商品列表
     * <AUTHOR>
     */
    public Map<Long, List<CouponSimpleResp>> getProductMatchCouponWithUserLimit(Long userId, Map<Long, List<Long>> productMap) {
        ProductAvailableQueryReq req = new ProductAvailableQueryReq();
        req.setUserId(userId);
        req.setProductMap(productMap);
        ProductAvailableQueryResp resp = ThriftResponseHelper.executeThriftCall(() -> couponQueryThriftService.getProductAvailableCouponList(req));
        if (resp == null) {
            return Collections.emptyMap();
        }
        return resp.getProductCouponMap();
    }

    /**
     * 根据商品ID获取商品匹配的有效的限时购活动
     *
     * @param productIdList 商品ID
     * <AUTHOR>
     */
    public Map<Long, EffectiveFlashSaleQueryResp> getProductMatchFlashSaleToMap(List<Long> productIdList) {
        // 去重
        productIdList = productIdList.stream().distinct().collect(Collectors.toList());
        List<EffectiveFlashSaleQueryResp> list = getProductMatchFlashSale(productIdList);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(EffectiveFlashSaleQueryResp::getProductId, Function.identity(), (oldV, newV) -> newV));
    }

    /**
     * 根据商品ID获取商品匹配的有效的限时购活动
     *
     * @param productIdList 商品ID
     * <AUTHOR>
     */
    public List<EffectiveFlashSaleQueryResp> getProductMatchFlashSale(List<Long> productIdList) {
        // 去重
        productIdList = productIdList.stream().distinct().collect(Collectors.toList());
        EffectiveFlashSaleQueryReq req = new EffectiveFlashSaleQueryReq();
        req.setProductIdList(productIdList);
        req.setFrontFlag(Boolean.TRUE);
        log.info("【营销】查询商品匹配的限时购活动, req={}", JsonUtil.toJsonString(req));
        EffectiveFlashSaleQueryListResp resp = ThriftResponseHelper.executeThriftCall(() -> flashSaleQueryThriftService.queryEffectiveFlashSaleList(req));
        log.info("【营销】查询商品匹配的限时购活动, resp={}", JsonUtil.toJsonString(resp));
        if (resp == null) {
            return null;
        }
        return resp.getList();
    }
}
