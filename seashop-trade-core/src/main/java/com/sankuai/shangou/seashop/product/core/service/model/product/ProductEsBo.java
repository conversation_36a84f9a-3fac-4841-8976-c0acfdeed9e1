package com.sankuai.shangou.seashop.product.core.service.model.product;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/27 9:41
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductEsBo {

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 类目路径
     */
    private String categoryPath;

    /**
     * 类目全路径
     */
    private String fullCategoryName;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 销售状态 1-销售中 2-仓库中 3-草稿箱
     */
    private ProductEnum.SaleStatusEnum saleStatus;

    /**
     * 审核状态 1-待审核 2-销售中 3-未通过 4-违规下架
     */
    private ProductEnum.AuditStatusEnum auditStatus;

    /**
     * 添加时间
     */
    private Date addedDate;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 最小销售价
     */
    private BigDecimal minSalePrice;

    /**
     * 是否有sku
     */
    private Boolean hasSku;

    /**
     * 运费模板ID
     */
    private Long freightTemplateId;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 体积
     */
    private BigDecimal volume;

    /**
     * 最大购买数
     */
    private Integer maxBuyCount;

    /**
     * 是否开启阶梯价
     */
    private Boolean whetherOpenLadder;

    /**
     * 规格1 别名
     */
    private String spec1Alias;

    /**
     * 规格2 别名
     */
    private String spec2Alias;

    /**
     * 规格3 别名
     */
    private String spec3Alias;

    /**
     * 商品主图
     */
    private String imagePath;

    /**
     * 倍数起购量
     */
    private Integer multipleCount;

    /**
     * 是否删除
     */
    private Boolean whetherDelete;

    /**
     * 提交审核时间
     */
    private Date submitAuditTime;

    /**
     * 审核时间
     */
    private Date checkTime;

    /**
     * 销售量
     */
    private Long saleCounts;

    /**
     * 图片列表
     */
    private List<String> imgList;

    /**
     * 平台显示顺序
     */
    private Long displaySequence;

    /**
     * 店铺显示顺序
     */
    private Long shopDisplaySequence;

    /**
     * 是否低于警戒库存
     */
    private Boolean whetherBelowSafeStock;

    /**
     * 虚拟销量
     */
    private Long virtualSaleCounts;

    /**
     * 店铺分类id集合
     */
    private List<Long> shopCategoryIds;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 计量单位
     */
    private String measureUnit;

    /**
     * 审核原因
     */
    private String auditReason;

    /**
     * 来源 1-商城 2-牵牛花 3-易久批
     */
    private Integer source;

    /**
     * 规格id的集合
     */
    private List<Long> skuAutoIds;

    /**
     * sku编码集合
     */
    private List<String> skuCodes;

    /**
     * 最大售价(阶梯价和sku价格的最大值)
     */
    private BigDecimal maxSalePrice;

    /**
     * 总库存
     */
    private Long totalStock;

    /**
     * skuId的集合
     */
    private List<String> skuIds;

    /**
     * 广告词
     */
    private String shortDescription;

    /**
     * 规格名称的集合
     */
    private List<Long> specNameIds;

}
