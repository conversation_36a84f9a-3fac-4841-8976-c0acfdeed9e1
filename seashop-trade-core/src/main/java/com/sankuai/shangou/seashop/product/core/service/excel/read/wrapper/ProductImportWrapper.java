package com.sankuai.shangou.seashop.product.core.service.excel.read.wrapper;

import java.util.List;

import com.sankuai.shangou.seashop.base.eimport.DataWrapper;
import com.sankuai.shangou.seashop.product.core.service.excel.read.dto.ProductImportDto;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/11/23 22:51
 */
@AllArgsConstructor
public class ProductImportWrapper extends DataWrapper<ProductImportDto> {

    private List<ProductImportDto> dataList;

    @Override
    public List<ProductImportDto> getDataList() {
        return dataList;
    }

    @Override
    public Integer getModule() {
        return null;
    }

    @Override
    public String getFileName() {
        return "商品导入错误数据";
    }
}
