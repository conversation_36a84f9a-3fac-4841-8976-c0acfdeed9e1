package com.sankuai.shangou.seashop.product.core.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.product.core.service.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.lock.DistributeLock;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.order.thrift.core.OrderQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.ProductCommentQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.response.ShopCommentSummaryResp;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.common.constant.LockConstant;
import com.sankuai.shangou.seashop.product.common.es.model.product.StatusDto;
import com.sankuai.shangou.seashop.product.common.es.service.EsProductService;
import com.sankuai.shangou.seashop.product.common.remote.base.RemoteSiteSettingService;
import com.sankuai.shangou.seashop.product.common.remote.base.model.RemoteProductSettingBo;
import com.sankuai.shangou.seashop.product.common.remote.user.FavoriteProductRemoteService;
import com.sankuai.shangou.seashop.product.common.remote.user.RemoteShopService;
import com.sankuai.shangou.seashop.product.core.service.assist.BizCodeAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.CategoryAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.LadderPriceAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductAuditAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.ShopCategoryAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.SkuAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerProcessor;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.TransactionEventPublisher;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.event.SendProductChangeEvent;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.ProductLogAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.BindDescriptionTemplateLogBo;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.BindFreightTemplateLogBo;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.BindRecommendProductLogBo;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.UpdateSafeStockLogBo;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.UpdateSequenceLogBo;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.UpdateShopSequenceLogBo;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.UpdateStockLogBo;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.UpdateVirtualSalesLogBo;
import com.sankuai.shangou.seashop.product.core.service.assist.sort.SortFieldFactory;
import com.sankuai.shangou.seashop.product.core.service.assist.sort.enums.ProductSortEnum;
import com.sankuai.shangou.seashop.product.core.service.converter.ProductConverter;
import com.sankuai.shangou.seashop.product.core.service.converter.ProductSkuConverter;
import com.sankuai.shangou.seashop.product.core.service.converter.SkuConverter;
import com.sankuai.shangou.seashop.product.core.service.hepler.ProductStatusHelper;
import com.sankuai.shangou.seashop.product.core.service.hepler.SkuIdHelper;
import com.sankuai.shangou.seashop.product.core.service.hepler.StringHelper;
import com.sankuai.shangou.seashop.product.core.service.model.BindDescriptionTemplateBo;
import com.sankuai.shangou.seashop.product.core.service.model.CategoryBo;
import com.sankuai.shangou.seashop.product.core.service.model.StockTaskBo;
import com.sankuai.shangou.seashop.product.core.service.model.StockTaskInfoBo;
import com.sankuai.shangou.seashop.product.core.service.model.erp.ErpLadderPriceBo;
import com.sankuai.shangou.seashop.product.core.service.model.erp.ErpProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.erp.ErpQueryProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.erp.ErpSkuBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductLadderPriceBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductPageBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductQueryBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductRichTextBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuMergeBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuMergeCombinationBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuQueryBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuUpdatePriceBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductUpdateVirtualSaleCountsBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductViolationOffSaleBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.QueryProductRichTextBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductAudit;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductDescription;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductDescriptionTemplate;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductLadderPrice;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductRelationProduct;
import com.sankuai.shangou.seashop.product.dao.core.domain.Sku;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStock;
import com.sankuai.shangou.seashop.product.dao.core.model.AddProductSaleCountDto;
import com.sankuai.shangou.seashop.product.dao.core.model.ShopSaleCountsDto;
import com.sankuai.shangou.seashop.product.dao.core.repository.BrandRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.DescriptionTemplateRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.FirstPageRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductDescriptionRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductImageRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductLadderPriceRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRelationProductRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuStockRepository;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductRichTextEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductSourceEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductStatusEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateKeyEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateTypeEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateWayEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.VirtualSaleCountsTypeEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.erp.ErpTimeType;
import com.sankuai.shangou.seashop.product.thrift.core.event.product.type.ProductChangeType;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.AddSaleCountReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.BindFreightTemplateReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.BindRecommendProductReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.CountProductTemplateReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductDeleteReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductOnOffSaleReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductSkuQueryReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductTemplateReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductUpdateSafeStockReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductUpdateSequenceReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductUpdateStockReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductViolationReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductBasicReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductByIdsReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.RecommendProductsReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.dto.UpdateStockDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.CountProductTemplateResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.MStatisticalProductResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductPageResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductSkuStockResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.RecommendProductsDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.RecommendProductsResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.SaveProductResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.StatisticalProductResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.LadderPriceDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductBasicDto;
import com.sankuai.shangou.seashop.promotion.core.remote.PromotionRemoteService;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/06 11:57
 */
@Service
@Slf4j
public class ProductServiceImpl implements ProductService {

    @Resource
    private ProductRepository productRepository;
    @Resource
    private ProductDescriptionRepository productDescriptionRepository;
    @Resource
    private ProductRelationProductRepository productRelationProductRepository;
    @Resource
    private SkuStockRepository skuStockRepository;
    @Resource
    private SkuRepository skuRepository;
    @Resource
    private BrandRepository brandRepository;
    @Resource
    private ProductHandlerProcessor productHandlerProcessor;
    @Resource
    private ProductAuditAssist productAuditAssist;
    @Resource
    private CategoryService categoryService;
    @Resource
    private ProductLadderPriceRepository productLadderPriceRepository;
    @Resource
    private ProductImageRepository productImageRepository;
    @Resource
    private BizCodeAssist bizCodeAssist;
    @Resource
    private FirstPageRepository firstPageRepository;
    @Resource
    private ProductAssist productAssist;
    @Resource
    private SkuAssist skuAssist;
    @Resource
    private SkuService skuService;
    @Resource
    private SkuStockService skuStockService;
    @Resource
    private LadderPriceAssist ladderPriceAssist;
    @Resource
    private DescriptionTemplateRepository descriptionTemplateRepository;
    @Resource
    private TransactionEventPublisher transactionEventPublisher;
    @Resource
    private ShopCategoryAssist shopCategoryAssist;

    @Resource
    private OrderQueryFeign orderQueryFeign;
    @Resource
    private ProductCommentQueryFeign productCommentQueryFeign;

    @Resource
    private RemoteShopService remoteShopService;
    @Resource
    private BrandService brandService;
    @Resource
    private RemoteSiteSettingService remoteSiteSettingService;
    @Resource
    private ProductLogAssist productLogAssist;

    @Resource
    private FavoriteProductRemoteService favoriteProductRemoteService;
    @Resource
    private EsProductService esProductService;
    @Resource
    private PromotionRemoteService promotionRemoteService;
    @Resource
    private CategoryAssist categoryAssist;

    @Resource
    private ProductEsBuildService productEsBuildService;
    /**
     * 保存商品信息
     *
     * <p>
     * 首先会执行 ProductHandlerType.CHECK_PRODUCT 类型的处理器, 对参数进行校验
     * 然后执行 ProductHandlerType.SAVE_PRODUCT类 型的处理器, 保存商品信息
     * 如果需要审批执行 ProductHandlerType.SUBMIT_PRODUCT_AUDIT类型的处理器, 提交审批
     *
     * @param productBo    商品信息
     * @param changeSource 变更来源
     * @param changeType   变更类型
     * @param partSave     是否是部分保存
     * @param skipRisk     是否跳过风控(风控自动通过)
     */
    @Override
    public SaveProductResp saveProduct(ProductBo productBo,
                                       ProductSourceEnum changeSource,
                                       ProductChangeType changeType,
                                       boolean partSave,
                                       boolean skipRisk) {
        AssertUtil.throwInvalidParamIfNull(productBo.getShopId(), "店铺id不能为空");

        // 构建上下文对象
        ProductContext context = ProductContext.builder()
                .shopId(productBo.getShopId())
                .saveProductBo(productBo)
                .productId(productBo.getProductId())
                .changeSource(changeSource)
                .changeType(changeType)
                .skuUpdateKey(productBo.getSkuUpdateKey())
                .partSave(partSave)
                .draftFlag(ObjectUtils.defaultIfNull(productBo.getDraftFlag(), false))
                .skipRisk(skipRisk)
                .currentShop(remoteShopService.getByShopId(productBo.getShopId()))
                .build();
        if (productBo.getProductId() != null && productBo.getProductId() > 0) {
            context.setEditFlag(Boolean.TRUE);
        }
        else {
            Long productId = bizCodeAssist.getProductId();
            context.setProductId(productId);
            context.getSaveProductBo().setProductId(productId);
        }

        log.info("【保存商品】构建上下文对象, context:{}", context);

        // 检验并处理商品参数
        productHandlerProcessor.handle(ProductHandlerType.CHECK_SAVE_PRODUCT, context);

        // 商品锁
        String productLock = LockConstant.PRODUCT_ID_LOCK + context.getProductId();

        LockHelper.lock(productLock, () -> {
            // 手动开启事务
            TransactionHelper.doInTransaction(() -> {
                // 执行保存商品 会根据修改的字段情况提交审批和风控
                productHandlerProcessor.handle(ProductHandlerType.SAVE_PRODUCT, context);

                //刷新es
                productEsBuildService.buildProductEs(context.getProductId());
                // 记录操作日志
                ProductBo newProductBo = partSave ? context.getAuditProductBo() : context.getSaveProductBo();
                productLogAssist.recordProductLog(productBo.getOperationUserId(), productBo.getShopId(), context.getOldProductBo(), newProductBo);

            });
        });
        return context.getSaveProductResp();
    }

    @Override
    public Boolean queryProductByTemplateId(ProductTemplateReq productTemplateReq) {
        return productRepository.count(new LambdaQueryWrapper<Product>()
                .eq(Product::getFreightTemplateId, productTemplateReq.getTemplateId())
                .eq(Product::getWhetherDelete, Boolean.FALSE)) > 0;
    }

    @Override
    public List<CountProductTemplateResp> queryProductCountByTemplateId(CountProductTemplateReq template) {
        List<Map<String, Object>> result = productRepository.queryProductCountByTemplateId(template.getTemplateIds(), template.getShopId());
        return JsonUtil.copyList(result, CountProductTemplateResp.class);
    }

    @Override
    public List<LadderPriceDto> getLadderPriceBoList(List<Long> productIdList) {
        List<ProductLadderPriceBo> priceBoList = ladderPriceAssist.queryLadderPriceBoList(productIdList);
        return JsonUtil.copyList(priceBoList, LadderPriceDto.class);
    }

    @Override
    public BasePageResp<ProductPageBo> pageProduct(BasePageParam pageParam, ProductQueryBo queryBo) {
        return commonPageProduct(pageParam, queryBo);
    }

    @Override
    public List<ProductPageBo> queryProductById(List<Long> productIds, Long shopId) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.emptyList();
        }

        ProductQueryBo queryBo = new ProductQueryBo();
        queryBo.setProductIds(productIds);
        queryBo.setShopId(shopId);
        return productAssist.listEsProduct(queryBo);
    }

    @Override
    public ProductBo queryProductDetail(Long productId, Long shopId) {
        ProductContext context = ProductContext.builder().shopId(shopId).productId(productId).build();
        productHandlerProcessor.handle(ProductHandlerType.QUERY_PRODUCT_DETAIL, context);
        return context.getOldProductBo();
    }

    /**
     * 销售状态 1-销售中 2-仓库中 3-草稿箱
     * 审核状态 1-待审核 2-销售中 3-未通过 4-违规下架
     *
     * <p>
     * 1.【仓库中】和【销售中】中的商品,  可以直接上下架的，无需走审核
     * 2.【违规下架】和【草稿箱】的商品编辑, 不允许走批量上架
     *
     * @param onOffSaleParam 上下架参数
     */
    @Override
    public void batchOnOffSaleProduct(ProductOnOffSaleReq onOffSaleParam) {
        List<Long> productIdList = onOffSaleParam.getProductIdList();
        Boolean onSale = onOffSaleParam.getOnSale();
        Long shopId = onOffSaleParam.getShopId();
        ProductSourceEnum source = ProductSourceEnum.getByCode(onOffSaleParam.getChangeSource());
        boolean fromImport = ObjectUtils.defaultIfNull(onOffSaleParam.getFromImport(), false);

        if (CollectionUtils.isEmpty(productIdList)) {
            return;
        }

        // 检测并查询商品权限
        List<Product> productList = productAssist.checkShopAndListProduct(productIdList, shopId);

        // 根据商品状态分组
        Map<ProductStatusEnum, List<Product>> productMap = productList.stream()
                .collect(Collectors.groupingBy(item -> ProductStatusEnum.getBySaleAuditStatus(item.getSaleStatus(), item.getAuditStatus())));

        // 上架
        if (onSale) {
            onSaleProduct(productMap, source, shopId);
        }
        // 下架
        else {
            offSaleProduct(productMap, source, fromImport);
        }

        // 记录操作日志
        productLogAssist.recordOnOffSaleLog(onOffSaleParam);
    }

    @Override
    public void batchDeleteProduct(ProductDeleteReq deleteParam) {
        List<Long> productIdList = deleteParam.getProductIdList();
        Long shopId = deleteParam.getShopId();
        ProductSourceEnum source = ProductSourceEnum.getByCode(deleteParam.getChangeSource());

        if (CollectionUtils.isEmpty(productIdList)) {
            return;
        }

        List<Sku> skuList = skuRepository.listByProductIds(productIdList);
        Map<Long, List<Sku>> skuMap = skuList.stream().collect(Collectors.groupingBy(Sku::getProductId));

        // 检验商家权限
        List<Product> products = productAssist.checkShopAndListProduct(productIdList, shopId);
        /*if (!ProductSourceEnum.QNH.equals(source)) {
            long count = products.stream().filter(item -> item.getSource().equals(ProductSourceEnum.QNH.getCode())).count();
            AssertUtil.throwIfTrue(count > 0, "存在商品来源为牵牛花, 不允许删除");
        }*/
        
        // 检验是否有参加了活动的商品
        List<Long> activeProductIds = promotionRemoteService.collocationFlashSaleProductId(shopId);
        List<Long> filterProductIds = productIdList.stream().filter(productId -> activeProductIds.contains(productId)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(filterProductIds)) {
            throw new BusinessException(String.format("商品【%s】为限时购/组合购商品, 无法删除", StrUtil.join(StrUtil.COMMA, filterProductIds)));
        }

        Long deleteVersion = bizCodeAssist.getDeleteVersion();

        SendProductChangeEvent changeEvent = SendProductChangeEvent.build(productIdList, shopId, source,
                ProductChangeType.DELETE);
        TransactionHelper.doInTransaction(() -> {
            productRepository.removeByProductIds(productIdList, deleteVersion);
            skuRepository.logicRemoveByProductIds(productIdList, deleteVersion);
            productAuditAssist.removeHistoryAudit(productIdList);
            productDescriptionRepository.logicRemoveByProductIds(productIdList);

            // 把商品关注的商品删除掉
            favoriteProductRemoteService.deleteFavoriteProduct(productIdList);

            // 推送商品变动时间
            transactionEventPublisher.publish(changeEvent);

            // 记录操作日志
            productLogAssist.recordProductDeleteLog(deleteParam);
        });
    }

    /**
     * 平台独有接口, 不进行店铺权限校验
     *
     * @param updateSequenceReq 设置序号入参
     */
    @Override
    public void batchSaveProductSequence(ProductUpdateSequenceReq updateSequenceReq) {
        List<Long> productIdList = updateSequenceReq.getProductIdList();
        Long displaySequence = updateSequenceReq.getDisplaySequence();

        List<Product> dbProductList = productRepository.listByProductIds(productIdList);
        if (CollectionUtils.isEmpty(dbProductList)) {
            return;
        }

        SendProductChangeEvent changeEvent = SendProductChangeEvent.build(dbProductList, ProductSourceEnum.MALL, ProductChangeType.SAVE_PLATFORM_NO);
        TransactionHelper.doInTransaction(() -> {
            Product updProduct = new Product();
            updProduct.setDisplaySequence(displaySequence);
            productRepository.updateBatchByProductId(updProduct, productIdList);

            transactionEventPublisher.publish(changeEvent);

            // 记录操作日志
            productLogAssist.recordSequenceLog(updateSequenceReq.getOperationUserId(), updateSequenceReq.getShopId(),
                    UpdateSequenceLogBo.build(dbProductList), UpdateSequenceLogBo.build(productIdList, displaySequence));
        });
    }

    @Override
    public void batchSaveProductShopSequence(ProductUpdateSequenceReq shopSequenceReq) {
        List<Long> productIdList = shopSequenceReq.getProductIdList();
        Long shopId = shopSequenceReq.getShopId();
        Integer  displaySequence = shopSequenceReq.getDisplaySequence().intValue();

        // 检验商家权限
        List<Product> dbProductList = productAssist.checkShopAndListProduct(productIdList, shopId);
        if (CollectionUtils.isEmpty(dbProductList)) {
            return;
        }

        SendProductChangeEvent changeEvent = SendProductChangeEvent.build(dbProductList, ProductSourceEnum.MALL, ProductChangeType.SAVE_MERCHANT_NO);
        TransactionHelper.doInTransaction(() -> {
            Product updProduct = new Product();
            updProduct.setShopDisplaySequence(displaySequence);
            productRepository.updateBatchByProductId(updProduct, productIdList);

            transactionEventPublisher.publish(changeEvent);

            // 记录操作日志
            productLogAssist.recordShopSequenceLog(shopSequenceReq.getOperationUserId(), shopId,
                    UpdateShopSequenceLogBo.build(dbProductList), UpdateShopSequenceLogBo.build(productIdList, displaySequence));
        });
    }

    @Override
    public void batchBindDescriptionTemplate(BindDescriptionTemplateBo bindBo) {
        // 检验商家权限
        List<Product> dbProductList = productAssist.checkShopAndListProduct(bindBo.getProductIdList(), bindBo.getShopId());
        if (CollectionUtils.isEmpty(dbProductList)) {
            return;
        }

        SendProductChangeEvent changeEvent = SendProductChangeEvent.build(dbProductList, ProductSourceEnum.MALL, ProductChangeType.BIND_DESCRIPTION_TEMPLATE);
        List<ProductDescription> dbDescriptionList = productDescriptionRepository.listByProductIds(bindBo.getProductIdList());

        TransactionHelper.doInTransaction(() -> {
            ProductDescription updDescription = new ProductDescription();
            updDescription.setDescriptionPrefixId(bindBo.getDescriptionPrefixId());
            updDescription.setDescriptionSuffixId(bindBo.getDescriptionSuffixId());
            productDescriptionRepository.updateBatchByProductId(updDescription, bindBo.getProductIdList());

            transactionEventPublisher.publish(changeEvent);

            // 记录操作日志
            productLogAssist.recordBindDescriptionTemplateLog(bindBo.getOperationUserId(), bindBo.getShopId(),
                    BindDescriptionTemplateLogBo.build(dbDescriptionList),
                    BindDescriptionTemplateLogBo.build(bindBo));
        });
    }

    @Override
    public void bindRecommendProduct(BindRecommendProductReq bindBo) {
        Long productId = bindBo.getProductId();
        Long shopId = bindBo.getShopId();
        List<Long> recommendProductIdList = bindBo.getRecommendProductIdList();
        List<Product> dbProductList = productAssist.checkShopAndListProduct(Arrays.asList(productId), shopId);
        if (CollectionUtils.isEmpty(dbProductList)) {
            return;
        }
        productAssist.checkShopAndListProduct(recommendProductIdList, shopId);

        SendProductChangeEvent changeEvent = SendProductChangeEvent.build(dbProductList, ProductSourceEnum.MALL, ProductChangeType.BIND_RECOMMEND_PRODUCT);
        String lockName = LockConstant.BIND_RELATION_PRODUCT_LOCK + productId;
        LockHelper.lock(lockName, () -> {
            TransactionHelper.doInTransaction(() -> {
                ProductRelationProduct relationProduct = productRelationProductRepository.getByProductId(productId);
                String dbRelate = relationProduct == null ? StrUtil.EMPTY : relationProduct.getRelation();
                if (relationProduct == null) {
                    relationProduct = new ProductRelationProduct();
                    relationProduct.setProductId(productId);
                }
                String relation = StringUtils.EMPTY;
                if (!CollectionUtils.isEmpty(recommendProductIdList)) {
                    relation = recommendProductIdList.stream().map(item -> String.valueOf(item)).collect(Collectors.joining(StrUtil.COMMA));
                }
                relationProduct.setRelation(relation);
                productRelationProductRepository.saveOrUpdate(relationProduct);

                transactionEventPublisher.publish(changeEvent);

                // 记录操作日志
                productLogAssist.recordBindRecommendProductLog(bindBo.getOperationUserId(), bindBo.getShopId(),
                        BindRecommendProductLogBo.build(bindBo.getProductId(), dbRelate),
                        BindRecommendProductLogBo.build(bindBo.getProductId(), relation));
            });
        });
    }

    @Override
    public void batchBindFreightTemplate(BindFreightTemplateReq bindBo) {
        List<Long> productIdList = bindBo.getProductIdList();
        Long operationUserId = bindBo.getOperationUserId();
        Long shopId = bindBo.getShopId();
        Long freightTemplateId = bindBo.getFreightTemplateId();
        if (CollectionUtils.isEmpty(productIdList)) {
            return;
        }

        // 检验店铺权限
        List<Product> dbProductList = productAssist.checkShopAndListProduct(productIdList, shopId);
        if (CollectionUtils.isEmpty(dbProductList)) {
            return;
        }

        SendProductChangeEvent changeEvent = SendProductChangeEvent.build(dbProductList, ProductSourceEnum.MALL, ProductChangeType.BIND_FREIGHT_TEMPLATE);
        TransactionHelper.doInTransaction(() -> {
            Product updProduct = new Product();
            updProduct.setFreightTemplateId(freightTemplateId);
            productRepository.updateBatchByProductId(updProduct, productIdList);

            transactionEventPublisher.publish(changeEvent);

            // 记录操作日志
            productLogAssist.recordFreightTemplateLog(operationUserId, shopId,
                    BindFreightTemplateLogBo.build(dbProductList),
                    BindFreightTemplateLogBo.build(productIdList, freightTemplateId));
        });
    }

    /**
     * 批量修改价格(阶梯价不支持批量设置)
     *
     * @param priceBoList 商品价格更新入参
     * @param shopId      店铺id
     */
    @Override
    public void batchUpdateProductPrice(List<ProductSkuUpdatePriceBo> priceBoList, Long shopId) {
        if (CollectionUtils.isEmpty(priceBoList)) {
            return;
        }

        List<Long> skuAutoIds = priceBoList.stream().map(ProductSkuUpdatePriceBo::getSkuAutoId).collect(Collectors.toList());
        List<Sku> skus = skuRepository.listSkuByIds(skuAutoIds);
        AssertUtil.throwIfTrue(CollectionUtils.isEmpty(skus), "商品不存在");
        long count = skus.stream().filter(item -> !item.getShopId().equals(shopId)).count();
        AssertUtil.throwIfTrue(count > 0, "商品不存在或者不属于当前店铺");

        List<Long> productIds = skus.stream().map(Sku::getProductId).distinct().collect(Collectors.toList());
        List<ProductAudit> productAudits = productAuditAssist.checkShopAndListProductAudit(productIds, shopId);
        StringBuilder errMsgBuilder = new StringBuilder();
        productAudits.forEach(audit -> {
            if (ProductEnum.AuditStatusEnum.NOT_PASS.getCode().equals(audit.getAuditStatus())) {
                errMsgBuilder.append(String.format("【%s】被审核拒绝,", audit.getProductName()));
            }
            else {
                errMsgBuilder.append(String.format("【%s】正在审核中,", audit.getProductName()));
            }
            errMsgBuilder.append("无法进行改价操作");
        });
        AssertUtil.throwIfTrue(errMsgBuilder.length() > 0, errMsgBuilder.toString());

        List<ProductBo> productBos = productBoBuildForImportPrice(priceBoList, skus);
        // 市场价为0
        long errorCount = productBos.stream()
                .filter(item -> item.getMarketPrice() != null && item.getMarketPrice().compareTo(BigDecimal.ZERO) == 0).count();
        AssertUtil.throwIfTrue(errorCount > 0, "市场价不能为0");

        TransactionHelper.doInTransaction(() -> {
            productBos.forEach(productBo -> {
                saveProduct(productBo, ProductSourceEnum.MALL, ProductChangeType.CHANGE_PRICE, true, true);
            });
        });
    }

    private List<ProductBo> productBoBuildForImportPrice(List<ProductSkuUpdatePriceBo> priceBoList, List<Sku> skus) {
        Map<Long, Sku> skuMap = skus.stream().collect(Collectors.toMap(Sku::getId, Function.identity(), (k1, k2) -> k2));
        priceBoList.forEach(priceBo -> {
            Sku sku = skuMap.get(priceBo.getSkuAutoId());
            priceBo.setProductId(sku.getProductId());
            priceBo.setSkuId(sku.getSkuId());
            priceBo.setShopId(sku.getShopId());
        });

        Map<Long, List<ProductSkuUpdatePriceBo>> priceBoMap = priceBoList.stream().collect(Collectors.groupingBy(ProductSkuUpdatePriceBo::getProductId));
        List<ProductBo> productBoList = new ArrayList<>();
        priceBoMap.forEach((productId, curPriceBoList) -> {
            ProductBo productBo = new ProductBo();
            productBo.setProductId(productId);
            productBo.setMarketPrice(curPriceBoList.get(0).getMarketPrice());
            productBo.setShopId(curPriceBoList.get(0).getShopId());
            productBo.setSkuList(JsonUtil.copyList(curPriceBoList, ProductSkuBo.class));
            productBoList.add(productBo);
        });
        return productBoList;
    }

    @Override
    public void batchUpdateSafeStock(ProductUpdateSafeStockReq updateReq) {
        List<Long> productIdList = updateReq.getProductIdList();
        Long shopId = updateReq.getShopId();
        Long safeStock = updateReq.getSafeStock();

        if (CollectionUtils.isEmpty(productIdList)) {
            return;
        }

        // 检验店铺权限
        List<Product> dbProductList = productAssist.checkShopAndListProduct(productIdList, shopId);
        List<SkuStock> dbSkuStocks = skuStockRepository.listByProductIds(productIdList);
        SendProductChangeEvent changeEvent = SendProductChangeEvent.build(dbProductList, ProductSourceEnum.MALL, ProductChangeType.SET_WARNING_STOCK);
        TransactionHelper.doInTransaction(() -> {
            SkuStock updSkuStock = new SkuStock();
            updSkuStock.setSafeStock(safeStock);
            skuStockRepository.updateBatchByProductId(updSkuStock, productIdList);

            transactionEventPublisher.publish(changeEvent);

            // 记录日志
            productLogAssist.recordSafeStockLog(updateReq.getOperationUserId(), shopId,
                    UpdateSafeStockLogBo.build(dbSkuStocks), UpdateSafeStockLogBo.build(dbSkuStocks, safeStock));
        });
    }

    @Override
    public void batchUpdateStock(ProductUpdateStockReq stockReq) {
        List<UpdateStockDto> skuList = stockReq.getSkuList();
        Long shopId = stockReq.getShopId();
        if (CollectionUtils.isEmpty(skuList)) {
            return;
        }

        List<Long> skuAutoIds = skuList.stream().map(UpdateStockDto::getSkuAutoId).collect(Collectors.toList());
        // 查询旧的库存
        List<SkuStock> oldStockList = skuStockRepository.listBySkuAutoIds(skuAutoIds);

        StockTaskBo taskBo = new StockTaskBo();
        taskBo.setBizCode(IdUtil.fastUUID());
        taskBo.setUpdateType(StockUpdateTypeEnum.LIST_BATCH_UPDATE);
        taskBo.setUpdateWay(StockUpdateWayEnum.COVER);
        taskBo.setUpdateKey(StockUpdateKeyEnum.SKU_AUTO_ID);
        List<StockTaskInfoBo> taskInfoBoList = JsonUtil.copyList(skuList, StockTaskInfoBo.class, (source, target) -> target.setShopId(shopId));
        taskBo.setTaskInfoBoList(taskInfoBoList);
        skuStockService.asyncChangeSkuStock(taskBo);

        // 记录操作日志
        List<UpdateStockDto> dbItems = JsonUtil.copyList(oldStockList, UpdateStockDto.class);
        productLogAssist.recordStockLog(stockReq.getOperationUserId(),
                shopId, UpdateStockLogBo.build(dbItems), UpdateStockLogBo.build(skuList));
    }

    /**
     * 平台独有接口, 不做店铺权限校验
     *
     * @param updateBo 商品虚拟销量更新入参
     */
    @Override
    public void batchUpdateVirtualSales(ProductUpdateVirtualSaleCountsBo updateBo) {
        List<Long> productIdList = updateBo.getProductIdList();
        if (CollectionUtils.isEmpty(productIdList)) {
            return;
        }

        List<Product> dbProductList = productRepository.listByProductIds(productIdList);
        if (CollectionUtils.isEmpty(dbProductList)) {
            return;
        }

        List<Product> updProductList = new ArrayList<>();
        updateBo.getProductIdList().forEach(productId -> {
            Product updProduct = new Product();
            updProduct.setProductId(productId);
            if (VirtualSaleCountsTypeEnum.FIXED.equals(updateBo.getVirtualSaleCountsType())) {
                updProduct.setVirtualSaleCounts(updateBo.getFixedNum());
            }
            else {
                updProduct.setVirtualSaleCounts(RandomUtils.nextLong(updateBo.getMinRandomNum(), updateBo.getMaxRandomNum()));
            }
            updProductList.add(updProduct);
        });

        SendProductChangeEvent changeEvent = SendProductChangeEvent.build(dbProductList, ProductSourceEnum.MALL, ProductChangeType.SET_VIRTUAL_SALES);
        TransactionHelper.doInTransaction(() -> {
            productRepository.updateBatchByProductId(updProductList);

            transactionEventPublisher.publish(changeEvent);

            // 记录操作日志
            productLogAssist.recordVirtualSalesLog(updateBo.getOperationUserId(), updateBo.getOperationShopId(),
                    UpdateVirtualSalesLogBo.build(dbProductList), UpdateVirtualSalesLogBo.build(updProductList));
        });
    }

    /**
     * 批量违规下架商品
     * 平台独有接口, 不做店铺权限校验
     *
     * @param violationReq 批量违规下架商品入参
     */
    @Override
    public void batchViolationOffSale(ProductViolationReq violationReq) {
        List<Long> productIdList = violationReq.getProductIdList();
        String auditReason = violationReq.getAuditReason();
        if (CollectionUtils.isEmpty(productIdList)) {
            return;
        }

        List<Product> dbProductList = productRepository.listByProductIds(productIdList);
        if (CollectionUtils.isEmpty(dbProductList)) {
            return;
        }

        SendProductChangeEvent changeEvent = SendProductChangeEvent.build(dbProductList, ProductSourceEnum.MALL, ProductChangeType.VIOLATION_DOWN);
        TransactionHelper.doInTransaction(() -> {
            Product updProduct = new Product();
            updProduct.setAuditStatus(ProductEnum.AuditStatusEnum.VIOLATION.getCode());
            productRepository.updateBatchByProductId(updProduct, productIdList);

            ProductDescription updDescription = new ProductDescription();
            updDescription.setAuditReason(auditReason);
            productDescriptionRepository.updateBatchByProductId(updDescription, productIdList);

            transactionEventPublisher.publish(changeEvent);

            // 记录操作日志
            productLogAssist.recordViolationOffSaleLog(violationReq);
        });
    }

    /**
     * 批量违规下架商品（导入违规下架）
     * 平台独有接口, 不做店铺权限校验
     *
     * @param violationProductList 批量违规下架商品列表
     */
    @Override
    public void batchViolationOffSale(List<ProductViolationOffSaleBo> violationProductList) {
        if (CollectionUtils.isEmpty(violationProductList)) {
            return;
        }

        List<Long> productIdList = violationProductList.stream().map(ProductViolationOffSaleBo::getProductId).collect(Collectors.toList());
        List<Product> dbProductList = productRepository.listByProductIds(productIdList);
        if (CollectionUtils.isEmpty(dbProductList)) {
            return;
        }

        SendProductChangeEvent changeEvent = SendProductChangeEvent.build(dbProductList, ProductSourceEnum.MALL, ProductChangeType.IMPORT_VIOLATION_DOWN);
        TransactionHelper.doInTransaction(() -> {
            Product updProduct = new Product();
            updProduct.setAuditStatus(ProductEnum.AuditStatusEnum.VIOLATION.getCode());
            productRepository.updateBatchByProductId(updProduct, productIdList);

            productDescriptionRepository.updateBatchByProductId(JsonUtil.copyList(violationProductList, ProductDescription.class));

            transactionEventPublisher.publish(changeEvent);
        });
    }

    /**
     * @param shopId 店铺id
     */
    @Override
    @DistributeLock(keyPattern = LockConstant.LOCK_OFF_SHELF_SHOP_PRODUCT_PATTERN, scenes =
            LockConstant.SCENE_OFF_SHELF_SHOP_PRODUCT,
            waitLock = true,
            keyValues = {"{0}"})
    public void offSaleAllProduct(Long shopId) {
        boolean end = false;
        while (!end) {
            LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<Product>().eq(Product::getShopId, shopId)
                    .eq(Product::getSaleStatus, ProductEnum.SaleStatusEnum.ON_SALE.getCode())
                    .select(Product::getProductId)
                    .last(CommonConstant.LIMIT + CommonConstant.UPDATE_LIMIT);
            List<Long> productIdList = productRepository.listObjs(wrapper, item -> Long.parseLong(String.valueOf(item)));

            if (!CollectionUtils.isEmpty(productIdList)) {
                List<Product> dbProductList = productRepository.listByProductIds(productIdList);
                SendProductChangeEvent changeEvent = SendProductChangeEvent.build(dbProductList, ProductSourceEnum.MALL, ProductChangeType.SHOP_FREEZE_DOWN);

                TransactionHelper.doInTransaction(() -> {
                    Product updProduct = new Product();
                    updProduct.setSaleStatus(ProductEnum.SaleStatusEnum.IN_STOCK.getCode());
                    productRepository.updateBatchByProductId(updProduct, productIdList);

                    transactionEventPublisher.publish(changeEvent);
                });
            }

            end = CollectionUtils.isEmpty(productIdList) || productIdList.size() < CommonConstant.QUERY_LIMIT;
        }
    }

    @Override
    public ProductSkuMergeCombinationBo queryProductSkuMerge(ProductSkuQueryBo queryBo) {
        ProductSkuMergeCombinationBo combination = new ProductSkuMergeCombinationBo();

        List<Sku> skuList = skuService.querySkuList(JsonUtil.copy(queryBo, ProductSkuQueryReq.class));
        // 商品map
        List<Long> productIdList = skuList.stream().map(Sku::getProductId).collect(Collectors.toList());
        // 如果是根据skuId 进行查询的，有可能该规格已经被删除，此时会在失效商品的集合里面返回商品基本的信息
        productIdList.addAll(SkuIdHelper.getProductIds(queryBo.getSkuIdList()));
        productIdList = productIdList.stream().distinct().collect(Collectors.toList());
        Map<Long, Product> productMap = productRepository.getProductMap(productIdList);
        // 品牌名称map
        List<Long> brandIdList = productMap.values().stream().map(Product::getBrandId).collect(Collectors.toList());
        Map<Long, String> brandNameMap = brandRepository.getBrandNameMap(brandIdList);
        // 类目map
        List<Long> categoryIdList = productMap.values().stream().map(Product::getCategoryId).collect(Collectors.toList());
        Map<Long, CategoryBo> categoryMap = categoryService.getCategoryMap(categoryIdList);
        // 库存map
        List<Long> skuAutoIds = skuList.stream().map(Sku::getId).collect(Collectors.toList());
        Map<Long, SkuStock> skuStockMap = skuStockRepository.listBySkuAutoIds(skuAutoIds).stream()
                .collect(Collectors.toMap(SkuStock::getSkuAutoId, Function.identity(), (k1, k2) -> k2));
        // 阶梯价map
        List<Long> ladderPriceProductIds = productMap.values().stream()
                .filter(item -> item.getWhetherOpenLadder()).map(Product::getProductId)
                .collect(Collectors.toList());
        List<ProductLadderPrice> ladderPriceList = productLadderPriceRepository.listByProductIds(ladderPriceProductIds);
        Map<Long, List<ProductLadderPrice>> ladderPriceMap = ladderPriceList.stream().collect(Collectors.groupingBy(ProductLadderPrice::getProductId));

        List<ProductSkuMergeBo> mergeBoList = new ArrayList<>();
        Map<Long, Long> skuCountMap = skuList.stream().collect(Collectors.groupingBy(Sku::getProductId, Collectors.counting()));
        for (Sku sku : skuList) {
            Product product = productMap.get(sku.getProductId());
            if (product == null) {
                continue;
            }
            // 过滤阶梯价
            if (queryBo.isFilterLadderPrice() && product.getWhetherOpenLadder()) {
                continue;
            }

            // 计算合并数量
            ProductSkuMergeBo mergeBo = ProductSkuConverter.convertToBo(sku, product);
            Long mergeCount = skuCountMap.getOrDefault(sku.getProductId(), 0L);
            mergeBo.setMergeCount(mergeCount);
            skuCountMap.remove(sku.getProductId());

            mergeBo.setBrandName(brandNameMap.get(product.getBrandId()));
            CategoryBo categoryBo = categoryMap.get(product.getCategoryId());
            if (categoryBo != null) {
                mergeBo.setFullCategoryName(categoryBo.getFullCategoryName());
                mergeBo.setCategoryName(categoryBo.getName());
                mergeBo.setCategoryIds(categoryBo.getFullIds());
            }

            SkuStock skuStock = skuStockMap.get(sku.getId());
            if (skuStock != null) {
                mergeBo.setStock(skuStock.getStock());
                mergeBo.setSafeStock(skuStock.getSafeStock());
            }

            List<ProductLadderPrice> subLadderPriceList = ladderPriceMap.get(sku.getProductId());
            if (CollectionUtils.isNotEmpty(subLadderPriceList)) {
                mergeBo.setLadderPriceList(JsonUtil.copyList(subLadderPriceList, ProductLadderPriceBo.class));
                mergeBo.setSalePrice(subLadderPriceList.get(0).getPrice());
            }

            mergeBo.setSpecName(skuAssist.getSpecValue(sku));
            mergeBo.setSkuName(mergeBo.getSpecName());
            mergeBoList.add(mergeBo);
        }

        combination.setProductSkuList(mergeBoList);
        // 提取失效的sku信息
        List<String> existSkuIds = skuList.stream().map(Sku::getSkuId).collect(Collectors.toList());
        combination.setInvalidSkuList(getInvalidSkuList(queryBo.getSkuIdList(), existSkuIds, productMap, brandNameMap));
        return combination;
    }

    /**
     * 提取失效的sku信息(该方法主要用于针对于已经被删除的sku，补充商品数据)
     *
     * @param skuIds       skuId的集合
     * @param existSkuIds  已经查到了信息的规格id的集合
     * @param productMap   商品map
     * @param brandNameMap 品牌名称map
     * @param categoryMap  类目map
     * @return 失效的sku信息
     */
    /**
     * 提取失效的sku信息(该方法主要用于针对于已经被删除的sku，补充商品数据)
     *
     * @param skuIds       skuId的集合
     * @param existSkuIds  已经查到了信息的规格id的集合
     * @param productMap   商品map
     * @param brandNameMap 品牌名称map
     * @return 失效的sku信息
     */
    private List<ProductSkuMergeBo> getInvalidSkuList(List<String> skuIds,
                                                      List<String> existSkuIds,
                                                      Map<Long, Product> productMap,
                                                      Map<Long, String> brandNameMap) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }

        // 过滤出不存在的skuIds
        List<String> notExistSkuIds = skuIds.stream().filter(skuId -> !existSkuIds.contains(skuId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notExistSkuIds)) {
            return Collections.emptyList();
        }

        List<ProductSkuMergeBo> invalidSkuList = new ArrayList<>();
        notExistSkuIds.forEach(skuId -> {
            Long productId = SkuIdHelper.getProductId(skuId);

            Product product = productMap.get(productId);
            if (product == null) {
                return;
            }

            ProductSkuMergeBo mergeBo = ProductSkuConverter.convertToBo(null, product);
            mergeBo.setSkuId(skuId);

            mergeBo.setBrandName(brandNameMap.get(product.getBrandId()));

            invalidSkuList.add(mergeBo);
        });
        return invalidSkuList;
    }

    @Override
    public BasePageResp<ErpProductBo> queryProductForErp(BasePageParam pageParam, ErpQueryProductBo queryBo) {
        BasePageResp<ProductPageBo> pageResult = productAssist.pageEsProduct(pageParam, erpQueryBoConvertToQueryBo(queryBo));
        List<ProductPageBo> productList = pageResult.getData();
        List<Long> productIdList = productList.stream().map(ProductPageBo::getProductId).collect(Collectors.toList());

        List<Sku> skuList = skuRepository.listByProductIds(productIdList);
        Map<Long, List<Sku>> skuMap = skuList.stream().collect(Collectors.groupingBy(Sku::getProductId));
        List<ProductLadderPrice> ladderPriceList = productLadderPriceRepository.listByProductIds(productIdList);
        Map<Long, List<ProductLadderPrice>> ladderPriceMap = ladderPriceList.stream().collect(Collectors.groupingBy(ProductLadderPrice::getProductId));
        Map<Long, List<String>> imageMap = productImageRepository.getImageMap(productIdList);
        List<SkuStock> skuStocks = skuStockRepository.listByProductIds(productIdList);

        return PageResultHelper.transfer(pageResult, db -> {
            ErpProductBo bo = ProductConverter.convertToErpBo(db);

            List<Sku> skus = skuMap.get(db.getProductId());
            if (!CollectionUtils.isEmpty(skus)) {
                List<ErpSkuBo> erpSkuBos = SkuConverter.convertToErpSkuBos(skus, skuStocks);
                bo.setSkuList(erpSkuBos);
                bo.setStock(erpSkuBos.stream().mapToLong(ErpSkuBo::getStock).sum());
            }

            List<ProductLadderPrice> ladderPrices = ladderPriceMap.get(bo.getProductId());
            if (!CollectionUtils.isEmpty(ladderPrices)) {
                bo.setLadderPriceList(JsonUtil.copyList(ladderPrices, ErpLadderPriceBo.class));
            }

            bo.setImageList(imageMap.get(bo.getProductId()));
            bo.setWebUrl(StrUtil.EMPTY);
            bo.setH5Url(StrUtil.EMPTY);
            return bo;
        });
    }

    @Override
    public List<Long> queryRecommendProductIds(Long id) {
        return productRelationProductRepository.getRelationProductIds(id);
    }

    @Override
    public StatisticalProductResp queryStatisticalProduct(Long shopId) {
        StatisticalProductResp result = new StatisticalProductResp();
        if (shopId == null) {
            return result;
        }
        result.setProductsNumber(firstPageRepository.queryProductsCount(shopId));
        result.setProductsOnSale(firstPageRepository.queryOnSaleProducts(shopId));
        result.setProductsInDraft(firstPageRepository.queryProductsInDraft(shopId));
        result.setProductsWaitForAuditing(firstPageRepository.queryWaitForAuditingProducts(shopId));
        result.setProductsAuditFailed(firstPageRepository.queryAuditFailureProducts(shopId));
        result.setProductsInfractionSaleOff(firstPageRepository.queryInfractionSaleOffProducts(shopId));
        result.setProductsInStock(firstPageRepository.queryInStockProducts(shopId));
        result.setOverSafeStockProducts(firstPageRepository.queryOverSafeStockProducts(shopId));
        result.setProductsBrands(firstPageRepository.queryProductsBrands(shopId));
        return result;
    }

    @Override
    public MStatisticalProductResp queryMStatisticalProduct() {
        MStatisticalProductResp result = new MStatisticalProductResp();
        result.setTotalNum(firstPageRepository.queryProductsCount(null));
        result.setProductsOnSale(firstPageRepository.queryOnSaleProducts(null));
        result.setProductsWaitForAuditing(firstPageRepository.queryWaitForAuditingProducts(null));
        result.setProductsBrands(firstPageRepository.queryProductsBrands(null));
        ShopCommentSummaryResp resp = ThriftResponseHelper.executeThriftCall(() -> productCommentQueryFeign.queryMCommentSummary());
        result.setProductsComment(resp.getTotalCount());
        return result;
    }

    @Override
    public RecommendProductsResp queryRecommendProducts(RecommendProductsReq req) {
        // 第一步，根据用户ID查出店铺ID
        log.info("lws,,,orderInfoDto param={}", req.getUserId());
        OrderInfoDto orderInfoDto = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.queryLastOrderInfo(req.getUserId()));
        log.info("lws,,,orderInfoDto result={}", JSONUtil.toJsonStr(orderInfoDto));
        Long shopId = null;
        if (orderInfoDto != null) {
            shopId = orderInfoDto.getShopId();
        }
        log.info("lws,,,productList param={}", shopId);
        List<Product> productList = productRepository.queryRecommendProducts(shopId, req.getLimitNum());
        log.info("lws,,,productList result={}" + productList);
        if (CollectionUtils.isEmpty(productList)) {
            return null;
        }
        RecommendProductsResp result = new RecommendProductsResp();
        List<RecommendProductsDto> productsDtoList = JsonUtil.copyList(productList, RecommendProductsDto.class);
        result.setProductsDtoList(productsDtoList);
        return result;
    }


    ProductSkuStockResp convertStock(SkuStock skuStock) {
        ProductSkuStockResp resp = new ProductSkuStockResp();
        resp.setSkuAutoId(skuStock.getSkuAutoId());
        resp.setStock(skuStock.getStock());
        resp.setProductId(skuStock.getProductId());
        resp.setUpdateTime(skuStock.getUpdateTime());
        resp.setSkuId(skuStock.getSkuId());
        resp.setSkuCode(skuStock.getSkuCode());
        return resp;
    }

    @Override
    public List<ProductSkuStockResp> queryBatchStocks(List<Long> skuAutoIds,
                                                      List<String> skuCodes, Long shopId) {
        if (CollectionUtils.isNotEmpty(skuAutoIds)) {
            return skuStockRepository.listByShopIdAndSkuAutoIds(shopId, skuAutoIds).stream().map(this::convertStock).collect(Collectors.toList());
        }
        return skuStockRepository.listByShopIdAndSkuCodes(shopId, skuCodes).stream().map(this::convertStock)
                .collect(Collectors.toList());
    }

    @Override
    public BasePageResp<ProductSkuStockResp> queryShopStocksPage(BasePageParam pageParam, Long shopId) {
        Page<SkuStock> pageInfo = skuStockRepository.listPage(shopId, pageParam.getPageNum(),
                pageParam.getPageSize());
        if (pageInfo.getResult() == null) {
            return PageResultHelper.defaultEmpty(pageParam);
        }
        return PageResultHelper.transfer(pageInfo, this::convertStock);
    }

    @Override
    public ProductRichTextBo queryProductRichText(QueryProductRichTextBo queryBo) {
        ProductDescription description = productDescriptionRepository.getProductDescriptionByProductId(queryBo.getProductId());
        if (description == null) {
            return ProductRichTextBo.ofEmpty();
        }

        ProductRichTextEnum type = ObjectUtils.defaultIfNull(ProductRichTextEnum.getByCode(queryBo.getType()), ProductRichTextEnum.PC);
        ProductDescriptionTemplate prefixTemplate = descriptionTemplateRepository.getByIdExcludeDeleted(description.getDescriptionPrefixId());
        ProductDescriptionTemplate suffixTemplate = descriptionTemplateRepository.getByIdExcludeDeleted(description.getDescriptionSuffixId());
        ProductRichTextBo richTextBo = new ProductRichTextBo();
        switch (type) {
            case PC:
                richTextBo.setDescription(description.getDescription());
                richTextBo.setDescriptionPrefix(Optional.ofNullable(prefixTemplate).map(ProductDescriptionTemplate::getContent).orElse(StringUtils.EMPTY));
                richTextBo.setDescriptionSuffix(Optional.ofNullable(suffixTemplate).map(ProductDescriptionTemplate::getContent).orElse(StringUtils.EMPTY));
                break;
            case MOBILE:
                richTextBo.setDescription(description.getMobileDescription());
                richTextBo.setDescriptionPrefix(Optional.ofNullable(prefixTemplate).map(ProductDescriptionTemplate::getMobileContent).orElse(StringUtils.EMPTY));
                richTextBo.setDescriptionSuffix(Optional.ofNullable(suffixTemplate).map(ProductDescriptionTemplate::getMobileContent).orElse(StringUtils.EMPTY));
                break;
            default:
                break;
        }
        return richTextBo;
    }

    @Override
    public List<ProductBasicDto> queryProductBasic(QueryProductBasicReq req) {
        if (CollectionUtils.isEmpty(req.getProductIds())) {
            return Collections.emptyList();
        }

        return JsonUtil.copyList(productRepository.listByProductIds(req.getProductIds()), ProductBasicDto.class);
    }

    @Override
    public String generateProductCode() {
        return bizCodeAssist.getProductCode();
    }

    @Override
    public void addSales(AddSaleCountReq req) {
        List<com.sankuai.shangou.seashop.product.thrift.core.request.product.dto.AddProductSaleCountDto> productList = req.getProductList();

        List<AddProductSaleCountDto> addSaleCountList = JsonUtil.copyList(productList, AddProductSaleCountDto.class);
        TransactionHelper.doInTransaction(() -> {
            addSaleCountList.forEach(item -> {
                productRepository.addSaleCount(item);
            });
        });
    }

    @Override
    public ShopSaleCountsDto queryShopSaleCounts(Long shopId) {
        return productRepository.getShopSaleCounts(shopId);
    }

    @Override
    public BasePageResp<ProductBasicDto> pageProductBasic(QueryProductBasicReq req) {
        Page<Product> result = PageHelper.startPage(req.getPageNo(), req.getPageSize());
        productRepository.list(new LambdaQueryWrapper<Product>().eq(Product::getWhetherDelete, Boolean.FALSE));
        return PageResultHelper.transfer(result, ProductBasicDto.class);
    }

    @Override
    public List<Long> queryProductIds(ProductQueryBo queryBo) {
        return productAssist.listProductIds(queryBo);
    }

    @Override
    public List<ProductPageResp> queryProductByIds(QueryProductByIdsReq request) {
        List<Product> respProduct = productRepository.queryProductByIds(request.getProductIdList(), request.getProductName());
        return JsonUtil.copyList(respProduct, ProductPageResp.class);
    }

    @Override
    public void clearScrollId(String scrollId) {
        esProductService.clearScrollId(scrollId);
    }

    @Override
    public ProductSkuMergeCombinationBo queryProductSkuMergeNoneMarket(ProductSkuQueryReq queryBo) {
        ProductSkuMergeCombinationBo combination = new ProductSkuMergeCombinationBo();

        List<Sku> skuList = skuService.querySkuList(queryBo);
        // 商品map
        List<Long> productIdList = skuList.stream().map(Sku::getProductId).collect(Collectors.toList());
        // 如果是根据skuId 进行查询的，有可能该规格已经被删除，此时会在失效商品的集合里面返回商品基本的信息
        productIdList.addAll(SkuIdHelper.getProductIds(queryBo.getSkuIdList()));
        productIdList = productIdList.stream().distinct().collect(Collectors.toList());
        Map<Long, Product> productMap = productRepository.getProductMap(productIdList);
        // 品牌名称map
        List<Long> brandIdList = productMap.values().stream().map(Product::getBrandId).collect(Collectors.toList());
        Map<Long, String> brandNameMap = brandRepository.getBrandNameMap(brandIdList);
        // 库存map
        List<Long> skuAutoIds = skuList.stream().map(Sku::getId).collect(Collectors.toList());
        Map<Long, SkuStock> skuStockMap = skuStockRepository.listBySkuAutoIds(skuAutoIds).stream()
                .collect(Collectors.toMap(SkuStock::getSkuAutoId, Function.identity(), (k1, k2) -> k2));

        List<ProductSkuMergeBo> mergeBoList = new ArrayList<>();
        Map<Long, Long> skuCountMap = skuList.stream().collect(Collectors.groupingBy(Sku::getProductId, Collectors.counting()));
        for (Sku sku : skuList) {
            Product product = productMap.get(sku.getProductId());
            if (product == null) {
                continue;
            }
            // 计算合并数量
            ProductSkuMergeBo mergeBo = ProductSkuConverter.convertToBo(sku, product);
            Long mergeCount = skuCountMap.getOrDefault(sku.getProductId(), 0L);
            mergeBo.setMergeCount(mergeCount);
            skuCountMap.remove(sku.getProductId());

            mergeBo.setBrandName(brandNameMap.get(product.getBrandId()));
            SkuStock skuStock = skuStockMap.get(sku.getId());
            if (skuStock != null) {
                mergeBo.setStock(skuStock.getStock());
                mergeBo.setSafeStock(skuStock.getSafeStock());
            }
            mergeBo.setSpecName(skuAssist.getSpecValue(sku));
            mergeBo.setSkuName(mergeBo.getSpecName());
            mergeBoList.add(mergeBo);
        }

        combination.setProductSkuList(mergeBoList);
        // 提取失效的sku信息
        List<String> existSkuIds = skuList.stream().map(Sku::getSkuId).collect(Collectors.toList());
        combination.setInvalidSkuList(this.getInvalidSkuList(queryBo.getSkuIdList(), existSkuIds, productMap, brandNameMap));
        return combination;
    }

    /**
     * 公共搜索方法
     *
     * @param pageParam 分页参数
     * @param queryBo   查询参数
     * @return 分页结果
     */
    private BasePageResp<ProductPageBo> commonPageProduct(BasePageParam pageParam, ProductQueryBo queryBo) {
        // 转换搜索状态
        ProductStatusHelper.Status status = ProductStatusHelper.getStatus(queryBo.getStatus());
        if (status != null) {
            queryBo.setSaleStatusCode(status.getSaleStatusCode());
            queryBo.setAuditStatusCode(status.getAuditStatusCode());
        }
//        List<Long> categoryIds = queryBo.getCategoryIds();
//        if(queryBo.getShopCategoryId() != null){
//            if(CollUtil.isEmpty(categoryIds)){
//                categoryIds = new ArrayList<>();
//            }
//            categoryIds.add(queryBo.getShopCategoryId());
//        }
//        queryBo.setCategoryIds(categoryIds);
        if (CollectionUtils.isNotEmpty(queryBo.getCategoryIds())) {
            queryBo.setCategoryPath(StrUtil.join(CommonConstant.CATEGORY_PATH_SPLIT_NO_ESCAPE, queryBo.getCategoryIds()));
            queryBo.setCategoryIds(null);
        }
        // 处理批量的商品状态
        if (CollectionUtils.isNotEmpty(queryBo.getInStatus())) {
            List<StatusDto> statusList = queryBo.getInStatus().stream().map(statusEnum -> {
                StatusDto statusDto = new StatusDto();
                statusDto.setSaleStatus(statusEnum.getSaleStatus() == null ? null : statusEnum.getSaleStatus().getCode());
                statusDto.setAuditStatus(statusEnum.getAuditStatus() == null ? null : statusEnum.getAuditStatus().getCode());
                return statusDto;
            }).collect(Collectors.toList());
            queryBo.setStatusList(statusList);
        }
        // 处理传了店铺名称的情况
        if (StringUtils.isNotEmpty(queryBo.getShopName())) {
            queryBo.setShopIds(remoteShopService.getShopIdsByName(StringHelper.escapeSpecialChar(queryBo.getShopName())));
            if (CollectionUtils.isEmpty(queryBo.getShopIds())) {
                return PageResultHelper.defaultEmpty(pageParam);
            }
        }
        // 搜索了品牌名称
        if (StringUtils.isNotEmpty(queryBo.getBrandName())) {
            queryBo.setBrandIds(brandService.queryBrandIdsByName(StringHelper.escapeSpecialChar(queryBo.getBrandName())));
            if (CollectionUtils.isEmpty(queryBo.getBrandIds())) {
                return PageResultHelper.defaultEmpty(pageParam);
            }
        }
        // 处理店铺分类查询
        if (queryBo.getShopCategoryId() != null) {
            queryBo.setShopCategoryIds(shopCategoryAssist.getChildShopCategoryIdsAndSelf(queryBo.getShopCategoryId()));
            queryBo.setShopCategoryId(null);
        }
        // 处理平台分类
        if (queryBo.getParentCategoryId() != null) {
            queryBo.setCategoryIds(categoryService.getRecursiveChildIds(queryBo.getParentCategoryId()));
        }
        SortFieldFactory.dealSortField(ProductSortEnum.class, queryBo.getSortList());
        return productAssist.pageEsProduct(pageParam, queryBo);
    }

    /**
     * 上架商品
     *
     * @param productMap   商品分组
     * @param changeSource 变更来源
     */
    private void onSaleProduct(Map<ProductStatusEnum, List<Product>> productMap, ProductSourceEnum changeSource, Long shopId) {
        // 获取草稿箱的商品 不可以直接上架
        List<Product> draftProductList = productMap.get(ProductStatusEnum.DRAFT);
        AssertUtil.throwIfTrue(CollectionUtils.isNotEmpty(draftProductList), "只能上架审核通过的商品");

        // 获取违规下架的商品 违规下架的商品不支持免审上架
        List<Product> violationProductList = productMap.get(ProductStatusEnum.VIOLATION);
        if (CollectionUtils.isNotEmpty(violationProductList)) {
            // 查看是否开启了平台审核
            RemoteProductSettingBo productSetting = remoteSiteSettingService.getProductSetting();
            AssertUtil.throwIfTrue(!productSetting.isProductAuditOnOff(), "违规下架的商品不能申请免审核上架！");
        }

        // 查询类目是否被冻结
        List<Long> categoryIds = productMap.values().stream().filter(Objects::nonNull)
                .flatMap(List::stream).map(Product::getCategoryId)
                .distinct()
                .collect(Collectors.toList());
        // 只有有一个类目失效 则不允许上架
        Boolean hasAuth = categoryAssist.checkCategoryAuth(categoryIds, shopId);
        AssertUtil.throwIfTrue(!hasAuth, "类目被冻结无法上架");

        // 获取仓库中的商品 仓库中的商品可以直接上架
        List<Product> stockProductList = productMap.get(ProductStatusEnum.IN_STOCK);
        if (CollectionUtils.isNotEmpty(stockProductList)) {
            TransactionHelper.doInTransaction(() -> {
                List<Product> updProductList = stockProductList.stream().map(item -> {
                    Product updProduct = new Product();
                    updProduct.setId(item.getId());
                    updProduct.setProductId(item.getProductId());
                    updProduct.setSaleStatus(ProductEnum.SaleStatusEnum.ON_SALE.getCode());
                    updProduct.setAuditStatus(ProductEnum.AuditStatusEnum.ON_SALE.getCode());
                    updProduct.setShopId(item.getShopId());
                    return updProduct;
                }).collect(Collectors.toList());
                productRepository.updateBatchById(updProductList);

                // 发送商品变更事件
                SendProductChangeEvent changeEvent = SendProductChangeEvent.build(updProductList, changeSource, ProductChangeType.UP);
                transactionEventPublisher.publish(changeEvent);
            });
        }

        // 违规下架的商品 不支持免审 需要走审核流程
        if (CollectionUtils.isNotEmpty(violationProductList)) {
            TransactionHelper.doInTransaction(() -> {
                violationProductList.forEach(product -> {
                    productAssist.submitAudit(product.getProductId());
                });

                // 发送商品变更事件
                SendProductChangeEvent changeEvent = SendProductChangeEvent.build(violationProductList, changeSource, ProductChangeType.VIOLATION_DOWN_UP);
                transactionEventPublisher.publish(changeEvent);
            });
        }
    }

    /**
     * 下架商品
     *
     * @param productMap   商品分组
     * @param changeSource 变更来源
     * @param fromImport   是否是导入
     */
    private void offSaleProduct(Map<ProductStatusEnum, List<Product>> productMap, ProductSourceEnum changeSource, boolean fromImport) {
        List<Product> onSaleList = productMap.get(ProductStatusEnum.ON_SALE);
        if (CollectionUtils.isEmpty(onSaleList)) {
            return;
        }

        List<Product> updList = new ArrayList<>();
        onSaleList.forEach(item -> {
            Product updProduct = new Product();
            updProduct.setId(item.getId());
            updProduct.setProductId(item.getProductId());
            updProduct.setSaleStatus(ProductEnum.SaleStatusEnum.IN_STOCK.getCode());
            updProduct.setShopId(item.getShopId());
            updList.add(updProduct);
        });

        SendProductChangeEvent changeEvent = SendProductChangeEvent.build(onSaleList, changeSource,
                fromImport ? ProductChangeType.IMPORT_DOWN : ProductChangeType.DOWN);
        TransactionHelper.doInTransaction(() -> {
            productRepository.updateBatchById(updList);

            // 发送商品变动事件
            transactionEventPublisher.publish(changeEvent);

            // 清空待审核记录
            productAuditAssist.removeHistoryAudit(updList.stream().map(Product::getProductId).collect(Collectors.toList()));
        });
    }

    /**
     * 转换状态筛选
     *
     * @param erpQueryBo erp查询参数
     * @return 商品查询参数
     */
    private ProductQueryBo erpQueryBoConvertToQueryBo(ErpQueryProductBo erpQueryBo) {
        ProductQueryBo queryBo = new ProductQueryBo();
        if (erpQueryBo == null) {
            return queryBo;
        }

        List<StatusDto> statusList = new ArrayList<>();
        if (erpQueryBo.getSaleStatus() != null) {
            StatusDto status = new StatusDto();
            status.setSaleStatus(erpQueryBo.getSaleStatus().getCode());
            statusList.add(status);
        }
        // 转换状态筛选
        if (CollectionUtils.isNotEmpty(erpQueryBo.getErpStatus())) {
            erpQueryBo.getErpStatus().forEach(erpStatus -> {
                StatusDto status = new StatusDto();
                if (erpStatus.getAuditStatus() != null) {
                    status.setAuditStatus(erpStatus.getAuditStatus().getCode());
                }
                if (erpStatus.getSaleStatus() != null) {
                    status.setSaleStatus(erpStatus.getSaleStatus().getCode());
                }
                statusList.add(status);
            });
            queryBo.setStatusList(statusList);
        }

        // 转换时间筛选
        if (erpQueryBo.getTimeType() != null) {
            if (erpQueryBo.getTimeType().equals(ErpTimeType.CREATE_TIME)) {
                queryBo.setCreateTimeStart(erpQueryBo.getStartTime());
                queryBo.setCreateTimeEnd(erpQueryBo.getEndTime());
            }
            else {
                queryBo.setUpdateTimeStart(erpQueryBo.getStartTime());
                queryBo.setUpdateTimeEnd(erpQueryBo.getEndTime());
            }
        }

        queryBo.setProductId(erpQueryBo.getProductId());
        queryBo.setProductName(erpQueryBo.getProductName());
        queryBo.setProductCode(erpQueryBo.getProductCode());
        queryBo.setShopId(erpQueryBo.getShopId());
        queryBo.setSkuCodes(erpQueryBo.getSkuCodes());
        queryBo.setSkuAutoIds(erpQueryBo.getSkuAutoIds());
        return queryBo;
    }

}
