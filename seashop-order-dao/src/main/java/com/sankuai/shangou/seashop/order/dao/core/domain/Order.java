package com.sankuai.shangou.seashop.order.dao.core.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 订单主表
 * </p>
 *
 * <AUTHOR> @since 2023-11-15
 */
@TableName("`order`")
public class Order implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId("id")
    private Long id;

    /**
     * 订单ID
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 订单状态(1:待付款,2:待发货,3:待收货,4:已关闭,5:已完成,6:支付中)
     */
    @TableField("order_status")
    private Integer orderStatus;

    /**
     * 订单创建日期
     */
    @TableField("order_date")
    private Date orderDate;

    /**
     * 关闭原因
     */
    @TableField("close_reason")
    private String closeReason;

    /**
     * 店铺id
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 店铺名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 供应商电话
     */
    @TableField("seller_phone")
    private String sellerPhone;

    /**
     * 供应商发货地址
     */
    @TableField("seller_address")
    private String sellerAddress;

    /**
     * 供应商说明
     */
    @TableField("seller_remark")
    private String sellerRemark;

    /**
     * 供应商说明标识
     */
    @TableField("seller_remark_flag")
    private Integer sellerRemarkFlag;

    /**
     * 商家id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 商家名称
     */
    @TableField("user_name")
    private String userName;

    /**
     * 商家留言
     */
    @TableField("user_remark")
    private String userRemark;

    /**
     * 收货人
     */
    @TableField("ship_to")
    private String shipTo;

    /**
     * 收货人电话
     */
    @TableField("cell_phone")
    private String cellPhone;

    /**
     * 收货人地址省份id
     */
    @TableField("top_region_id")
    private Integer topRegionId;

    /**
     * 收货人区域id
     */
    @TableField("region_id")
    private Integer regionId;

    /**
     * 全名的收货地址
     */
    @TableField("region_full_name")
    private String regionFullName;

    /**
     * 地址
     */
    @TableField("address")
    private String address;

    /**
     * 收货地址坐标
     */
    @TableField("receive_longitude")
    private BigDecimal receiveLongitude;

    /**
     * 收货地址坐标
     */
    @TableField("receive_latitude")
    private BigDecimal receiveLatitude;

    /**
     * 运费，如果有修改，就是修改后的运费
     */
    @TableField("freight")
    private BigDecimal freight;
    /**
     * 手动修改运费后备份的原运费，只有第一次修改运费时才备份
     */
    @TableField("backup_freight")
    private BigDecimal backupFreight;

    /**
     * 发货日期
     */
    @TableField("shipping_date")
    private Date shippingDate;

    /**
     * 是否打印快递单
     */
    @TableField("is_printed")
    private Boolean isPrinted;

    /**
     * 付款类型名称
     */
    @TableField("payment_type_name")
    private String paymentTypeName;

    /**
     * 支付方式(1:线上支付)
     */
    @TableField("payment_type")
    private Integer paymentType;

    /**
     * 支付接口返回的id
     */
    @TableField("gateway_order_id")
    private String gatewayOrderId;

    /**
     * 付款注释
     */
    @TableField("pay_remark")
    private String payRemark;

    /**
     * 付款日期
     */
    @TableField("pay_date")
    private Date payDate;

    /**
     * 发票税钱
     */
    @TableField("tax")
    private BigDecimal tax;

    /**
     * 完成订单日期
     */
    @TableField("finish_date")
    private Date finishDate;

    /**
     * 商品总金额
     */
    @TableField("product_total_amount")
    private BigDecimal productTotalAmount;

    /**
     * 退款金额
     */
    @TableField("refund_total_amount")
    private BigDecimal refundTotalAmount;

    /**
     * 佣金总金额
     */
    @TableField("commis_total_amount")
    private BigDecimal commissionTotalAmount;

    /**
     * 退还佣金总金额
     */
    @TableField("refund_commis_amount")
    private BigDecimal refundCommissionAmount;

    /**
     * 结算手续费比率
     */
    @TableField("settlement_charge")
    private BigDecimal settlementCharge;

    /**
     * 活动类型
     */
    @TableField("active_type")
    private Integer activeType;

    /**
     * 来自哪个终端的订单。0：pc端，2：小程序
     */
    @TableField("platform")
    private Integer platform;

    /**
     * 优惠券抵扣金额
     * 对应.net的discountAmount
     */
    @TableField("coupon_amount")
    private BigDecimal couponAmount;

    /**
     * 订单类型。0:正常购,1:组合购,2:限时购
     */
    @TableField("order_type")
    private Integer orderType;

    /**
     * 订单备注(买家留言)
     */
    @TableField("order_remarks")
    private String orderRemarks;

    /**
     * 最后操作时间
     */
    @TableField("last_modify_time")
    private Date lastModifyTime;

    /**
     * 发货类型(0:快递配送)
     */
    @TableField("delivery_type")
    private Integer deliveryType;

    /**
     * 订单实付金额=productTotalAmount+tax+freight-fullDiscount-moneyOff/discountAmount
     */
    @TableField("total_amount")
    private BigDecimal totalAmount;

    /**
     * 订单实收金额(订单实付-退款金额)
     */
    @TableField("actual_pay_amount")
    private BigDecimal actualPayAmount;

    /**
     * 折扣抵扣金额
     * 对应.net的 fullDiscount字段
     */
    @TableField("discount_amount")
    private BigDecimal discountAmount;

    /**
     * 满减活动优惠金额
     */
    @TableField("money_off_amount")
    private BigDecimal moneyOffAmount;

    /**
     * 满足了多少金额，才参与的满减活动（如果有叠加，则是叠加后的满足金额）
     */
    @TableField("money_off_condition")
    private BigDecimal moneyOffCondition;

    /**
     * 使用的优惠券id
     */
    @TableField("coupon_id")
    private Long couponId;

    /**
     * 是否发送过短信
     */
    @TableField("is_send")
    private Boolean hasSend;

    /**
     * 优惠券类型（0代表商家券，1代表商家红包）
     */
    @TableField("coupon_type")
    private Integer couponType;

    ///**
    // * 订单来源 0表示本系统，1表示牵牛花
    // */
    //@TableField("order_source")
    //private Integer orderSource;

    /**
     * 牵牛花订单号
     */
    @TableField("source_order_id")
    private String sourceOrderId;

    /**
     * 推送erp类型
     */
    @TableField("push_erp_type")
    private Integer pushErpType;

    /**
     * 推送erp结果 0未推送 1推送成功 2推送失败
     */
    @TableField("push_erp_state")
    private Integer pushErpState;

    /**
     * 推送erp错误信息
     */
    @TableField("push_erp_msg")
    private String pushErpMsg;

    /**
     * 收货延迟天数
     */
    @TableField("receive_delay")
    private Integer receiveDelay;

    /**
     * 汇付支付方式
     */
    @TableField("payment")
    private Integer payment;

    /**
     * 汇付支付网银编码
     */
    @TableField("pay_bank_code")
    private String payBankCode;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 主订单号。用于区分同一批次提交的订单，取批次中第一个订单号
     */
    @TableField("main_order_id")
    private String mainOrderId;
    /**
     * 订单是否已评价。0：否；1：是
     */
    @TableField("has_commented")
    private Integer hasCommented;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Date getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Date orderDate) {
        this.orderDate = orderDate;
    }

    public String getCloseReason() {
        return closeReason;
    }

    public void setCloseReason(String closeReason) {
        this.closeReason = closeReason;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getSellerPhone() {
        return sellerPhone;
    }

    public void setSellerPhone(String sellerPhone) {
        this.sellerPhone = sellerPhone;
    }

    public String getSellerAddress() {
        return sellerAddress;
    }

    public void setSellerAddress(String sellerAddress) {
        this.sellerAddress = sellerAddress;
    }

    public String getSellerRemark() {
        return sellerRemark;
    }

    public void setSellerRemark(String sellerRemark) {
        this.sellerRemark = sellerRemark;
    }

    public Integer getSellerRemarkFlag() {
        return sellerRemarkFlag;
    }

    public void setSellerRemarkFlag(Integer sellerRemarkFlag) {
        this.sellerRemarkFlag = sellerRemarkFlag;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserRemark() {
        return userRemark;
    }

    public void setUserRemark(String userRemark) {
        this.userRemark = userRemark;
    }

    public String getShipTo() {
        return shipTo;
    }

    public void setShipTo(String shipTo) {
        this.shipTo = shipTo;
    }

    public String getCellPhone() {
        return cellPhone;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }

    public Integer getTopRegionId() {
        return topRegionId;
    }

    public void setTopRegionId(Integer topRegionId) {
        this.topRegionId = topRegionId;
    }

    public Integer getRegionId() {
        return regionId;
    }

    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }

    public String getRegionFullName() {
        return regionFullName;
    }

    public void setRegionFullName(String regionFullName) {
        this.regionFullName = regionFullName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public BigDecimal getReceiveLongitude() {
        return receiveLongitude;
    }

    public void setReceiveLongitude(BigDecimal receiveLongitude) {
        this.receiveLongitude = receiveLongitude;
    }

    public BigDecimal getReceiveLatitude() {
        return receiveLatitude;
    }

    public void setReceiveLatitude(BigDecimal receiveLatitude) {
        this.receiveLatitude = receiveLatitude;
    }

    public BigDecimal getFreight() {
        return freight;
    }

    public void setFreight(BigDecimal freight) {
        this.freight = freight;
    }

    public Date getShippingDate() {
        return shippingDate;
    }

    public void setShippingDate(Date shippingDate) {
        this.shippingDate = shippingDate;
    }

    public Boolean getPrinted() {
        return isPrinted;
    }

    public void setPrinted(Boolean isPrinted) {
        this.isPrinted = isPrinted;
    }

    public String getPaymentTypeName() {
        return paymentTypeName;
    }

    public void setPaymentTypeName(String paymentTypeName) {
        this.paymentTypeName = paymentTypeName;
    }

    public Integer getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(Integer paymentType) {
        this.paymentType = paymentType;
    }

    public String getGatewayOrderId() {
        return gatewayOrderId;
    }

    public void setGatewayOrderId(String gatewayOrderId) {
        this.gatewayOrderId = gatewayOrderId;
    }

    public String getPayRemark() {
        return payRemark;
    }

    public void setPayRemark(String payRemark) {
        this.payRemark = payRemark;
    }

    public Date getPayDate() {
        return payDate;
    }

    public void setPayDate(Date payDate) {
        this.payDate = payDate;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public Date getFinishDate() {
        return finishDate;
    }

    public void setFinishDate(Date finishDate) {
        this.finishDate = finishDate;
    }

    public BigDecimal getProductTotalAmount() {
        return productTotalAmount;
    }

    public void setProductTotalAmount(BigDecimal productTotalAmount) {
        this.productTotalAmount = productTotalAmount;
    }

    public BigDecimal getRefundTotalAmount() {
        return refundTotalAmount;
    }

    public void setRefundTotalAmount(BigDecimal refundTotalAmount) {
        this.refundTotalAmount = refundTotalAmount;
    }

    public BigDecimal getCommissionTotalAmount() {
        return commissionTotalAmount;
    }

    public void setCommissionTotalAmount(BigDecimal commissionTotalAmount) {
        this.commissionTotalAmount = commissionTotalAmount;
    }

    public BigDecimal getRefundCommissionAmount() {
        return refundCommissionAmount;
    }

    public void setRefundCommissionAmount(BigDecimal refundCommissionAmount) {
        this.refundCommissionAmount = refundCommissionAmount;
    }

    public BigDecimal getSettlementCharge() {
        return settlementCharge;
    }

    public void setSettlementCharge(BigDecimal settlementCharge) {
        this.settlementCharge = settlementCharge;
    }

    public Integer getActiveType() {
        return activeType;
    }

    public void setActiveType(Integer activeType) {
        this.activeType = activeType;
    }

    public Integer getPlatform() {
        return platform;
    }

    public void setPlatform(Integer platform) {
        this.platform = platform;
    }

    public BigDecimal getCouponAmount() {
        return couponAmount;
    }

    public void setCouponAmount(BigDecimal couponAmount) {
        this.couponAmount = couponAmount;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getOrderRemarks() {
        return orderRemarks;
    }

    public void setOrderRemarks(String orderRemarks) {
        this.orderRemarks = orderRemarks;
    }

    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    public Integer getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(Integer deliveryType) {
        this.deliveryType = deliveryType;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getActualPayAmount() {
        return actualPayAmount;
    }

    public void setActualPayAmount(BigDecimal actualPayAmount) {
        this.actualPayAmount = actualPayAmount;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public BigDecimal getMoneyOffAmount() {
        return moneyOffAmount;
    }

    public void setMoneyOffAmount(BigDecimal moneyOffAmount) {
        this.moneyOffAmount = moneyOffAmount;
    }

    public BigDecimal getMoneyOffCondition() {
        return moneyOffCondition;
    }

    public void setMoneyOffCondition(BigDecimal moneyOffCondition) {
        this.moneyOffCondition = moneyOffCondition;
    }

    public Long getCouponId() {
        return couponId;
    }

    public void setCouponId(Long couponId) {
        this.couponId = couponId;
    }

    public Boolean getHasSend() {
        return hasSend;
    }

    public void setHasSend(Boolean hasSend) {
        this.hasSend = hasSend;
    }

    public Integer getCouponType() {
        return couponType;
    }

    public void setCouponType(Integer couponType) {
        this.couponType = couponType;
    }

    //public Integer getOrderSource() {
    //    return orderSource;
    //}
    //
    //public void setOrderSource(Integer orderSource) {
    //    this.orderSource = orderSource;
    //}

    public String getSourceOrderId() {
        return sourceOrderId;
    }

    public void setSourceOrderId(String sourceOrderId) {
        this.sourceOrderId = sourceOrderId;
    }

    public Integer getPushErpType() {
        return pushErpType;
    }

    public void setPushErpType(Integer pushErpType) {
        this.pushErpType = pushErpType;
    }

    public Integer getPushErpState() {
        return pushErpState;
    }

    public void setPushErpState(Integer pushErpState) {
        this.pushErpState = pushErpState;
    }

    public String getPushErpMsg() {
        return pushErpMsg;
    }

    public void setPushErpMsg(String pushErpMsg) {
        this.pushErpMsg = pushErpMsg;
    }

    public Integer getReceiveDelay() {
        return receiveDelay;
    }

    public void setReceiveDelay(Integer receiveDelay) {
        this.receiveDelay = receiveDelay;
    }

    public Integer getPayment() {
        return payment;
    }

    public void setPayment(Integer payment) {
        this.payment = payment;
    }

    public String getPayBankCode() {
        return payBankCode;
    }

    public void setPayBankCode(String payBankCode) {
        this.payBankCode = payBankCode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getMainOrderId() {
        return mainOrderId;
    }

    public void setMainOrderId(String mainOrderId) {
        this.mainOrderId = mainOrderId;
    }

    public BigDecimal getBackupFreight() {
        return backupFreight;
    }

    public void setBackupFreight(BigDecimal backupFreight) {
        this.backupFreight = backupFreight;
    }

    public Integer getHasCommented() {
        return hasCommented;
    }

    public void setHasCommented(Integer hasCommented) {
        this.hasCommented = hasCommented;
    }

    @Override
    public String toString() {
        return "Order{" +
        "id=" + id +
        ", orderId=" + orderId +
        ", orderStatus=" + orderStatus +
        ", orderDate=" + orderDate +
        ", closeReason=" + closeReason +
        ", shopId=" + shopId +
        ", shopName=" + shopName +
        ", sellerPhone=" + sellerPhone +
        ", sellerAddress=" + sellerAddress +
        ", sellerRemark=" + sellerRemark +
        ", sellerRemarkFlag=" + sellerRemarkFlag +
        ", userId=" + userId +
        ", userName=" + userName +
        ", userRemark=" + userRemark +
        ", shipTo=" + shipTo +
        ", cellPhone=" + cellPhone +
        ", topRegionId=" + topRegionId +
        ", regionId=" + regionId +
        ", regionFullName=" + regionFullName +
        ", address=" + address +
        ", receiveLongitude=" + receiveLongitude +
        ", receiveLatitude=" + receiveLatitude +
        ", freight=" + freight +
        ", shippingDate=" + shippingDate +
        ", isPrinted=" + isPrinted +
        ", paymentTypeName=" + paymentTypeName +
        ", paymentType=" + paymentType +
        ", gatewayOrderId=" + gatewayOrderId +
        ", payRemark=" + payRemark +
        ", payDate=" + payDate +
        ", tax=" + tax +
        ", finishDate=" + finishDate +
        ", productTotalAmount=" + productTotalAmount +
        ", refundTotalAmount=" + refundTotalAmount +
        ", commisTotalAmount=" + commissionTotalAmount +
        ", refundCommisAmount=" + refundCommissionAmount +
        ", settlementCharge=" + settlementCharge +
        ", activeType=" + activeType +
        ", platform=" + platform +
        ", couponAmount=" + couponAmount +
        ", orderType=" + orderType +
        ", orderRemarks=" + orderRemarks +
        ", lastModifyTime=" + lastModifyTime +
        ", deliveryType=" + deliveryType +
        ", totalAmount=" + totalAmount +
        ", actualPayAmount=" + actualPayAmount +
        ", discountAmount=" + discountAmount +
        ", moneyOffAmount=" + moneyOffAmount +
        ", moneyOffCondition=" + moneyOffCondition +
        ", couponId=" + couponId +
        ", hasSend=" + hasSend +
        ", couponType=" + couponType +
        //", orderSource=" + orderSource +
        ", sourceOrderId=" + sourceOrderId +
        ", pushErpType=" + pushErpType +
        ", pushErpState=" + pushErpState +
        ", pushErpMsg=" + pushErpMsg +
        ", receiveDelay=" + receiveDelay +
        ", payment=" + payment +
        ", payBankCode=" + payBankCode +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", mainOrderId=" + mainOrderId +
                ", hasCommented=" + hasCommented +
        "}";
    }
}
