package com.sankuai.shangou.seashop.order.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 订单明细表
 * </p>
 *
 * <AUTHOR> @since 2023-11-15
 */
@TableName("order_item")
public class OrderItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单id，对应order表的order_id
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 店铺id
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 商品id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * skuid
     */
    @TableField("sku_id")
    private String skuId;

    /**
     * sku表sku字段
     */
    @TableField("sku")
    private String sku;

    /**
     * 购买数量
     */
    @TableField("quantity")
    private Long quantity;

    /**
     * 退货数量
     */
    @TableField("return_quantity")
    private Long returnQuantity;

    /**
     * 成本价
     */
    @TableField("cost_price")
    private BigDecimal costPrice;

    /**
     * 销售价，商品实际售价，不需要考虑折扣。如果是限时购与组合购商品，是活动价格，其他情况就是 专享价>阶梯价>原价
     */
    @TableField("sale_price")
    private BigDecimal salePrice;

    /**
     * 优惠金额，
     * 注意：用于改价，这个与折扣和优惠券都没有关系
     */
    @TableField("discount_amount")
    private BigDecimal discountAmount;

    /**
     * 商品实际应付金额=salePrice*quantity-fullDiscount-moneyOff/couponDiscount
     */
    @TableField("real_total_price")
    private BigDecimal realTotalPrice;

    /**
     * 退款价格
     */
    @TableField("refund_price")
    private BigDecimal refundPrice;

    /**
     * 商品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * sku颜色
     */
    @TableField("color")
    private String color;

    /**
     * sku尺寸
     */
    @TableField("size")
    private String size;

    /**
     * sku版本
     */
    @TableField("version")
    private String version;

    /**
     * 缩略图
     */
    @TableField("thumbnails_url")
    private String thumbnailsUrl;

    /**
     * 分佣比例
     */
    @TableField("commis_rate")
    private BigDecimal commisRate;

    /**
     * 可退金额
     */
    @TableField("enabled_refund_amount")
    private BigDecimal enabledRefundAmount;

    /**
     * 已申请退货数量
     */
    @TableField("apply_refund_quantity")
    private Long applyRefundQuantity;

    /**
     * 是否为限时购商品
     */
    @TableField("is_limit_buy")
    private Boolean isLimitBuy;

    /**
     * 优惠券抵扣金额-均摊到订单项的金额
     */
    @TableField("coupon_discount")
    private BigDecimal couponDiscount;

    /**
     * 折扣均摊金额
     */
    @TableField("full_discount")
    private BigDecimal fullDiscount;

    /**
     * 满减活动均摊金额
     */
    @TableField("money_off")
    private BigDecimal moneyOff;

    /**
     * 折扣营销活动id
     */
    @TableField("active_id")
    private Long activeId;

    /**
     * 限时购活动id
     */
    @TableField("flash_sale_id")
    private Long flashSaleId;

    /**
     * sku表auto_id字段
     */
    @TableField("sku_auto_id")
    private Long skuAutoId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Long getQuantity() {
        return quantity;
    }

    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    public Long getReturnQuantity() {
        return returnQuantity;
    }

    public void setReturnQuantity(Long returnQuantity) {
        this.returnQuantity = returnQuantity;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public BigDecimal getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(BigDecimal salePrice) {
        this.salePrice = salePrice;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public BigDecimal getRealTotalPrice() {
        return realTotalPrice;
    }

    public void setRealTotalPrice(BigDecimal realTotalPrice) {
        this.realTotalPrice = realTotalPrice;
    }

    public BigDecimal getRefundPrice() {
        return refundPrice;
    }

    public void setRefundPrice(BigDecimal refundPrice) {
        this.refundPrice = refundPrice;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getThumbnailsUrl() {
        return thumbnailsUrl;
    }

    public void setThumbnailsUrl(String thumbnailsUrl) {
        this.thumbnailsUrl = thumbnailsUrl;
    }

    public BigDecimal getCommisRate() {
        return commisRate;
    }

    public void setCommisRate(BigDecimal commisRate) {
        this.commisRate = commisRate;
    }

    public BigDecimal getEnabledRefundAmount() {
        return enabledRefundAmount;
    }

    public void setEnabledRefundAmount(BigDecimal enabledRefundAmount) {
        this.enabledRefundAmount = enabledRefundAmount;
    }

    public Boolean getLimitBuy() {
        return isLimitBuy;
    }

    public void setLimitBuy(Boolean isLimitBuy) {
        this.isLimitBuy = isLimitBuy;
    }

    public BigDecimal getCouponDiscount() {
        return couponDiscount;
    }

    public void setCouponDiscount(BigDecimal couponDiscount) {
        this.couponDiscount = couponDiscount;
    }

    public BigDecimal getFullDiscount() {
        return fullDiscount;
    }

    public void setFullDiscount(BigDecimal fullDiscount) {
        this.fullDiscount = fullDiscount;
    }

    public BigDecimal getMoneyOff() {
        return moneyOff;
    }

    public void setMoneyOff(BigDecimal moneyOff) {
        this.moneyOff = moneyOff;
    }

    public Long getActiveId() {
        return activeId;
    }

    public void setActiveId(Long activeId) {
        this.activeId = activeId;
    }

    public Long getFlashSaleId() {
        return flashSaleId;
    }

    public void setFlashSaleId(Long flashSaleId) {
        this.flashSaleId = flashSaleId;
    }

    public Long getSkuAutoId() {
        return skuAutoId;
    }

    public void setSkuAutoId(Long skuAutoId) {
        this.skuAutoId = skuAutoId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getApplyRefundQuantity() {
        return applyRefundQuantity;
    }

    public void setApplyRefundQuantity(Long applyRefundQuantity) {
        this.applyRefundQuantity = applyRefundQuantity;
    }

    @Override
    public String toString() {
        return "Item{" +
        "id=" + id +
        ", orderId=" + orderId +
        ", shopId=" + shopId +
        ", productId=" + productId +
        ", skuId=" + skuId +
        ", sku=" + sku +
        ", quantity=" + quantity +
        ", returnQuantity=" + returnQuantity +
        ", costPrice=" + costPrice +
        ", salePrice=" + salePrice +
        ", discountAmount=" + discountAmount +
        ", realTotalPrice=" + realTotalPrice +
        ", refundPrice=" + refundPrice +
        ", productName=" + productName +
        ", color=" + color +
        ", size=" + size +
        ", version=" + version +
        ", thumbnailsUrl=" + thumbnailsUrl +
        ", commisRate=" + commisRate +
        ", enabledRefundAmount=" + enabledRefundAmount +
                ", applyRefundQuantity=" + applyRefundQuantity +
        ", isLimitBuy=" + isLimitBuy +
        ", couponDiscount=" + couponDiscount +
        ", fullDiscount=" + fullDiscount +
        ", moneyOff=" + moneyOff +
        ", activeId=" + activeId +
        ", flashSaleId=" + flashSaleId +
        ", skuAutoId=" + skuAutoId +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
