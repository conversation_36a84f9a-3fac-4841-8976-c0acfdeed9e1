package com.sankuai.shangou.seashop.pay.dao.core.mapper;

import com.meituan.xframe.boot.mybatisplus.autoconfigure.mapper.EnhancedMapper;
import com.sankuai.shangou.seashop.pay.dao.core.domain.ChannelConfig;
import com.sankuai.shangou.seashop.pay.dao.core.model.AliPayConfigModel;
import com.sankuai.shangou.seashop.pay.dao.core.model.PayOpenStateModel;
import com.sankuai.shangou.seashop.pay.dao.core.model.WxPayConfigModel;

/**
 * <p>
 * 支付渠道配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-11-20
 */
public interface ChannelConfigMapper extends EnhancedMapper<ChannelConfig> {

    WxPayConfigModel getWxPayConfig();

    AliPayConfigModel getAliPayConfig();

    PayOpenStateModel getPayOpenState();

}
