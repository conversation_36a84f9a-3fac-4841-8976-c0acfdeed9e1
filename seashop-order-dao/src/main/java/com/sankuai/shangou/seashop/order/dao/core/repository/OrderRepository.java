package com.sankuai.shangou.seashop.order.dao.core.repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sankuai.shangou.seashop.order.common.enums.YesOrNoEnum;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.mapper.CustomOrderMapper;
import com.sankuai.shangou.seashop.order.dao.core.mapper.OrderMapper;
import com.sankuai.shangou.seashop.order.dao.core.mapper.ext.OrderExtMapper;
import com.sankuai.shangou.seashop.order.dao.core.model.CountFlashOrderAndProductModel;
import com.sankuai.shangou.seashop.order.dao.core.model.OrderRefundModel;
import com.sankuai.shangou.seashop.order.dao.core.model.OrderStatisticsModel;
import com.sankuai.shangou.seashop.order.dao.core.po.CommonOrderQueryParamBo;
import com.sankuai.shangou.seashop.order.dao.core.po.OrderStatisticsPo;
import com.sankuai.shangou.seashop.order.dao.core.po.UpdateReceiverPo;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Repository
@Slf4j
public class OrderRepository extends ServiceImpl<OrderMapper, Order> {

    @Resource
    private CustomOrderMapper customOrderMapper;
    @Resource
    private OrderExtMapper orderExtMapper;
    @Resource
    private OrderMapper orderMapper;

    /**
     * 日志记录使用
     */
    public Order selectById(String orderId) {
        log.info("selectById:{}", orderId);
        return this.getByOrderId(orderId);
    }

    public Order getByOrderId(String orderId) {
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        return baseMapper.selectOne(wrapper.eq(Order::getOrderId, orderId));
    }

    public Order getByOrderIdForceMaster(String orderId) {
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        return baseMapper.selectOne(wrapper.eq(Order::getOrderId, orderId));
    }

    public List<Order> getBySourceOrderId(String sourceOrderId) {
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        return baseMapper.selectList(wrapper.eq(Order::getSourceOrderId, sourceOrderId).orderByDesc(Order::getId));
    }

    public List<Order> getByOrderIdList(List<String> orderIdList) {
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Order::getOrderId, orderIdList);
        return baseMapper.selectList(wrapper);
    }


    public List<Order> getByOrderIdListForceMaster(List<String> orderIdList) {
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Order::getOrderId, orderIdList);
        return baseMapper.selectList(wrapper);
    }

    public List<Order> getBySourceOrderIdList(List<String> sourceOrderIdList) {
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Order::getSourceOrderId, sourceOrderIdList);
        return baseMapper.selectList(wrapper);
    }

    public int updateOrderClose(List<String> orderIdList, Integer fromStatus, Integer toStatus, String closeReason) {
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Order::getOrderId, orderIdList).eq(Order::getOrderStatus, fromStatus);
        Order order = new Order();
        order.setOrderStatus(toStatus);
        order.setLastModifyTime(new Date());
        order.setCloseReason(closeReason);
        return baseMapper.update(order, wrapper);
    }

    public int updateOrderStatus(List<String> orderIdList, Integer fromStatus, Integer toStatus) {
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Order::getOrderId, orderIdList).eq(Order::getOrderStatus, fromStatus);
        Order order = new Order();
        order.setOrderStatus(toStatus);
        order.setLastModifyTime(new Date());
        return baseMapper.update(order, wrapper);
    }

    public int updateOrderPaying(List<String> orderIdList, Integer fromStatus, Integer toStatus, BigDecimal settleFeeRate) {
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Order::getOrderId, orderIdList).eq(Order::getOrderStatus, fromStatus);
        Order order = new Order();
        order.setOrderStatus(toStatus);
        order.setSettlementCharge(settleFeeRate);
        order.setLastModifyTime(new Date());
        return baseMapper.update(order, wrapper);
    }

    public int updateOrderShipInfo(UpdateReceiverPo updateReceiverPo) {
        return customOrderMapper.updateOrderShipInfo(updateReceiverPo);
    }

    public int updateReceiveDelay(String orderId, Integer receiveDelay) {
        return customOrderMapper.updateReceiveDelay(orderId, receiveDelay);
    }

    public int updateOrderReceived(String orderId, Integer originStatus, Integer targetStatus, Date finishTIme, String closeReason) {
        return customOrderMapper.updateOrderReceived(orderId, originStatus, targetStatus, finishTIme, closeReason);
    }

    public int updateLastModifyTime(String orderId, Date lastModifyTime) {
        return customOrderMapper.updateLastModifyTime(orderId, lastModifyTime);
    }

    public List<OrderStatisticsModel> orderStatistics(OrderStatisticsPo param) {
        return customOrderMapper.orderStatistics(param);
    }

    public BigDecimal sumOrderAmount(OrderStatisticsPo param) {
        return customOrderMapper.sumOrderAmount(param);
    }

    public BigDecimal sumOrderAmountMember(OrderStatisticsPo param) {
        return customOrderMapper.sumOrderAmountMember(param);
    }

    public int countOrder(OrderStatisticsPo param) {
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        if (param.getShopId() != null) {
            wrapper.eq(Order::getShopId, param.getShopId());
        }
        if (null != param.getStartOrderDate() && null != param.getEndOrderDate()) {
            wrapper.between(Order::getOrderDate, param.getStartOrderDate(), param.getEndOrderDate());
        }
        if (null != param.getStartPayDate() && null != param.getEndPayDate()) {
            wrapper.between(Order::getPayDate, param.getStartPayDate(), param.getEndPayDate());
        }
        return Math.toIntExact(this.count(wrapper));
    }

    public int countNum(OrderRefundModel param) {
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Order::getUserId, param.getUserId());
        if(CollectionUtils.isNotEmpty(param.getInStatusList())){
            wrapper.in(Order::getOrderStatus, param.getInStatusList());
        }
        if(CollectionUtils.isNotEmpty(param.getNotInStatusList())){
            wrapper.notIn(Order::getOrderStatus, param.getNotInStatusList());
        }
        if(param.getHasCommented() != null){
            wrapper.eq(Order::getHasCommented, param.getHasCommented());
        }
        return Math.toIntExact(this.count(wrapper));
    }

    public Order queryLastOrderInfo(Long userId) {
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Order::getUserId, userId);
        wrapper.orderByDesc(Order::getCreateTime);
        wrapper.last("limit 1");
        return baseMapper.selectOne(wrapper);
    }


    public PageInfo<Order> queryOrderPage(Integer pageNo, Integer pageSize, Long shopId
            , String orderId, Integer status, Date startCreateTime, Date endCreateTime,
                                          Date startUpdateTime, Date endUpdateTime) {
        LambdaQueryWrapper<Order> wrapper = Wrappers.<Order>lambdaQuery()
                .eq(shopId != null, Order::getShopId, shopId)
                .eq(orderId != null, Order::getOrderId, orderId)
                .eq(status != null, Order::getOrderStatus, status);
                //.in(CollUtil.isNotEmpty(orderSources), Order::getOrderSource, orderSources);
        if (startCreateTime != null || endCreateTime != null) {
            wrapper.orderByDesc(Order::getCreateTime);
        } else if (startUpdateTime != null || endUpdateTime != null) {
            wrapper.orderByDesc(Order::getUpdateTime);
        } else {
            wrapper.orderByDesc(Order::getId);
        }
        return PageHelper.startPage(pageNo, pageSize).doSelectPageInfo(() -> baseMapper.selectList(wrapper));
    }

    public PageInfo<Order> getSoldTrades(Long shopId, Date startCreated, Date endCreated, String status, String buyerUname, Integer pageNo, Integer pageSize) {
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Order::getShopId, shopId)
                .eq(StringUtils.isNotBlank(status), Order::getOrderStatus, status)
                .eq(StringUtils.isNotBlank(buyerUname), Order::getShipTo, buyerUname)
                .ge(startCreated != null, Order::getCreateTime, startCreated)
                .le(endCreated != null, Order::getCreateTime, endCreated);
        return PageHelper.startPage(pageNo, pageSize).doSelectPageInfo(() -> baseMapper.selectList(wrapper));
    }
    public PageInfo<Order> getSoldTradesLastModifyTime(Long shopId, Date startCreated, Date endCreated, String status, String buyerUname, Integer pageNo, Integer pageSize) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getShopId, shopId)
                .eq(StringUtils.isNotBlank(status), Order::getOrderStatus, status)
                .eq(StringUtils.isNotBlank(buyerUname), Order::getShipTo, buyerUname)
                .ge(startCreated != null, Order::getLastModifyTime, startCreated)
                .le(endCreated != null, Order::getLastModifyTime, endCreated);
        return PageHelper.startPage(pageNo, pageSize).doSelectPageInfo(() -> baseMapper.selectList(queryWrapper));

    }

    public boolean updateSellerRemark(String orderId, String remark, Integer remarkFlag) {
        return lambdaUpdate().eq(Order::getOrderId, orderId)
                .set(Order::getSellerRemark, remark)
                .set(Order::getSellerRemarkFlag, remarkFlag)
                .update();
    }

    /**
     * 统计订单和商品数量
     *
     * @param shopId
     * @param status
     * @return
     */
    public CountFlashOrderAndProductModel countOrderAndProduct(Long shopId, Integer status) {
        Long countFlashProduct = orderExtMapper.countProduct(shopId, status);
        LambdaQueryWrapper<Order> wrapper = Wrappers.<Order>lambdaQuery()
                .eq(Order::getShopId, shopId)
                .eq(Order::getOrderStatus, status);
        long countFlashOrder = this.count(wrapper);
        CountFlashOrderAndProductModel dto = new CountFlashOrderAndProductModel();
        dto.setShopId(shopId);
        dto.setCountFlashOrder(countFlashOrder);
        dto.setCountFlashProduct(null != countFlashProduct ? countFlashProduct : 0);
        return dto;
    }


    /**
     * 通用的订单查询方法，主要是定时任务查询使用，其他业务慎用
     * <AUTHOR>
     * @param param 查询参数
     */
    public List<Order> getByCondition(CommonOrderQueryParamBo param) {
        LambdaQueryWrapper<Order> wrapper = Wrappers.<Order>lambdaQuery()
                .eq(param.getOrderStatusEq() != null, Order::getOrderStatus, param.getOrderStatusEq())
                .le(param.getOrderDateLe() != null, Order::getOrderDate, param.getOrderDateLe())
                .lt(param.getOrderDateLt() != null, Order::getOrderDate, param.getOrderDateLt())
                .ge(param.getOrderDateGe() != null, Order::getOrderDate, param.getOrderDateGe())
                .eq(param.getIsSendEq() != null, Order::getHasSend, param.getIsSendEq())
                .in(CollUtil.isNotEmpty(param.getOrderStatusIn()), Order::getOrderStatus, param.getOrderStatusIn())
                .lt(param.getShippingDateLt() != null, Order::getShippingDate, param.getShippingDateLt())
                .ge(param.getIdGe() != null, Order::getId, param.getIdGe())
                .le(param.getIdLe() != null, Order::getId, param.getIdLe())
                .ge(param.getUpdateTimeGe() != null, Order::getUpdateTime, param.getUpdateTimeGe())
                .le(param.getUpdateTimeLe() != null, Order::getUpdateTime, param.getUpdateTimeLe())
                .eq(param.getShopId() != null, Order::getShopId, param.getShopId());
        return baseMapper.selectList(wrapper);
    }

    /**
     * 更新订单是否给商家发送短信通知
     *
     * @param orderIdList 订单号列表
     */
    public boolean updateIsSendBatch(List<String> orderIdList) {
        Order order = new Order();
        order.setHasSend(true);
        LambdaUpdateWrapper<Order> wrapper = Wrappers.<Order>lambdaUpdate().in(Order::getOrderId, orderIdList);
        return this.update(order, wrapper);
    }

    public boolean updateByOrderId(String orderId, Order order) {
        LambdaUpdateWrapper<Order> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Order::getOrderId, orderId);
        return this.update(order, wrapper);
    }

    /**
     * 设置订单已评价
     * <AUTHOR>
     * @param orderId 订单号
     * @param yesOrNoEnum 是否已评价
     */
    public boolean updateOrderCommented(String orderId, YesOrNoEnum yesOrNoEnum) {
        LambdaUpdateWrapper<Order> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Order::getOrderId, orderId)
                .set(Order::getHasCommented, yesOrNoEnum.getCode());
        return this.update(wrapper);
    }

    public List<Order> getByUpdateTime(Date updateTime) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        if (updateTime != null) {
            queryWrapper.ge(Order::getUpdateTime, updateTime);
        }

        return baseMapper.selectList(queryWrapper);
    }

    public List<Long> queryPayOrderUserId(Date payDate, List<Long> targetUserIdList) {
        return orderMapper.queryPayOrderUserId(payDate, targetUserIdList);
    }
}
