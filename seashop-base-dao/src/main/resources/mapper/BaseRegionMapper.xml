<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.base.dao.core.mapper.BaseRegionMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegion">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="short_name" jdbcType="VARCHAR" property="shortName"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="region_level" jdbcType="INTEGER" property="regionLevel"/>
        <result column="left" jdbcType="INTEGER" property="left"/>
        <result column="right" jdbcType="INTEGER" property="right"/>
        <result column="custom" jdbcType="BIT" property="custom"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id
        , code, name, short_name, status, parent_id, region_level, `left`, `right`
    </sql>
    <select id="selectByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegionExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from base_region
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from base_region
        where id = #{id,jdbcType=BIGINT} and  `status`=0
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from base_region
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegionExample">
        delete from base_region
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegion">

        insert into base_region (id, code, name,
        short_name, status, parent_id,
        region_level, `left`, `right`,custom
        )
        values (#{id,jdbcType=BIGINT}, #{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
        #{shortName,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{parentId,jdbcType=BIGINT},
        #{regionLevel,jdbcType=INTEGER}, #{left,jdbcType=INTEGER}, #{right,jdbcType=INTEGER},#{custom,jdbcType=BIT}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegion">
        <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into base_region
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            <if test="code != null">
                code,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="shortName != null">
                short_name,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="regionLevel != null">
                region_level,
            </if>
            <if test="left != null">
                `left`,
            </if>
            <if test="right != null">
                `right`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id,jdbcType=BIGINT},
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="shortName != null">
                #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=BIGINT},
            </if>
            <if test="regionLevel != null">
                #{regionLevel,jdbcType=INTEGER},
            </if>
            <if test="left != null">
                #{left,jdbcType=INTEGER},
            </if>
            <if test="right != null">
                #{right,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegionExample"
            resultType="java.lang.Long">
        select count(*) from base_region
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update base_region
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.code != null">
                code = #{record.code,jdbcType=VARCHAR},
            </if>
            <if test="record.name != null">
                name = #{record.name,jdbcType=VARCHAR},
            </if>
            <if test="record.shortName != null">
                short_name = #{record.shortName,jdbcType=VARCHAR},
            </if>
            <if test="record.status != null">
                status = #{record.status,jdbcType=INTEGER},
            </if>
            <if test="record.parentId != null">
                parent_id = #{record.parentId,jdbcType=BIGINT},
            </if>
            <if test="record.regionLevel != null">
                region_level = #{record.regionLevel,jdbcType=INTEGER},
            </if>
            <if test="record.left != null">
                `left` = #{record.left,jdbcType=INTEGER},
            </if>
            <if test="record.right != null">
                `right` = #{record.right,jdbcType=INTEGER},
            </if>
            <if test="record.custom != null">
                `right` = #{record.custom,jdbcType=BIT},
            </if>
        </set>
        <if test="example != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update base_region
        set id = #{record.id,jdbcType=BIGINT},
        code = #{record.code,jdbcType=VARCHAR},
        name = #{record.name,jdbcType=VARCHAR},
        short_name = #{record.shortName,jdbcType=VARCHAR},
        status = #{record.status,jdbcType=INTEGER},
        parent_id = #{record.parentId,jdbcType=BIGINT},
        region_level = #{record.regionLevel,jdbcType=INTEGER},
        `left` = #{record.left,jdbcType=INTEGER},
        `right` = #{record.right,jdbcType=INTEGER}
        <if test="example != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>

    <update id="updateLeftAndRightByInsert" parameterType="map">
        update base_region
        set
        `left` = `left`+2,
        `right` = `right`+2
        <if test="example != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateParentLeftAndRightByInsert" parameterType="map">
        update base_region
        set
        `right` = `right`+2
        <if test="example != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>



    <update id="updateByPrimaryKeySelective"
            parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegion">
        update base_region
        <set>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="shortName != null">
                short_name = #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId,jdbcType=BIGINT},
            </if>
            <if test="regionLevel != null">
                region_level = #{regionLevel,jdbcType=INTEGER},
            </if>
            <if test="left != null">
                `left` = #{left,jdbcType=INTEGER},
            </if>
            <if test="right != null">
                `right` = #{right,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegion">
        update base_region
        set code         = #{code,jdbcType=VARCHAR},
            name         = #{name,jdbcType=VARCHAR},
            short_name   = #{shortName,jdbcType=VARCHAR},
            status       = #{status,jdbcType=INTEGER},
            parent_id    = #{parentId,jdbcType=BIGINT},
            region_level = #{regionLevel,jdbcType=INTEGER},
            `left`       = #{left,jdbcType=INTEGER},
            `right`      = #{right,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="getAllPathRegions" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from base_region
        where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and  `status`=0
    </select>

    <select id="getTownsNameByIds" parameterType="java.util.List" resultType="java.lang.String">
        select name from base_region where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and  `status`=0
    </select>
    <select id="getMaxRight"  resultType="java.lang.Long">
        select MAX(`right`) from base_region
    </select>
    <select id="getMaxId" resultType="java.lang.Long">
        select MAX(`id`)
        from base_region
    </select>
    <select id="selectCountSubRegionAsMap" resultType="map">
        select parent_id,count(1) as count
        from base_region
        where parent_id in
        <foreach collection="subIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and `status` = 0
        group by parent_id
    </select>

    <select id="queryProvinceCity" resultType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegionProvinceCity">
        SELECT
            r.id as input_id,
            r.name as input_name,
            r.region_level as input_level,
            CASE
                WHEN r.region_level = 1 THEN r.id
                WHEN r.region_level = 2 THEN r2.id
                WHEN r.region_level = 3 THEN r3.id
                WHEN r.region_level = 4 THEN r4.id
                END as province_id,
            CASE
                WHEN r.region_level = 1 THEN r.name
                WHEN r.region_level = 2 THEN r2.name
                WHEN r.region_level = 3 THEN r3.name
                WHEN r.region_level = 4 THEN r4.name
                END as province_name,
            CASE
                WHEN r.region_level &lt;= 1 THEN NULL
                WHEN r.region_level = 2 THEN r.id
                WHEN r.region_level = 3 THEN r2.id
                WHEN r.region_level = 4 THEN r3.id
                END as city_id,
            CASE
                WHEN r.region_level &lt;= 1 THEN NULL
                WHEN r.region_level = 2 THEN r.name
                WHEN r.region_level = 3 THEN r2.name
                WHEN r.region_level = 4 THEN r3.name
                END as city_name
        FROM base_region r
                 LEFT JOIN base_region r2 ON r.parent_id = r2.id
                 LEFT JOIN base_region r3 ON r2.parent_id = r3.id
                 LEFT JOIN base_region r4 ON r3.parent_id = r4.id
        WHERE r.id = #{id}
          AND r.status = 0
    </select>
</mapper>