package com.sankuai.shangou.seashop.base.dao.core.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegionProvinceCity;
import org.apache.ibatis.annotations.Param;

import com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegion;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegionExample;

public interface BaseRegionMapper extends BaseMapper<BaseRegion> {
    long countByExample(BaseRegionExample example);

    int deleteByExample(BaseRegionExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BaseRegion record);

    int insertSelective(BaseRegion record);

    List<BaseRegion> selectByExample(BaseRegionExample example);

    BaseRegion selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BaseRegion record, @Param("example") BaseRegionExample example);

    int updateByExample(@Param("record") BaseRegion record, @Param("example") BaseRegionExample example);

    int updateLeftAndRightByInsert(@Param("example") BaseRegionExample example);

    int updateParentLeftAndRightByInsert(@Param("example") BaseRegionExample example);

    int updateByPrimaryKeySelective(BaseRegion record);

    int updateByPrimaryKey(BaseRegion record);

    List<BaseRegion> getAllPathRegions(@Param("list") List<Integer> list);

    List<String> getTownsNameByIds(@Param("list") List<String> list);

    long getMaxRight();

    long getMaxId();

    BaseRegionProvinceCity queryProvinceCity(Long id);
}