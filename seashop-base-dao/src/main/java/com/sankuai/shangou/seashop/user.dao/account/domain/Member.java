package com.sankuai.shangou.seashop.user.dao.account.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 商家信息表
 * </p>
 *
 * <AUTHOR> @since 2024-01-24
 */
@TableName("user_member")
public class Member implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 名称
     */
    @TableField("user_name")
    private String userName;

    /**
     * 密码
     */
    @TableField("password")
    private String password;

    /**
     * 密码加盐
     */
    @TableField("password_salt")
    private String passwordSalt;

    /**
     * 昵称
     */
    @TableField("nick")
    private String nick;

    /**
     * 性别
     */
    @TableField("sex")
    private Integer sex;

    /**
     * 邮件
     */
    @TableField("email")
    private String email;

    /**
     * 省份id
     */
    @TableField("top_region_id")
    private Integer topRegionId;

    /**
     * 省市区id
     */
    @TableField("region_id")
    private Integer regionId;

    /**
     * 真实姓名
     */
    @TableField("real_name")
    private String realName;

    /**
     * 电话
     */
    @TableField("cell_phone")
    private String cellPhone;

    /**
     * qq(删除)
     */
    @TableField("qq")
    private String qq;

    /**
     * 街道地址(删除)
     */
    @TableField("address")
    private String address;

    /**
     * 是否禁用
     */
    @TableField("disabled")
    private Boolean disabled;

    /**
     * 最后登录日期
     */
    @TableField("last_login_date")
    private Date lastLoginDate;

    /**
     * 下单次数
     */
    @TableField("order_number")
    private Integer orderNumber;

    /**
     * 总消费金额（不排除退款）
     */
    @TableField("total_amount")
    private BigDecimal totalAmount;

    /**
     * 总消费金额（不排除退款）
     */
    @TableField("expenditure")
    private BigDecimal expenditure;

    /**
     * 积分
     */
    @TableField("points")
    private Integer points;

    /**
     * 头像
     */
    @TableField("photo")
    private String photo;

    /**
     * 商家父账号id
     */
    @TableField("parent_seller_id")
    private Long parentSellerId;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 支付密码
     */
    @TableField("pay_pwd")
    private String payPwd;

    /**
     * 支付密码加密字符
     */
    @TableField("pay_pwd_salt")
    private String payPwdSalt;

    /**
     * 邀请人
     */
    @TableField("invite_user_id")
    private Long inviteUserId;

    /**
     * 会员生日
     */
    @TableField("birth_day")
    private Date birthDay;

    /**
     * 职业
     */
    @TableField("occupation")
    private String occupation;

    /**
     * 净消费金额（排除退款）
     */
    @TableField("net_amount")
    private BigDecimal netAmount;

    /**
     * 最后消费时间
     */
    @TableField("last_consumption_time")
    private Date lastConsumptionTime;

    /**
     * 用户来源终端
     */
    @TableField("platform")
    private Integer platform;

    /**
     * 加密方式
     */
    @TableField("encryption_mode")
    private String encryptionMode;

    /**
     * 是否不提示入驻为供应商
     */
    @TableField("whether_notice_join")
    private Boolean whetherNoticeJoin;

    /**
     * 是否注销用户
     */
    @TableField("whether_log_out")
    private Boolean whetherLogOut;

    /**
     * 注销用户的时间
     */
    @TableField("log_out_time")
    private Date logOutTime;

    /**
     * 注册来源 0表示本系统，1表示牵牛花
     */
    @TableField("register_source")
    private Integer registerSource;

    /**
     * 微信openid
     */
    @TableField("open_id")
    private String openId;

    /**
     * 微信openid
     */
    @TableField("union_id")
    private String unionId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @TableField("province_id")
    private Long provinceId;

    /**
     * 微信公众号openid
     */
    @TableField("wxmp_open_id")
    private String wxmpOpenId;

    /**
     * 微信公众号unionid
     */
    @TableField("wxmp_union_id")
    private String wxmpUnionId;

    public Long getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(Long provinceId) {
        this.provinceId = provinceId;
    }

    public Date getFirstConsumptionTime() {
        return firstConsumptionTime;
    }

    public void setFirstConsumptionTime(Date firstConsumptionTime) {
        this.firstConsumptionTime = firstConsumptionTime;
    }

    @TableField("first_consumption_time")
    private Date firstConsumptionTime;
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPasswordSalt() {
        return passwordSalt;
    }

    public void setPasswordSalt(String passwordSalt) {
        this.passwordSalt = passwordSalt;
    }

    public String getNick() {
        return nick;
    }

    public void setNick(String nick) {
        this.nick = nick;
    }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getTopRegionId() {
        return topRegionId;
    }

    public void setTopRegionId(Integer topRegionId) {
        this.topRegionId = topRegionId;
    }

    public Integer getRegionId() {
        return regionId;
    }

    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getCellPhone() {
        return cellPhone;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }

    public String getQq() {
        return qq;
    }

    public void setQq(String qq) {
        this.qq = qq;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    public Date getLastLoginDate() {
        return lastLoginDate;
    }

    public void setLastLoginDate(Date lastLoginDate) {
        this.lastLoginDate = lastLoginDate;
    }

    public Integer getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(Integer orderNumber) {
        this.orderNumber = orderNumber;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getExpenditure() {
        return expenditure;
    }

    public void setExpenditure(BigDecimal expenditure) {
        this.expenditure = expenditure;
    }

    public Integer getPoints() {
        return points;
    }

    public void setPoints(Integer points) {
        this.points = points;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public Long getParentSellerId() {
        return parentSellerId;
    }

    public void setParentSellerId(Long parentSellerId) {
        this.parentSellerId = parentSellerId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPayPwd() {
        return payPwd;
    }

    public void setPayPwd(String payPwd) {
        this.payPwd = payPwd;
    }

    public String getPayPwdSalt() {
        return payPwdSalt;
    }

    public void setPayPwdSalt(String payPwdSalt) {
        this.payPwdSalt = payPwdSalt;
    }

    public Long getInviteUserId() {
        return inviteUserId;
    }

    public void setInviteUserId(Long inviteUserId) {
        this.inviteUserId = inviteUserId;
    }

    public Date getBirthDay() {
        return birthDay;
    }

    public void setBirthDay(Date birthDay) {
        this.birthDay = birthDay;
    }

    public String getOccupation() {
        return occupation;
    }

    public void setOccupation(String occupation) {
        this.occupation = occupation;
    }

    public BigDecimal getNetAmount() {
        return netAmount;
    }

    public void setNetAmount(BigDecimal netAmount) {
        this.netAmount = netAmount;
    }

    public Date getLastConsumptionTime() {
        return lastConsumptionTime;
    }

    public void setLastConsumptionTime(Date lastConsumptionTime) {
        this.lastConsumptionTime = lastConsumptionTime;
    }

    public Integer getPlatform() {
        return platform;
    }

    public void setPlatform(Integer platform) {
        this.platform = platform;
    }

    public String getEncryptionMode() {
        return encryptionMode;
    }

    public void setEncryptionMode(String encryptionMode) {
        this.encryptionMode = encryptionMode;
    }

    public Boolean getWhetherNoticeJoin() {
        return whetherNoticeJoin;
    }

    public void setWhetherNoticeJoin(Boolean whetherNoticeJoin) {
        this.whetherNoticeJoin = whetherNoticeJoin;
    }

    public Boolean getWhetherLogOut() {
        return whetherLogOut;
    }

    public void setWhetherLogOut(Boolean whetherLogOut) {
        this.whetherLogOut = whetherLogOut;
    }

    public Date getLogOutTime() {
        return logOutTime;
    }

    public void setLogOutTime(Date logOutTime) {
        this.logOutTime = logOutTime;
    }

    public Integer getRegisterSource() {
        return registerSource;
    }

    public void setRegisterSource(Integer registerSource) {
        this.registerSource = registerSource;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getWxmpOpenId() {
        return wxmpOpenId;
    }

    public void setWxmpOpenId(String wxmpOpenId) {
        this.wxmpOpenId = wxmpOpenId;
    }

    public String getWxmpUnionId() {
        return wxmpUnionId;
    }

    public void setWxmpUnionId(String wxmpUnionId) {
        this.wxmpUnionId = wxmpUnionId;
    }

    @Override
    public String toString() {
        return "Member{" +
        "id=" + id +
        ", userName=" + userName +
        ", password=" + password +
        ", passwordSalt=" + passwordSalt +
        ", nick=" + nick +
        ", sex=" + sex +
        ", email=" + email +
        ", topRegionId=" + topRegionId +
        ", regionId=" + regionId +
        ", realName=" + realName +
        ", cellPhone=" + cellPhone +
        ", qq=" + qq +
        ", address=" + address +
        ", disabled=" + disabled +
        ", lastLoginDate=" + lastLoginDate +
        ", orderNumber=" + orderNumber +
        ", totalAmount=" + totalAmount +
        ", expenditure=" + expenditure +
        ", points=" + points +
        ", photo=" + photo +
        ", parentSellerId=" + parentSellerId +
        ", remark=" + remark +
        ", payPwd=" + payPwd +
        ", payPwdSalt=" + payPwdSalt +
        ", inviteUserId=" + inviteUserId +
        ", birthDay=" + birthDay +
        ", occupation=" + occupation +
        ", netAmount=" + netAmount +
        ", lastConsumptionTime=" + lastConsumptionTime +
        ", platform=" + platform +
        ", encryptionMode=" + encryptionMode +
        ", whetherNoticeJoin=" + whetherNoticeJoin +
        ", whetherLogOut=" + whetherLogOut +
        ", logOutTime=" + logOutTime +
        ", registerSource=" + registerSource +
        ", openId=" + openId +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", wxmpOpenId=" + wxmpOpenId +
        ", wxmpUnionId=" + wxmpUnionId +
        "}";
    }
}
